<?xml version="1.0" encoding="utf-8"?>
<Rule Name="WebView2" DisplayName="WebView2" Order="75" PageTemplate="generic" xmlns="http://schemas.microsoft.com/build/2009/properties">

  <Rule.Categories>
    <Category Name="General" DisplayName="General" />
    <Category Name="wv2winrt" DisplayName="WinRT in Script" />
  </Rule.Categories>

  <Rule.DataSource>
    <DataSource Persistence="ProjectFile" HasConfigurationCondition="false" Label="Globals" />
  </Rule.DataSource>

  <BoolProperty Name="WebView2UseWinRT"
                DisplayName="Use WebView2 WinRT APIs"
                Description="Enables the WebView2 WinRT APIs for this project"
                Category="General" />

  <BoolProperty Name="WebView2EnableCsWinRTProjection"
                DisplayName="Use WebView2 C#/WinRT"
                Description="Enables the WebView2 C#/WinRT NET6.0 projection"
                Category="General" />

  <EnumProperty Name="WebView2LoaderPreference"
                DisplayName="Loader preference"
                Description="Switch between using a static library or a DLL for the WebView2Loader"
                Category="General">
    <EnumValue Name="Dynamic" DisplayName="Dynamic" Description="Sets the project to use a DLL for the WebView2Loader" />
    <EnumValue Name="Static" DisplayName="Static" Description="Sets the project to use a static library for the WebView2Loader" />
  </EnumProperty>

  <BoolProperty Name="WebView2UseDispatchAdapter"
                DisplayName="Use the wv2winrt tool"
                Description="Uses the wv2winrt tool that allows you to use WinRT objects with CoreWebView2.AddHostObjectToScript."
                Category="wv2winrt" />

  <StringProperty Name="WebView2DispatchAdapterOutputDir"
                  DisplayName="Output directory"
                  Description="Specifies the directory where source files generated by wv2winrt will be saved."
                  Category="wv2winrt" />

  <StringProperty Name="WebView2DispatchAdapterNamespace"
                  DisplayName="Output namespace"
                  Description="Namespace where types generated by wv2winrt will be placed."
                  Category="wv2winrt" />

  <BoolProperty Name="WebView2DispatchAdapterUseJavaScriptCase"
                DisplayName="Use JavaScript case"
                Description="Enables JavaScript casing in wv2winrt generated types"
                Category="wv2winrt" />

  <EnumProperty Name="WV2WinRTVerbosity"
                DisplayName="Verbosity"
                Description="Sets the importance of wv2winrt build messages"
                Category="wv2winrt">
    <EnumValue Name="low" DisplayName="low" Description="Enables messages when MSBuild verbosity is set to at least 'detailed'" />
    <EnumValue Name="normal" DisplayName="normal" Description="Enables messages when MSBuild verbosity is set to at least 'normal'" />
    <EnumValue Name="high" DisplayName="high" Description="Enables messages when MSBuild verbosity is set to at least 'minimal'" />
  </EnumProperty>

  <BoolProperty Name="WV2WinRTExplicitIncludesOnly"
                DisplayName="Explicit includes only"
                Description="Instead of implicitly including types referenced by included types, only types explicitly listed in an include will be included by wv2winrt."
                Category="wv2winrt" />

  <BoolProperty Name="WV2WinRTRequireAllowForWebAttribute"
                DisplayName="AllowForWeb attribute is required"
                Description="Only types with the 'AllowForWeb' attribute will be included by wv2winrt."
                Category="wv2winrt" />

  <BoolProperty Name="WV2WinRTIgnoreWebHostHiddenAttribute"
                DisplayName="WebHostHidden attribute is ignored"
                Description="Instead of excluding types with the 'WebHostHidden' attribute, ignore the attribute when deciding to include or exclude types."
                Category="wv2winrt" />

  <BoolProperty Name="WV2WinRTDisallowEmptyAdapter"
                DisplayName="Disallow empty adapter"
                Description="Enables build error when there are no winmd reference inputs to wv2winrt"
                Category="wv2winrt" />

  <BoolProperty Name="WV2WinRTWrapWebViewTypes"
                DisplayName="Wrap WebView2 types"
                Description="Includes WebView2 types in the types generated by wv2winrt"
                Category="wv2winrt" />

  <BoolProperty Name="WebView2WinRTWrapSystemTypes"
                DisplayName="Wrap system types"
                Description="Includes WinRT types from the Windows SDK in the types generated by wv2winrt"
                Category="wv2winrt" />

  <EnumProperty Name="WV2WinRTPlatformReferencesLevel"
                DisplayName="Platform references level"
                Description="Sets the level of platform references to take as input to wv2winrt. Changes to this property have the most impact for Desktop projects, where the default of matching C++/WinRT can have a dramatic impact on binary size. Setting this property to a value different to the default one may require manually adding references to WebView2WinRTAdditionalWinMDReferences."
                Category="wv2winrt">
    <EnumValue Name="explicit" DisplayName="Explicit references" Description="Resolves to platform references already resolved by the project. Behavior may vary depending on the project's target platform (UAP/Desktop)." />
    <EnumValue Name="foundation" DisplayName="Explicit references + Foundation contracts" Description="Resolves to explicit references + Foundation contracts from the SDK referenced by this project." />
    <EnumValue Name="match" DisplayName="Match C++/WinRT" Description="Resolves to the references set by C++/WinRT." />
  </EnumProperty>

  <StringListProperty Name="WebView2WinRTAdditionalWinMDReferences"
                      DisplayName="Additional WinMD references"
                      Description="List of paths to additional WinMD files to provide to wv2winrt"
                      Category="wv2winrt" />

  <StringListProperty Name="WebView2DispatchAdapterIncludeFilters"
                      DisplayName="Include filters"
                      Description="List of namespaces or runtimeclass full names used to match types to include from referenced WinMD files"
                      Category="wv2winrt" />

  <StringListProperty Name="WebView2DispatchAdapterExcludeFilters"
                      DisplayName="Exclude filters"
                      Description="List of namespaces or runtimeclass full names used to match types to exclude from referenced WinMD files"
                      Category="wv2winrt" />

</Rule>
