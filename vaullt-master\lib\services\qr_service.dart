import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:network_info_plus/network_info_plus.dart';

/// Service for generating QR codes with connection information
class QrService {
  /// Generate QR code data for device linking
  Future<String> generateConnectionQrData({
    required String sshPublicKey,
    required int vaultwardenPort,
    required String localDomainName,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // Get network information
      final networkInfo = NetworkInfo();
      final wifiIP = await networkInfo.getWifiIP();
      final wifiName = await networkInfo.getWifiName();
      
      // Create connection data
      final connectionData = {
        'version': '1.0',
        'service': 'vaultwarden',
        'domain': localDomainName,
        'ip': wifiIP ?? '127.0.0.1',
        'port': vaultwardenPort,
        'ssh_public_key': sshPublicKey,
        'network_name': wifiName?.replaceAll('"', '') ?? 'Unknown',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'expires_at': DateTime.now().add(const Duration(hours: 24)).millisecondsSinceEpoch,
        ...?additionalData,
      };
      
      // Convert to JSON string
      final jsonString = json.encode(connectionData);
      
      debugPrint('Generated QR data for connection to $localDomainName:$vaultwardenPort');
      
      return jsonString;
      
    } catch (e) {
      debugPrint('Error generating QR code data: $e');
      rethrow;
    }
  }
  
  /// Parse QR code data (for testing/validation)
  Map<String, dynamic>? parseConnectionQrData(String qrData) {
    try {
      final data = json.decode(qrData) as Map<String, dynamic>;
      
      // Validate required fields
      final requiredFields = ['version', 'service', 'domain', 'port', 'ssh_public_key'];
      for (final field in requiredFields) {
        if (!data.containsKey(field)) {
          debugPrint('Missing required field in QR data: $field');
          return null;
        }
      }
      
      // Check if data has expired
      if (data.containsKey('expires_at')) {
        final expiresAt = data['expires_at'] as int;
        if (DateTime.now().millisecondsSinceEpoch > expiresAt) {
          debugPrint('QR code data has expired');
          return null;
        }
      }
      
      // Validate service type
      if (data['service'] != 'vaultwarden') {
        debugPrint('Invalid service type in QR data: ${data['service']}');
        return null;
      }
      
      return data;
      
    } catch (e) {
      debugPrint('Error parsing QR code data: $e');
      return null;
    }
  }
  
  /// Validate SSH public key format
  bool isValidSshPublicKey(String publicKey) {
    try {
      final trimmed = publicKey.trim();
      
      // Check for common SSH key formats
      final validPrefixes = ['ssh-rsa', 'ssh-dss', 'ssh-ed25519', 'ecdsa-sha2-'];
      
      for (final prefix in validPrefixes) {
        if (trimmed.startsWith(prefix)) {
          final parts = trimmed.split(' ');
          if (parts.length >= 2) {
            // Basic validation: should have at least type and key data
            return parts[1].isNotEmpty;
          }
        }
      }
      
      return false;
    } catch (e) {
      return false;
    }
  }
  
  /// Generate a shareable connection summary
  String generateConnectionSummary({
    required String localDomainName,
    required int port,
    required String networkName,
  }) {
    return '''
VaAulLT Connection Details:
• Domain: $localDomainName
• Port: $port
• Network: $networkName
• Generated: ${DateTime.now().toString().split('.')[0]}

Scan the QR code with the VaAulLT mobile app to connect.
''';
  }
}
