{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\139.0.3405.119\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\139.0.3405.119\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "E2C7E5C5E5BEC777359D0BC3A82417DDFD9EF6273975D61E0200F3DBE0EB95E7"}, "default_search_provider_data": {"template_url_data": "057E243F7CF7A30529BA3D4E64833985DB09938E819F02533762BED0AC44A712"}, "edge": {"services": {"account_id": "046617931431BA950541670C4012EE238AE89467FE23C21EAD8A7DBF2BF27CEE", "last_username": "971FECD4253021B1ACEE0EE51BB92A4E8EE3F9C88FC4B371F801CB62365B021F"}}, "enterprise_signin": {"policy_recovery_token": "7C2763B1D2760D62478BAB9E1A71D4040A33A463DCEE65435A7CFC7F26456978"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "44D4CFCB5E00B3B30283B5550736800BC2DD58E5AC91D6ED24A6CAADC1C2FAA6", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "2B20D61CC63A0EF83DDEB959A06DBB92D0DEF63693E41B1BA9FC2131B81317DF"}, "ui": {"developer_mode": "C033C6D638720CAA2E8FED7914FEE4B03D9A97FDE2ED69DAE3CD32FF9008D59D"}}, "google": {"services": {"last_signed_in_username": "BCAF433A40C14BA5B86A7E718244D78544C8CE462DF40316C9073026FE647DA1"}}, "homepage": "F9CB0E9782F0ECA09720DBFA7D4266472C57D15474A4A3E65FF424A651319C09", "homepage_is_newtabpage": "BBD8284712E956F42518ADAC3F585AA3AFAEB29C269CF20D72A2B45474B94587", "media": {"cdm": {"origin_data": "DDF2305C06966419F563014B4C17960AD02255FC632354AC86396902FC36C8E2"}, "storage_id_salt": "F3D9D1B7433F73B40CDFAD955AE2B401B1C6653EB253E8D3AB44A01DA14B8E80"}, "pinned_tabs": "A9D46101C46FCE37E1031CE344318D7A87515C491625DAB5F78975ED9EF1F5AE", "prefs": {"preference_reset_time": "CAD477B762E6B9B5991C1FEE45099D415BC3480F0C3DD482813F344C31B07E5B"}, "safebrowsing": {"incidents_sent": "750F0C4CE85CED394FB659B0A3936499D8463CBC8EFA4A45DBE81F74F2581B12"}, "schedule_to_flush_to_disk": "557AC327B8BC3C92972057D3CB37EE80CB7010813DD9FD8FD9E5A16FD8082D2F", "search_provider_overrides": "1A29010ACC5585D7100841242507F1B912F6E5358EA5B4F719BDCCC45D794C5B", "session": {"restore_on_startup": "35AD51F192FCE6DEEC97ED4BAB5D26D711F9D7DD905646A55B9CD08187C4E99F", "startup_urls": "1F53A1F5B0B9F7F2C49D3DB5677ABA58474341483EE4E8450180D234FCF75F89"}}, "super_mac": "426F673AE44204C5F0CF0F259AB889E3A1A01A154A5943AF009C5B46775578CC"}}