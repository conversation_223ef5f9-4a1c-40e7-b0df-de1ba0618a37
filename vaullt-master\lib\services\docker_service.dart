import 'package:process_run/shell.dart';

/// Service class for Docker operations
class DockerService {
  final String dockerComposePath;
  final Shell _shell;

  DockerService({required this.dockerComposePath}) : _shell = Shell();

  /// Check if the Vaultwarden container is running
  Future<bool> isContainerRunning() async {
    try {
      final result = await _shell.run('docker-compose -f "$dockerComposePath" ps -q vaultwarden');
      
      if (result.isNotEmpty && result.first.stdout.toString().trim().isNotEmpty) {
        final containerId = result.first.stdout.toString().trim();
        final statusResult = await _shell.run('docker inspect --format="{{.State.Running}}" $containerId');
        return statusResult.first.stdout.toString().trim() == 'true';
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Start the Vaultwarden container
  Future<void> startContainer() async {
    await _shell.run('docker-compose -f "$dockerComposePath" up -d');
  }

  /// Stop the Vaultwarden container
  Future<void> stopContainer() async {
    await _shell.run('docker-compose -f "$dockerComposePath" down');
  }

  /// Restart the container (useful after configuration changes)
  Future<void> restartContainer() async {
    try {
      await stopContainer();
      // Small delay to ensure container is fully stopped
      await Future.delayed(const Duration(seconds: 2));
      await startContainer();
    } catch (e) {
      rethrow;
    }
  }

  /// Get container logs
  Future<String> getLogs({int lines = 100}) async {
    try {
      final result = await _shell.run('docker-compose -f "$dockerComposePath" logs --tail $lines vaultwarden');
      return result.first.stdout.toString();
    } catch (e) {
      return 'Error retrieving logs: $e';
    }
  }

  /// Check if Docker is installed and running
  Future<bool> isDockerAvailable() async {
    try {
      await _shell.run('docker --version');
      await _shell.run('docker-compose --version');
      return true;
    } catch (e) {
      return false;
    }
  }
}
