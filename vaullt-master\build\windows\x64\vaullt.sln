﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{29BF4A92-F4B8-385C-A1D5-56E47E8BE4D1}"
	ProjectSection(ProjectDependencies) = postProject
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4} = {84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}
		{AF645B42-6963-31E1-A2A0-23CFC750CB85} = {AF645B42-6963-31E1-A2A0-23CFC750CB85}
		{BD001A86-7018-33B4-A9CE-5737C8B0A7A1} = {BD001A86-7018-33B4-A9CE-5737C8B0A7A1}
		{A53C1CF4-A888-39DE-8BE5-817893E122A7} = {A53C1CF4-A888-39DE-8BE5-817893E122A7}
		{816C6DDD-2596-3472-8F72-EC94A66FFB38} = {816C6DDD-2596-3472-8F72-EC94A66FFB38}
		{C997C8F7-54A9-33FD-AF5A-E8B9F37753D4} = {C997C8F7-54A9-33FD-AF5A-E8B9F37753D4}
		{02897320-F5F0-3207-B78F-C306B81767FC} = {02897320-F5F0-3207-B78F-C306B81767FC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{DF15B384-8472-317F-97BC-C82894769883}"
	ProjectSection(ProjectDependencies) = postProject
		{29BF4A92-F4B8-385C-A1D5-56E47E8BE4D1} = {29BF4A92-F4B8-385C-A1D5-56E47E8BE4D1}
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4} = {84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "flutter\flutter_assemble.vcxproj", "{5F5769A9-DA6F-35E4-A178-0B7B4D552772}"
	ProjectSection(ProjectDependencies) = postProject
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4} = {84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_inappwebview_windows_DEPENDENCIES_DOWNLOAD", "plugins\flutter_inappwebview_windows\flutter_inappwebview_windows_DEPENDENCIES_DOWNLOAD.vcxproj", "{AF645B42-6963-31E1-A2A0-23CFC750CB85}"
	ProjectSection(ProjectDependencies) = postProject
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4} = {84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_inappwebview_windows_plugin", "plugins\flutter_inappwebview_windows\flutter_inappwebview_windows_plugin.vcxproj", "{BD001A86-7018-33B4-A9CE-5737C8B0A7A1}"
	ProjectSection(ProjectDependencies) = postProject
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4} = {84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}
		{5F5769A9-DA6F-35E4-A178-0B7B4D552772} = {5F5769A9-DA6F-35E4-A178-0B7B4D552772}
		{816C6DDD-2596-3472-8F72-EC94A66FFB38} = {816C6DDD-2596-3472-8F72-EC94A66FFB38}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_app", "flutter\flutter_wrapper_app.vcxproj", "{A53C1CF4-A888-39DE-8BE5-817893E122A7}"
	ProjectSection(ProjectDependencies) = postProject
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4} = {84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}
		{5F5769A9-DA6F-35E4-A178-0B7B4D552772} = {5F5769A9-DA6F-35E4-A178-0B7B4D552772}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "flutter\flutter_wrapper_plugin.vcxproj", "{816C6DDD-2596-3472-8F72-EC94A66FFB38}"
	ProjectSection(ProjectDependencies) = postProject
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4} = {84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}
		{5F5769A9-DA6F-35E4-A178-0B7B4D552772} = {5F5769A9-DA6F-35E4-A178-0B7B4D552772}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "nsd_windows_plugin", "plugins\nsd_windows\nsd_windows_plugin.vcxproj", "{C997C8F7-54A9-33FD-AF5A-E8B9F37753D4}"
	ProjectSection(ProjectDependencies) = postProject
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4} = {84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}
		{5F5769A9-DA6F-35E4-A178-0B7B4D552772} = {5F5769A9-DA6F-35E4-A178-0B7B4D552772}
		{816C6DDD-2596-3472-8F72-EC94A66FFB38} = {816C6DDD-2596-3472-8F72-EC94A66FFB38}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "vaullt", "runner\vaullt.vcxproj", "{02897320-F5F0-3207-B78F-C306B81767FC}"
	ProjectSection(ProjectDependencies) = postProject
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4} = {84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}
		{5F5769A9-DA6F-35E4-A178-0B7B4D552772} = {5F5769A9-DA6F-35E4-A178-0B7B4D552772}
		{BD001A86-7018-33B4-A9CE-5737C8B0A7A1} = {BD001A86-7018-33B4-A9CE-5737C8B0A7A1}
		{A53C1CF4-A888-39DE-8BE5-817893E122A7} = {A53C1CF4-A888-39DE-8BE5-817893E122A7}
		{C997C8F7-54A9-33FD-AF5A-E8B9F37753D4} = {C997C8F7-54A9-33FD-AF5A-E8B9F37753D4}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{29BF4A92-F4B8-385C-A1D5-56E47E8BE4D1}.Debug|x64.ActiveCfg = Debug|x64
		{29BF4A92-F4B8-385C-A1D5-56E47E8BE4D1}.Debug|x64.Build.0 = Debug|x64
		{29BF4A92-F4B8-385C-A1D5-56E47E8BE4D1}.Profile|x64.ActiveCfg = Profile|x64
		{29BF4A92-F4B8-385C-A1D5-56E47E8BE4D1}.Profile|x64.Build.0 = Profile|x64
		{29BF4A92-F4B8-385C-A1D5-56E47E8BE4D1}.Release|x64.ActiveCfg = Release|x64
		{29BF4A92-F4B8-385C-A1D5-56E47E8BE4D1}.Release|x64.Build.0 = Release|x64
		{DF15B384-8472-317F-97BC-C82894769883}.Debug|x64.ActiveCfg = Debug|x64
		{DF15B384-8472-317F-97BC-C82894769883}.Debug|x64.Build.0 = Debug|x64
		{DF15B384-8472-317F-97BC-C82894769883}.Profile|x64.ActiveCfg = Profile|x64
		{DF15B384-8472-317F-97BC-C82894769883}.Profile|x64.Build.0 = Profile|x64
		{DF15B384-8472-317F-97BC-C82894769883}.Release|x64.ActiveCfg = Release|x64
		{DF15B384-8472-317F-97BC-C82894769883}.Release|x64.Build.0 = Release|x64
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}.Debug|x64.ActiveCfg = Debug|x64
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}.Debug|x64.Build.0 = Debug|x64
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}.Profile|x64.ActiveCfg = Profile|x64
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}.Profile|x64.Build.0 = Profile|x64
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}.Release|x64.ActiveCfg = Release|x64
		{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}.Release|x64.Build.0 = Release|x64
		{5F5769A9-DA6F-35E4-A178-0B7B4D552772}.Debug|x64.ActiveCfg = Debug|x64
		{5F5769A9-DA6F-35E4-A178-0B7B4D552772}.Debug|x64.Build.0 = Debug|x64
		{5F5769A9-DA6F-35E4-A178-0B7B4D552772}.Profile|x64.ActiveCfg = Profile|x64
		{5F5769A9-DA6F-35E4-A178-0B7B4D552772}.Profile|x64.Build.0 = Profile|x64
		{5F5769A9-DA6F-35E4-A178-0B7B4D552772}.Release|x64.ActiveCfg = Release|x64
		{5F5769A9-DA6F-35E4-A178-0B7B4D552772}.Release|x64.Build.0 = Release|x64
		{AF645B42-6963-31E1-A2A0-23CFC750CB85}.Debug|x64.ActiveCfg = Debug|x64
		{AF645B42-6963-31E1-A2A0-23CFC750CB85}.Debug|x64.Build.0 = Debug|x64
		{AF645B42-6963-31E1-A2A0-23CFC750CB85}.Profile|x64.ActiveCfg = Profile|x64
		{AF645B42-6963-31E1-A2A0-23CFC750CB85}.Profile|x64.Build.0 = Profile|x64
		{AF645B42-6963-31E1-A2A0-23CFC750CB85}.Release|x64.ActiveCfg = Release|x64
		{AF645B42-6963-31E1-A2A0-23CFC750CB85}.Release|x64.Build.0 = Release|x64
		{BD001A86-7018-33B4-A9CE-5737C8B0A7A1}.Debug|x64.ActiveCfg = Debug|x64
		{BD001A86-7018-33B4-A9CE-5737C8B0A7A1}.Debug|x64.Build.0 = Debug|x64
		{BD001A86-7018-33B4-A9CE-5737C8B0A7A1}.Profile|x64.ActiveCfg = Profile|x64
		{BD001A86-7018-33B4-A9CE-5737C8B0A7A1}.Profile|x64.Build.0 = Profile|x64
		{BD001A86-7018-33B4-A9CE-5737C8B0A7A1}.Release|x64.ActiveCfg = Release|x64
		{BD001A86-7018-33B4-A9CE-5737C8B0A7A1}.Release|x64.Build.0 = Release|x64
		{A53C1CF4-A888-39DE-8BE5-817893E122A7}.Debug|x64.ActiveCfg = Debug|x64
		{A53C1CF4-A888-39DE-8BE5-817893E122A7}.Debug|x64.Build.0 = Debug|x64
		{A53C1CF4-A888-39DE-8BE5-817893E122A7}.Profile|x64.ActiveCfg = Profile|x64
		{A53C1CF4-A888-39DE-8BE5-817893E122A7}.Profile|x64.Build.0 = Profile|x64
		{A53C1CF4-A888-39DE-8BE5-817893E122A7}.Release|x64.ActiveCfg = Release|x64
		{A53C1CF4-A888-39DE-8BE5-817893E122A7}.Release|x64.Build.0 = Release|x64
		{816C6DDD-2596-3472-8F72-EC94A66FFB38}.Debug|x64.ActiveCfg = Debug|x64
		{816C6DDD-2596-3472-8F72-EC94A66FFB38}.Debug|x64.Build.0 = Debug|x64
		{816C6DDD-2596-3472-8F72-EC94A66FFB38}.Profile|x64.ActiveCfg = Profile|x64
		{816C6DDD-2596-3472-8F72-EC94A66FFB38}.Profile|x64.Build.0 = Profile|x64
		{816C6DDD-2596-3472-8F72-EC94A66FFB38}.Release|x64.ActiveCfg = Release|x64
		{816C6DDD-2596-3472-8F72-EC94A66FFB38}.Release|x64.Build.0 = Release|x64
		{C997C8F7-54A9-33FD-AF5A-E8B9F37753D4}.Debug|x64.ActiveCfg = Debug|x64
		{C997C8F7-54A9-33FD-AF5A-E8B9F37753D4}.Debug|x64.Build.0 = Debug|x64
		{C997C8F7-54A9-33FD-AF5A-E8B9F37753D4}.Profile|x64.ActiveCfg = Profile|x64
		{C997C8F7-54A9-33FD-AF5A-E8B9F37753D4}.Profile|x64.Build.0 = Profile|x64
		{C997C8F7-54A9-33FD-AF5A-E8B9F37753D4}.Release|x64.ActiveCfg = Release|x64
		{C997C8F7-54A9-33FD-AF5A-E8B9F37753D4}.Release|x64.Build.0 = Release|x64
		{02897320-F5F0-3207-B78F-C306B81767FC}.Debug|x64.ActiveCfg = Debug|x64
		{02897320-F5F0-3207-B78F-C306B81767FC}.Debug|x64.Build.0 = Debug|x64
		{02897320-F5F0-3207-B78F-C306B81767FC}.Profile|x64.ActiveCfg = Profile|x64
		{02897320-F5F0-3207-B78F-C306B81767FC}.Profile|x64.Build.0 = Profile|x64
		{02897320-F5F0-3207-B78F-C306B81767FC}.Release|x64.ActiveCfg = Release|x64
		{02897320-F5F0-3207-B78F-C306B81767FC}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {93A891DE-0EAF-3948-9C41-65629228ADF2}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
