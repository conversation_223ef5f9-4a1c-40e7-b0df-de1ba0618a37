﻿<?xml version="1.0"?>
<Project ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- This build/.targets file is used by managed VS projects including win32
    apps and UWP apps. -->

  <PropertyGroup>
    <WebView2ProjectKind>managed</WebView2ProjectKind>
  </PropertyGroup>

  <PropertyGroup>
    <!-- The managed targets file is under build
      so the root is one path segment up. -->
    <NugetRoot>$(MSBuildThisFileDirectory)..\</NugetRoot>
  </PropertyGroup>

  <!-- Example logging
  <Target Name="WebView2ManagedEntryLog" BeforeTargets="Build">
    <Message Text="WebView2 managed .targets file. $(NugetRoot)" Importance="high"/>
  </Target>
  -->

  <Import Project="$(NugetRoot)\build\Common.targets"/>
</Project>
