﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{BD001A86-7018-33B4-A9CE-5737C8B0A7A1}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>flutter_inappwebview_windows_plugin</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Documents\VaAulLT\vaullt-master\build\windows\x64\plugins\flutter_inappwebview_windows\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">flutter_inappwebview_windows_plugin.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">flutter_inappwebview_windows_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\Documents\VaAulLT\vaullt-master\build\windows\x64\plugins\flutter_inappwebview_windows\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">flutter_inappwebview_windows_plugin.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">flutter_inappwebview_windows_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Documents\VaAulLT\vaullt-master\build\windows\x64\plugins\flutter_inappwebview_windows\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">flutter_inappwebview_windows_plugin.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">flutter_inappwebview_windows_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral;C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;HAVE_FLUTTER_D3D_TEXTURE;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR="Debug";flutter_inappwebview_windows_plugin_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;HAVE_FLUTTER_D3D_TEXTURE;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\";flutter_inappwebview_windows_plugin_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral;C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral;C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\flutter\Debug\flutter_wrapper_plugin.lib;C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/plugins/flutter_inappwebview_windows/Debug/flutter_inappwebview_windows_plugin.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/plugins/flutter_inappwebview_windows/Debug/flutter_inappwebview_windows_plugin.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral;C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;HAVE_FLUTTER_D3D_TEXTURE;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR="Profile";flutter_inappwebview_windows_plugin_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;HAVE_FLUTTER_D3D_TEXTURE;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR=\"Profile\";flutter_inappwebview_windows_plugin_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral;C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral;C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\flutter\Profile\flutter_wrapper_plugin.lib;C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/plugins/flutter_inappwebview_windows/Profile/flutter_inappwebview_windows_plugin.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/plugins/flutter_inappwebview_windows/Profile/flutter_inappwebview_windows_plugin.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral;C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;HAVE_FLUTTER_D3D_TEXTURE;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR="Release";flutter_inappwebview_windows_plugin_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;HAVE_FLUTTER_D3D_TEXTURE;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\";flutter_inappwebview_windows_plugin_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral;C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral;C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\flutter\Release\flutter_wrapper_plugin.lib;C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/plugins/flutter_inappwebview_windows/Release/flutter_inappwebview_windows_plugin.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/plugins/flutter_inappwebview_windows/Release/flutter_inappwebview_windows_plugin.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Documents/VaAulLT/vaullt-master/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/VaAulLT/vaullt-master/windows -BC:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64 --check-stamp-file C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/plugins/flutter_inappwebview_windows/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Documents\VaAulLT\vaullt-master\build\windows\x64\plugins\flutter_inappwebview_windows\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule C:/Users/<USER>/Documents/VaAulLT/vaullt-master/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/VaAulLT/vaullt-master/windows -BC:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64 --check-stamp-file C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/plugins/flutter_inappwebview_windows/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\Documents\VaAulLT\vaullt-master\build\windows\x64\plugins\flutter_inappwebview_windows\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Documents/VaAulLT/vaullt-master/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/VaAulLT/vaullt-master/windows -BC:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64 --check-stamp-file C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/plugins/flutter_inappwebview_windows/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Documents\VaAulLT\vaullt-master\build\windows\x64\plugins\flutter_inappwebview_windows\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\include\flutter_inappwebview_windows\flutter_inappwebview_windows_plugin_c_api.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\flutter_inappwebview_windows_plugin_c_api.cpp" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\flutter_inappwebview_windows_plugin.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\flutter_inappwebview_windows_plugin.h" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\utils\log.h" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\utils\strconv.h" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\utils\map.h" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\utils\vector.h" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\utils\string.h" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\utils\util.h" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\utils\flutter.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\utils\base64.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\utils\base64.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\channel_delegate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\channel_delegate.h" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\base_callback_result.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\url_request.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\url_request.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\navigation_action.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\navigation_action.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\web_resource_error.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\web_resource_error.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\web_resource_request.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\web_resource_request.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\web_resource_response.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\web_resource_response.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\web_history.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\web_history.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\web_history_item.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\web_history_item.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\content_world.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\content_world.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\user_script.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\user_script.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\plugin_script.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\plugin_script.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\size_2d.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\size_2d.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\rect.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\rect.h" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\callbacks_complete.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\screenshot_configuration.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\screenshot_configuration.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\create_window_action.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\create_window_action.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\new_window_requested_args.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\new_window_requested_args.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\window_features.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\window_features.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\ssl_certificate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\ssl_certificate.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\permission_response.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\permission_response.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\custom_scheme_response.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\custom_scheme_response.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\custom_scheme_registration.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\types\custom_scheme_registration.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\custom_platform_view.cc" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\custom_platform_view.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\texture_bridge.cc" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\texture_bridge.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\graphics_context.cc" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\graphics_context.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\util\direct3d11.interop.cc" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\util\direct3d11.interop.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\util\rohelper.cc" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\util\rohelper.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\util\string_converter.cc" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\util\string_converter.h" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\util\swizzle.h" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\plugin_scripts_js\plugin_scripts_util.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\plugin_scripts_js\javascript_bridge_js.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\plugin_scripts_js\javascript_bridge_js.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\webview_environment\webview_environment_settings.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\webview_environment\webview_environment_settings.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\webview_environment\webview_environment.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\webview_environment\webview_environment.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\webview_environment\webview_environment_manager.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\webview_environment\webview_environment_manager.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\webview_environment\webview_environment_channel_delegate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\webview_environment\webview_environment_channel_delegate.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_webview\user_content_controller.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_webview\user_content_controller.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_webview\in_app_webview_settings.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_webview\in_app_webview_settings.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_webview\in_app_webview.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_webview\in_app_webview.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_webview\in_app_webview_manager.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_webview\in_app_webview_manager.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_webview\webview_channel_delegate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_webview\webview_channel_delegate.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\headless_in_app_webview\headless_in_app_webview.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\headless_in_app_webview\headless_in_app_webview.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\headless_in_app_webview\headless_in_app_webview_manager.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\headless_in_app_webview\headless_in_app_webview_manager.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\headless_in_app_webview\headless_webview_channel_delegate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\headless_in_app_webview\headless_webview_channel_delegate.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_browser\in_app_browser_settings.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_browser\in_app_browser_settings.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_browser\in_app_browser_manager.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_browser\in_app_browser_manager.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_browser\in_app_browser.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_browser\in_app_browser.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_browser\in_app_browser_channel_delegate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\in_app_browser\in_app_browser_channel_delegate.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\cookie_manager.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\cookie_manager.h" />
    <ClCompile Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\texture_bridge_gpu.cc" />
    <ClInclude Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral\.plugin_symlinks\flutter_inappwebview_windows\windows\custom_platform_view\texture_bridge_gpu.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{84BF418A-9A1F-3019-BEB8-10BDCE9DE8A4}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{5F5769A9-DA6F-35E4-A178-0B7B4D552772}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Documents\VaAulLT\vaullt-master\build\windows\x64\flutter\flutter_wrapper_plugin.vcxproj">
      <Project>{816C6DDD-2596-3472-8F72-EC94A66FFB38}</Project>
      <Name>flutter_wrapper_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="..\..\packages\Microsoft.Web.WebView2\build\native\Microsoft.Web.WebView2.targets" Condition="Exists('..\..\packages\Microsoft.Web.WebView2\build\native\Microsoft.Web.WebView2.targets') And ('$(Configuration)'=='Debug' Or '$(Configuration)'=='Profile' Or '$(Configuration)'=='Release')" />
    <Import Project="..\..\packages\Microsoft.Windows.ImplementationLibrary\build\native\Microsoft.Windows.ImplementationLibrary.targets" Condition="Exists('..\..\packages\Microsoft.Windows.ImplementationLibrary\build\native\Microsoft.Windows.ImplementationLibrary.targets') And ('$(Configuration)'=='Debug' Or '$(Configuration)'=='Profile' Or '$(Configuration)'=='Release')" />
    <Import Project="..\..\packages\nlohmann.json\build\native\nlohmann.json.targets" Condition="Exists('..\..\packages\nlohmann.json\build\native\nlohmann.json.targets') And ('$(Configuration)'=='Debug' Or '$(Configuration)'=='Profile' Or '$(Configuration)'=='Release')" />
  </ImportGroup>
</Project>