services:
  vaultwarden:
    image: vaultwarden/server:latest
    container_name: vaultwarden
    restart: always
    environment:
      # DOMAIN: "https://vaultwarden.example.com"  # required when using a reverse proxy; your domain; vaultwarden needs to know it's https to work properly with attachments
      SIGNUPS_ALLOWED: "true" # Deactivate this with "false" after you have created your account so that no strangers can register
    volumes:
      - C:/Users/<USER>/Downloads/VAulLT:/data # the path before the : can be changed
    ports:
      - "127.0.0.1:11001:80" # you can replace the port with your preferred port
