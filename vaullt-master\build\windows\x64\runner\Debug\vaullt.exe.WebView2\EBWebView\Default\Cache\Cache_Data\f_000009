{"allApplications": {"message": "All applications"}, "appLogoLabel": {"message": "Bitwarden logo"}, "criticalApplications": {"message": "Critical applications"}, "noCriticalAppsAtRisk": {"message": "No critical applications at risk"}, "accessIntelligence": {"message": "Access Intelligence"}, "riskInsights": {"message": "Risk Insights"}, "passwordRisk": {"message": "Password Risk"}, "reviewAtRiskPasswords": {"message": "Review at-risk passwords (weak, exposed, or reused) across applications. Select your most critical applications to prioritize security actions for your users to address at-risk passwords."}, "dataLastUpdated": {"message": "Data last updated: $DATE$", "placeholders": {"date": {"content": "$1", "example": "2021-01-01"}}}, "notifiedMembers": {"message": "Notified members"}, "revokeMembers": {"message": "Revoke members"}, "restoreMembers": {"message": "Restore members"}, "cannotRestoreAccessError": {"message": "Cannot restore organization access"}, "allApplicationsWithCount": {"message": "All applications ($COUNT$)", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "createNewLoginItem": {"message": "Create new login item"}, "criticalApplicationsWithCount": {"message": "Critical applications ($COUNT$)", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "notifiedMembersWithCount": {"message": "Notified members ($COUNT$)", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "noAppsInOrgTitle": {"message": "No applications found in $ORG NAME$", "placeholders": {"org name": {"content": "$1", "example": "Company Name"}}}, "noAppsInOrgDescription": {"message": "As users save logins, applications appear here, showing any at-risk passwords. Mark critical apps and notify users to update passwords."}, "noCriticalAppsTitle": {"message": "You haven't marked any applications as a Critical"}, "noCriticalAppsDescription": {"message": "Select your most critical applications to discover at-risk passwords, and notify users to change those passwords."}, "markCriticalApps": {"message": "Mark critical apps"}, "markAppAsCritical": {"message": "Mark app as critical"}, "applicationsMarkedAsCriticalSuccess": {"message": "Applications marked as critical"}, "application": {"message": "Application"}, "atRiskPasswords": {"message": "At-risk passwords"}, "requestPasswordChange": {"message": "Request password change"}, "totalPasswords": {"message": "Total passwords"}, "searchApps": {"message": "Search applications"}, "atRiskMembers": {"message": "At-risk members"}, "atRiskMembersWithCount": {"message": "At-risk members ($COUNT$)", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "atRiskApplicationsWithCount": {"message": "At-risk applications ($COUNT$)", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "atRiskMembersDescription": {"message": "These members are logging into applications with weak, exposed, or reused passwords."}, "atRiskMembersDescriptionNone": {"message": "These are no members logging into applications with weak, exposed, or reused passwords."}, "atRiskApplicationsDescription": {"message": "These applications have weak, exposed, or reused passwords."}, "atRiskApplicationsDescriptionNone": {"message": "These are no applications with weak, exposed, or reused passwords."}, "atRiskMembersDescriptionWithApp": {"message": "These members are logging into $APPNAME$ with weak, exposed, or reused passwords.", "placeholders": {"appname": {"content": "$1", "example": "Salesforce"}}}, "atRiskMembersDescriptionWithAppNone": {"message": "There are no at risk members for $APPNAME$.", "placeholders": {"appname": {"content": "$1", "example": "Salesforce"}}}, "totalMembers": {"message": "Total members"}, "atRiskApplications": {"message": "At-risk applications"}, "totalApplications": {"message": "Total applications"}, "unmarkAsCriticalApp": {"message": "Unmark as critical app"}, "criticalApplicationSuccessfullyUnmarked": {"message": "Critical application successfully unmarked"}, "whatTypeOfItem": {"message": "What type of item is this?"}, "name": {"message": "Name"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "New URI"}, "username": {"message": "Username"}, "password": {"message": "Password"}, "newPassword": {"message": "New password"}, "passphrase": {"message": "Passphrase"}, "notes": {"message": "Notes"}, "privateNote": {"message": "Private note"}, "note": {"message": "Note"}, "customFields": {"message": "Custom fields"}, "cardholderName": {"message": "Cardholder name"}, "loginCredentials": {"message": "Login credentials"}, "personalDetails": {"message": "Personal details"}, "identification": {"message": "Identification"}, "contactInfo": {"message": "Contact info"}, "cardDetails": {"message": "Card details"}, "cardBrandDetails": {"message": "$BRAND$ details", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "itemHistory": {"message": "Item history"}, "authenticatorKey": {"message": "Authenticator key"}, "autofillOptions": {"message": "Autofill options"}, "websiteUri": {"message": "Website (URI)"}, "websiteUriCount": {"message": "Website (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Website added"}, "addWebsite": {"message": "Add website"}, "deleteWebsite": {"message": "Delete website"}, "defaultLabel": {"message": "Default ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Show match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Hide match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Autofill on page load?"}, "number": {"message": "Number"}, "brand": {"message": "Brand"}, "expiration": {"message": "Expiration"}, "securityCode": {"message": "Security code (CVV)"}, "securityCodeSlashCVV": {"message": "Security code / CVV"}, "identityName": {"message": "Identity name"}, "company": {"message": "Company"}, "ssn": {"message": "Social Security number"}, "passportNumber": {"message": "Passport number"}, "licenseNumber": {"message": "License number"}, "email": {"message": "Email"}, "phone": {"message": "Phone"}, "january": {"message": "January"}, "february": {"message": "February"}, "march": {"message": "March"}, "april": {"message": "April"}, "may": {"message": "May"}, "june": {"message": "June"}, "july": {"message": "July"}, "august": {"message": "August"}, "september": {"message": "September"}, "october": {"message": "October"}, "november": {"message": "November"}, "december": {"message": "December"}, "title": {"message": "Title"}, "mr": {"message": "Mr"}, "mrs": {"message": "Mrs"}, "ms": {"message": "Ms"}, "mx": {"message": "Mx"}, "dr": {"message": "Dr"}, "cardExpiredTitle": {"message": "Expired card"}, "cardExpiredMessage": {"message": "If you've renewed it, update the card's information"}, "expirationMonth": {"message": "Expiration month"}, "expirationYear": {"message": "Expiration year"}, "authenticatorKeyTotp": {"message": "Authenticator key (TOTP)"}, "totpHelperTitle": {"message": "Make 2-step verification seamless"}, "totpHelper": {"message": "Bitwarden can store and fill 2-step verification codes. Copy and paste the key into this field."}, "totpHelperWithCapture": {"message": "Bitwarden can store and fill 2-step verification codes. Select the camera icon to take a screenshot of this website's authenticator QR code, or copy and paste the key into this field."}, "learnMoreAboutAuthenticators": {"message": "Learn more about authenticators"}, "folder": {"message": "Folder"}, "value": {"message": "Value"}, "cfTypeText": {"message": "Text"}, "cfTypeHidden": {"message": "Hidden"}, "cfTypeBoolean": {"message": "Boolean"}, "cfTypeCheckbox": {"message": "Checkbox"}, "cfTypeLinked": {"message": "Linked", "description": "This describes a field that is 'linked' (related) to another field."}, "fieldType": {"message": "Field type"}, "fieldLabel": {"message": "Field label"}, "remove": {"message": "Remove"}, "unassigned": {"message": "Unassigned"}, "noneFolder": {"message": "No folder", "description": "This is the folder for uncategorized items"}, "selfOwnershipLabel": {"message": "You", "description": "Used as a label to indicate that the user is the owner of an item."}, "addFolder": {"message": "Add folder"}, "editFolder": {"message": "Edit folder"}, "editWithName": {"message": "Edit $ITEM$: $NAME$", "placeholders": {"item": {"content": "$1", "example": "login"}, "name": {"content": "$2", "example": "Social"}}}, "newFolder": {"message": "New folder"}, "folderName": {"message": "Folder name"}, "folderHintText": {"message": "Nest a folder by adding the parent folder's name followed by a “/”. Example: Social/Forums"}, "deleteFolderPermanently": {"message": "Are you sure you want to permanently delete this folder?"}, "baseDomain": {"message": "Base domain", "description": "Domain name. Example: website.com"}, "domainName": {"message": "Domain name", "description": "Domain name. Example: website.com"}, "host": {"message": "Host", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "Exact"}, "startsWith": {"message": "Starts with"}, "regEx": {"message": "Regular expression", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "Match detection", "description": "URI match detection for auto-fill."}, "defaultMatchDetection": {"message": "Default match detection", "description": "Default URI match detection for auto-fill."}, "never": {"message": "Never"}, "toggleVisibility": {"message": "Toggle visibility"}, "toggleCollapse": {"message": "Toggle collapse", "description": "Toggling an expand/collapse state."}, "checkPassword": {"message": "Check if password has been exposed."}, "passwordExposed": {"message": "This password has been exposed $VALUE$ time(s) in data breaches. You should change it.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "This password was not found in any known data breaches. It should be safe to use."}, "save": {"message": "Save"}, "cancel": {"message": "Cancel"}, "canceled": {"message": "Canceled"}, "close": {"message": "Close"}, "delete": {"message": "Delete"}, "favorite": {"message": "Favorite"}, "unfavorite": {"message": "Unfavorite"}, "edit": {"message": "Edit"}, "searchCollection": {"message": "Search collection"}, "searchFolder": {"message": "Search folder"}, "searchFavorites": {"message": "Search favorites"}, "searchLogin": {"message": "Search logins", "description": "Search Login type"}, "searchCard": {"message": "Search cards", "description": "Search Card type"}, "searchIdentity": {"message": "Search identities", "description": "Search Identity type"}, "searchSecureNote": {"message": "Search secure notes", "description": "Search Secure Note type"}, "searchVault": {"message": "Search vault"}, "searchMyVault": {"message": "Search my vault"}, "searchOrganization": {"message": "Search organization"}, "searchMembers": {"message": "Search members"}, "searchGroups": {"message": "Search groups"}, "allItems": {"message": "All items"}, "favorites": {"message": "Favorites"}, "types": {"message": "Types"}, "typeLogin": {"message": "<PERSON><PERSON>"}, "typeCard": {"message": "Card"}, "typeIdentity": {"message": "Identity"}, "typeSecureNote": {"message": "Secure note"}, "typeSshKey": {"message": "SSH key"}, "typeLoginPlural": {"message": "<PERSON><PERSON>"}, "typeCardPlural": {"message": "Cards"}, "typeIdentityPlural": {"message": "Identities"}, "typeSecureNotePlural": {"message": "Secure notes"}, "folders": {"message": "Folders"}, "collections": {"message": "Collections"}, "firstName": {"message": "First name"}, "middleName": {"message": "Middle name"}, "lastName": {"message": "Last name"}, "fullName": {"message": "Full name"}, "address": {"message": "Address"}, "address1": {"message": "Address 1"}, "address2": {"message": "Address 2"}, "address3": {"message": "Address 3"}, "cityTown": {"message": "City / Town"}, "stateProvince": {"message": "State / Province"}, "zipPostalCode": {"message": "Zip / Postal code"}, "country": {"message": "Country"}, "shared": {"message": "Shared"}, "attachments": {"message": "Attachments"}, "select": {"message": "Select"}, "newItem": {"message": "New item"}, "addItem": {"message": "Add item"}, "editItem": {"message": "Edit item"}, "viewItem": {"message": "View item"}, "newItemHeader": {"message": "New $TYPE$", "placeholders": {"type": {"content": "$1", "example": "login"}}}, "editItemHeader": {"message": "Edit $TYPE$", "placeholders": {"type": {"content": "$1", "example": "login"}}}, "viewItemType": {"message": "View $ITEMTYPE$", "placeholders": {"itemtype": {"content": "$1", "example": "login"}}}, "new": {"message": "New", "description": "for adding new items"}, "item": {"message": "<PERSON><PERSON>"}, "itemDetails": {"message": "Item details"}, "itemName": {"message": "Item name"}, "ex": {"message": "ex.", "description": "Short abbreviation for 'example'."}, "other": {"message": "Other"}, "share": {"message": "Share"}, "moveToOrganization": {"message": "Move to organization"}, "valueCopied": {"message": "$VALUE$ copied", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "copySuccessful": {"message": "Copy Successful"}, "copyValue": {"message": "Copy value", "description": "Copy value to clipboard"}, "copyPassword": {"message": "Copy password", "description": "Copy password to clipboard"}, "copyPassphrase": {"message": "Copy passphrase", "description": "Copy passphrase to clipboard"}, "passwordCopied": {"message": "Password copied"}, "copyUsername": {"message": "Copy username", "description": "Copy username to clipboard"}, "copyNumber": {"message": "Copy number", "description": "Copy credit card number"}, "copySecurityCode": {"message": "Copy security code", "description": "Copy credit card security code (CVV)"}, "copyUri": {"message": "Copy URI", "description": "Copy URI to clipboard"}, "copyCustomField": {"message": "Copy $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Copy website"}, "copyNotes": {"message": "Copy notes"}, "copyAddress": {"message": "Copy address"}, "copyPhone": {"message": "Copy phone"}, "copyEmail": {"message": "Copy email"}, "copyCompany": {"message": "Copy company"}, "copySSN": {"message": "Copy Social Security number"}, "copyPassportNumber": {"message": "Copy passport number"}, "copyLicenseNumber": {"message": "Copy license number"}, "copyName": {"message": "Copy name"}, "me": {"message": "Me"}, "myVault": {"message": "My vault"}, "allVaults": {"message": "All vaults"}, "vault": {"message": "<PERSON><PERSON>"}, "vaults": {"message": "Vaults"}, "vaultItems": {"message": "Vault items"}, "filter": {"message": "Filter"}, "deleteSelected": {"message": "Delete selected"}, "moveSelected": {"message": "Move selected"}, "selectAll": {"message": "Select all"}, "unselectAll": {"message": "Unselect all"}, "launch": {"message": "Launch"}, "newAttachment": {"message": "Add new attachment"}, "deletedAttachment": {"message": "Deleted attachment"}, "deleteAttachmentConfirmation": {"message": "Are you sure you want to delete this attachment?"}, "attachmentSaved": {"message": "<PERSON><PERSON><PERSON><PERSON> saved"}, "file": {"message": "File"}, "selectFile": {"message": "Select a file."}, "maxFileSize": {"message": "Maximum file size is 500 MB."}, "addedItem": {"message": "<PERSON><PERSON> added"}, "editedItem": {"message": "<PERSON><PERSON> saved"}, "movedItemToOrg": {"message": "$ITEMNAME$ moved to $ORGNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "itemsMovedToOrg": {"message": "Items moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Item moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "deleteItem": {"message": "Delete item"}, "deleteFolder": {"message": "Delete folder"}, "deleteAttachment": {"message": "Delete attachment"}, "deleteItemConfirmation": {"message": "Do you really want to send to the trash?"}, "deletedItem": {"message": "Item sent to trash"}, "deletedItems": {"message": "Items sent to trash"}, "movedItems": {"message": "Items moved"}, "overwritePasswordConfirmation": {"message": "Are you sure you want to overwrite the current password?"}, "editedFolder": {"message": "Folder saved"}, "addedFolder": {"message": "Folder added"}, "deleteFolderConfirmation": {"message": "Are you sure you want to delete this folder?"}, "deletedFolder": {"message": "Folder deleted"}, "editInfo": {"message": "Edit info"}, "access": {"message": "Access"}, "accessLevel": {"message": "Access level"}, "accessing": {"message": "Accessing"}, "loggedOut": {"message": "Logged out"}, "loggedOutDesc": {"message": "You have been logged out of your account."}, "loginExpired": {"message": "Your login session has expired."}, "restartRegistration": {"message": "Restart registration"}, "expiredLink": {"message": "Expired link"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Please restart registration or try logging in."}, "youMayAlreadyHaveAnAccount": {"message": "You may already have an account"}, "logOutConfirmation": {"message": "Are you sure you want to log out?"}, "logOut": {"message": "Log out"}, "ok": {"message": "Ok"}, "yes": {"message": "Yes"}, "no": {"message": "No"}, "location": {"message": "Location"}, "loginOrCreateNewAccount": {"message": "Log in or create a new account to access your secure vault."}, "loginWithDevice": {"message": "Log in with device"}, "loginWithDeviceEnabledNote": {"message": "Log in with device must be set up in the settings of the Bitwarden app. Need another option?"}, "needAnotherOptionV1": {"message": "Need another option?"}, "loginWithMasterPassword": {"message": "Log in with master password"}, "readingPasskeyLoading": {"message": "Reading passkey..."}, "readingPasskeyLoadingInfo": {"message": "Keep this window open and follow prompts from your browser."}, "useADifferentLogInMethod": {"message": "Use a different log in method"}, "logInWithPasskey": {"message": "Log in with passkey"}, "useSingleSignOn": {"message": "Use single sign-on"}, "welcomeBack": {"message": "Welcome back"}, "invalidPasskeyPleaseTryAgain": {"message": "Invalid <PERSON>. Please try again."}, "twoFactorForPasskeysNotSupportedOnClientUpdateToLogIn": {"message": "2FA for passkeys is not supported. Update the app to log in."}, "loginWithPasskeyInfo": {"message": "Use a generated passkey that will automatically log you in without a password. Biometrics, like facial recognition or fingerprint, or another FIDO2 security method will verify your identity."}, "newPasskey": {"message": "New passkey"}, "learnMoreAboutPasswordless": {"message": "Learn more about passwordless"}, "creatingPasskeyLoading": {"message": "Creating passkey..."}, "creatingPasskeyLoadingInfo": {"message": "Keep this window open and follow prompts from your browser."}, "errorCreatingPasskey": {"message": "Error creating passkey"}, "errorCreatingPasskeyInfo": {"message": "There was a problem creating your passkey."}, "passkeySuccessfullyCreated": {"message": "Passkey successfully created!"}, "customPasskeyNameInfo": {"message": "Name your passkey to help you identify it."}, "useForVaultEncryption": {"message": "Use for vault encryption"}, "useForVaultEncryptionInfo": {"message": "Log in and unlock on supported devices without your master password. Follow the prompts from your browser to finalize setup."}, "useForVaultEncryptionErrorReadingPasskey": {"message": "Error reading passkey. Try again or uncheck this option."}, "encryptionNotSupported": {"message": "Encryption not supported"}, "enablePasskeyEncryption": {"message": "Set up encryption"}, "usedForEncryption": {"message": "Used for encryption"}, "loginWithPasskeyEnabled": {"message": "Log in with passkey turned on"}, "passkeySaved": {"message": "$NAME$ saved", "placeholders": {"name": {"content": "$1", "example": "Personal yubikey"}}}, "passkeyRemoved": {"message": "Passkey removed"}, "removePasskey": {"message": "Remove passkey"}, "removePasskeyInfo": {"message": "If all passkeys are removed, you will be unable to log into new devices without your master password."}, "passkeyLimitReachedInfo": {"message": "Passkey limit reached. Remove a passkey to add another."}, "tryAgain": {"message": "Try again"}, "createAccount": {"message": "Create account"}, "newToBitwarden": {"message": "New to Bitwarden?"}, "setAStrongPassword": {"message": "Set a strong password"}, "finishCreatingYourAccountBySettingAPassword": {"message": "Finish creating your account by setting a password"}, "newAroundHere": {"message": "New around here?"}, "startTrial": {"message": "Start trial"}, "logIn": {"message": "Log in"}, "logInToBitwarden": {"message": "Log in to Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "Enter the code sent to your email"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Enter the code from your authenticator app"}, "pressYourYubiKeyToAuthenticate": {"message": "Press your YubiKey to authenticate"}, "authenticationTimeout": {"message": "Authentication timeout"}, "authenticationSessionTimedOut": {"message": "The authentication session timed out. Please restart the login process."}, "verifyYourIdentity": {"message": "Verify your Identity"}, "weDontRecognizeThisDevice": {"message": "We don't recognize this device. Enter the code sent to your email to verify your identity."}, "continueLoggingIn": {"message": "Continue logging in"}, "whatIsADevice": {"message": "What is a device?"}, "aDeviceIs": {"message": "A device is a unique installation of the Bitwarden app where you have logged in. Reinstalling, clearing app data, or clearing your cookies could result in a device appearing multiple times."}, "logInInitiated": {"message": "Log in initiated"}, "logInRequestSent": {"message": "Request sent"}, "submit": {"message": "Submit"}, "emailAddressDesc": {"message": "You'll use your email address to log in."}, "yourName": {"message": "Your name"}, "yourNameDesc": {"message": "What should we call you?"}, "masterPass": {"message": "Master password"}, "masterPassDesc": {"message": "The master password is the password you use to access your vault. It is very important that you do not forget your master password. There is no way to recover the password in the event that you forget it."}, "masterPassImportant": {"message": "Your master password cannot be recovered if you forget it!"}, "masterPassHintDesc": {"message": "A master password hint can help you remember your password if you forget it."}, "reTypeMasterPass": {"message": "Re-type master password"}, "masterPassHint": {"message": "Master password hint (optional)"}, "newMasterPassHint": {"message": "New master password hint (optional)"}, "masterPassHintLabel": {"message": "Master password hint"}, "masterPassHintText": {"message": "If you forget your password, the password hint can be sent to your email. $CURRENT$/$MAXIMUM$ character maximum.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "settings": {"message": "Settings"}, "accountEmail": {"message": "Account email"}, "requestHint": {"message": "Request hint"}, "requestPasswordHint": {"message": "Request password hint"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Enter your account email address and your password hint will be sent to you"}, "getMasterPasswordHint": {"message": "Get master password hint"}, "emailRequired": {"message": "Email address is required."}, "invalidEmail": {"message": "Invalid email address."}, "masterPasswordRequired": {"message": "Master password is required."}, "confirmMasterPasswordRequired": {"message": "Master password retype is required."}, "masterPasswordMinlength": {"message": "Master password must be at least $VALUE$ characters long.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "Master password confirmation does not match."}, "newAccountCreated": {"message": "Your new account has been created! You may now log in."}, "newAccountCreated2": {"message": "Your new account has been created!"}, "youHaveBeenLoggedIn": {"message": "You have been logged in!"}, "trialAccountCreated": {"message": "Account created successfully."}, "masterPassSent": {"message": "We've sent you an email with your master password hint."}, "unexpectedError": {"message": "An unexpected error has occurred."}, "expirationDateError": {"message": "Please select an expiration date that is in the future."}, "emailAddress": {"message": "Email address"}, "yourVaultIsLockedV2": {"message": "Your vault is locked"}, "yourAccountIsLocked": {"message": "Your account is locked"}, "uuid": {"message": "UUID"}, "unlock": {"message": "Unlock"}, "loggedInAsEmailOn": {"message": "Logged in as $EMAIL$ on $HOSTNAME$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "Invalid master password"}, "invalidFilePassword": {"message": "Invalid file password, please use the password you entered when you created the export file."}, "lockNow": {"message": "Lock now"}, "noItemsInList": {"message": "There are no items to list."}, "noPermissionToViewAllCollectionItems": {"message": "You do not have permission to view all items in this collection."}, "youDoNotHavePermissions": {"message": "You do not have permissions to this collection"}, "noCollectionsInList": {"message": "There are no collections to list."}, "noGroupsInList": {"message": "There are no groups to list."}, "noUsersInList": {"message": "There are no users to list."}, "noMembersInList": {"message": "There are no members to list."}, "noEventsInList": {"message": "There are no events to list."}, "newOrganization": {"message": "New organization"}, "noOrganizationsList": {"message": "You do not belong to any organizations. Organizations allow you to securely share items with other users."}, "notificationSentDevice": {"message": "A notification has been sent to your device."}, "notificationSentDevicePart1": {"message": "Unlock Bitwarden on your device or on the "}, "areYouTryingToAccessYourAccount": {"message": "Are you trying to access your account?"}, "accessAttemptBy": {"message": "Access attempt by $EMAIL$", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "confirmAccess": {"message": "Confirm access"}, "denyAccess": {"message": "Deny access"}, "notificationSentDeviceAnchor": {"message": "web app"}, "notificationSentDevicePart2": {"message": "Make sure the Fingerprint phrase matches the one below before approving."}, "notificationSentDeviceComplete": {"message": "Unlock <PERSON>warden on your device. Make sure the Fingerprint phrase matches the one below before approving."}, "aNotificationWasSentToYourDevice": {"message": "A notification was sent to your device"}, "versionNumber": {"message": "Version $VERSION_NUMBER$", "placeholders": {"version_number": {"content": "$1", "example": "1.2.3"}}}, "verificationCodeEmailSent": {"message": "Verification email sent to $EMAIL$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Don't ask again on this device for 30 days"}, "selectAnotherMethod": {"message": "Select another method", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Use your recovery code"}, "insertU2f": {"message": "Insert your security key into your computer's USB port. If it has a button, touch it."}, "loginUnavailable": {"message": "Login unavailable"}, "noTwoStepProviders": {"message": "This account has two-step login set up, however, none of the configured two-step providers are supported by this web browser."}, "noTwoStepProviders2": {"message": "Please use a supported web browser (such as Chrome) and/or add additional providers that are better supported across web browsers (such as an authenticator app)."}, "twoStepOptions": {"message": "Two-step login options"}, "selectTwoStepLoginMethod": {"message": "Select two-step login method"}, "recoveryCodeDesc": {"message": "Lost access to all of your two-step login providers? Use your recovery code to turn off all two-step login providers from your account."}, "recoveryCodeTitle": {"message": "Recovery code"}, "authenticatorAppTitle": {"message": "Authenticator app"}, "authenticatorAppDescV2": {"message": "Enter a code generated by an authenticator app like Bitwarden Authenticator.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "Yubico OTP security key"}, "yubiKeyDesc": {"message": "Use a YubiKey 4, 5 or NEO device."}, "duoDescV2": {"message": "Enter a code generated by Duo Security.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Verify with Duo Security for your organization using the Duo Mobile app, SMS, phone call, or U2F security key.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "u2fDesc": {"message": "Use any FIDO U2F compatible security key to access your account."}, "u2fTitle": {"message": "FIDO U2F security a key"}, "webAuthnTitle": {"message": "Passkey"}, "webAuthnDesc": {"message": "Use your device's biometrics or a FIDO2 compatible security key."}, "webAuthnMigrated": {"message": "(Migrated from FIDO)"}, "openInNewTab": {"message": "Open in new tab"}, "emailTitle": {"message": "Email"}, "emailDescV2": {"message": "Enter a code sent to your email."}, "continue": {"message": "Continue"}, "organization": {"message": "Organization"}, "organizations": {"message": "Organizations"}, "moveToOrgDesc": {"message": "Choose an organization that you wish to move this item to. Moving to an organization transfers ownership of the item to that organization. You will no longer be the direct owner of this item once it has been moved."}, "collectionsDesc": {"message": "Edit the collections that this item is being shared with. Only organization users with access to these collections will be able to see this item."}, "deleteSelectedItemsDesc": {"message": "$COUNT$ item(s) will be sent to trash.", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "deleteSelectedCollectionsDesc": {"message": "$COUNT$ collection(s) will be permanently deleted.", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "deleteSelectedConfirmation": {"message": "Are you sure you want to continue?"}, "moveSelectedItemsDesc": {"message": "Choose a folder that you would like to add the $COUNT$ selected item(s) to.", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "verificationCodeTotp": {"message": "Verification code (TOTP)"}, "copyVerificationCode": {"message": "Copy verification code"}, "copyUuid": {"message": "Copy UUID"}, "errorRefreshingAccessToken": {"message": "Access Token Refresh Error"}, "errorRefreshingAccessTokenDesc": {"message": "No refresh token or API keys found. Please try logging out and logging back in."}, "warning": {"message": "Warning"}, "confirmVaultExport": {"message": "Confirm vault export"}, "confirmSecretsExport": {"message": "Confirm secrets export"}, "exportWarningDesc": {"message": "This export contains your vault data in an unencrypted format. You should not store or send the exported file over unsecure channels (such as email). Delete it immediately after you are done using it."}, "exportSecretsWarningDesc": {"message": "This export contains your secrets data in an unencrypted format. You should not store or send the exported file over unsecure channels (such as email). Delete it immediately after you are done using it."}, "encExportKeyWarningDesc": {"message": "This export encrypts your data using your account's encryption key. If you ever rotate your account's encryption key you should export again since you will not be able to decrypt this export file."}, "encExportAccountWarningDesc": {"message": "Account encryption keys are unique to each Bitwarden user account, so you can't import an encrypted export into a different account."}, "export": {"message": "Export"}, "exportFrom": {"message": "Export from"}, "exportVault": {"message": "Export vault"}, "exportSecrets": {"message": "Export secrets"}, "fileFormat": {"message": "File format"}, "fileEncryptedExportWarningDesc": {"message": "This file export will be password protected and require the file password to decrypt."}, "exportPasswordDescription": {"message": "This password will be used to export and import this file"}, "confirmMasterPassword": {"message": "Confirm master password"}, "confirmFormat": {"message": "Confirm format"}, "filePassword": {"message": "File password"}, "confirmFilePassword": {"message": "Confirm file password"}, "accountRestrictedOptionDescription": {"message": "Use your account encryption key, derived from your account's username and Master Password, to encrypt the export and restrict import to only the current Bitwarden account."}, "passwordProtectedOptionDescription": {"message": "Set a file password to encrypt the export and import it to any Bitwarden account using the password for decryption."}, "exportTypeHeading": {"message": "Export type"}, "accountRestricted": {"message": "Account restricted"}, "passwordProtected": {"message": "Password protected"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "“File password”  and “Confirm file password“ do not match."}, "confirmVaultImport": {"message": "Confirm vault import"}, "confirmVaultImportDesc": {"message": "This file is password-protected. Please enter the file password to import data."}, "exportSuccess": {"message": "Vault data exported"}, "passwordGenerator": {"message": "Password generator"}, "minComplexityScore": {"message": "Minimum complexity score"}, "minNumbers": {"message": "Minimum numbers"}, "minSpecial": {"message": "Minimum special", "description": "Minimum special characters"}, "ambiguous": {"message": "Avoid ambiguous characters", "description": "deprecated. Use avoidAmbiguous instead."}, "avoidAmbiguous": {"message": "Avoid ambiguous characters", "description": "Label for the avoid ambiguous characters checkbox."}, "length": {"message": "Length"}, "passwordMinLength": {"message": "Minimum password length"}, "uppercase": {"message": "Uppercase (A-Z)", "description": "deprecated. Use uppercaseLabel instead."}, "lowercase": {"message": "Lowercase (a-z)", "description": "deprecated. Use lowercaseLabel instead."}, "numbers": {"message": "Numbers (0-9)", "description": "deprecated. Use numbersLabel instead."}, "specialCharacters": {"message": "Special characters (!@#$%^&*)"}, "numWords": {"message": "Number of words"}, "wordSeparator": {"message": "Word separator"}, "capitalize": {"message": "Capitalize", "description": "Make the first letter of a word uppercase."}, "includeNumber": {"message": "Include number"}, "generatorPolicyInEffect": {"message": "Enterprise policy requirements have been applied to your generator options.", "description": "Indicates that a policy limits the credential generator screen."}, "passwordHistory": {"message": "Password history"}, "generatorHistory": {"message": "Generator history"}, "clearGeneratorHistoryTitle": {"message": "Clear generator history"}, "cleargGeneratorHistoryDescription": {"message": "If you continue, all entries will be permanently deleted from generator's history. Are you sure you want to continue?"}, "noPasswordsInList": {"message": "There are no passwords to list."}, "clearHistory": {"message": "Clear history"}, "nothingToShow": {"message": "Nothing to show"}, "nothingGeneratedRecently": {"message": "You haven't generated anything recently"}, "clear": {"message": "Clear", "description": "To clear something out. Example: To clear browser history."}, "accountUpdated": {"message": "Account saved"}, "changeEmail": {"message": "Change email"}, "changeEmailTwoFactorWarning": {"message": "Proceeding will change your account email address. It will not change the email address used for two-step login authentication. You can change this email address in the two-step login settings."}, "newEmail": {"message": "New email"}, "code": {"message": "Code"}, "changeEmailDesc": {"message": "We have emailed a verification code to $EMAIL$. Please check your email for this code and enter it below to finalize the email address change.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "loggedOutWarning": {"message": "Proceeding will log you out of your current session, requiring you to log back in. Active sessions on other devices may continue to remain active for up to one hour."}, "emailChanged": {"message": "<PERSON><PERSON> saved"}, "logBackIn": {"message": "Please log back in."}, "currentSession": {"message": "Current session"}, "requestPending": {"message": "Request pending"}, "logBackInOthersToo": {"message": "Please log back in. If you are using other Bitwarden applications log out and back in to those as well."}, "changeMasterPassword": {"message": "Change master password"}, "masterPasswordChanged": {"message": "Master password saved"}, "currentMasterPass": {"message": "Current master password"}, "newMasterPass": {"message": "New master password"}, "confirmNewMasterPass": {"message": "Confirm new master password"}, "encKeySettings": {"message": "Encryption key settings"}, "kdfAlgorithm": {"message": "KDF algorithm"}, "kdfIterations": {"message": "KDF iterations"}, "kdfIterationsDesc": {"message": "Higher KDF iterations can help protect your master password from being brute forced by an attacker. We recommend a value of $VALUE$ or more.", "placeholders": {"value": {"content": "$1", "example": "100,000"}}}, "kdfIterationsWarning": {"message": "Setting your KDF iterations too high could result in poor performance when logging into (and unlocking) Bitwarden on slower or older devices. We recommend that you increase the value in increments of $INCREMENT$ and then test all of your devices.", "placeholders": {"increment": {"content": "$1", "example": "50,000"}}}, "kdfMemory": {"message": "KDF memory (MB)", "description": "Memory refers to computer memory (RAM). MB is short for megabytes."}, "argon2Warning": {"message": "Setting your KDF iterations, memory, and parallelism too high could result in poor performance when logging into (and unlocking) Bitwarden on slower or older devices. We recommend changing these individually in small increments and then test all of your devices."}, "kdfParallelism": {"message": "KDF parallelism"}, "argon2Desc": {"message": "Higher KDF iterations, memory, and parallelism can help protect your master password from being brute forced by an attacker."}, "changeKdf": {"message": "Change KDF"}, "encKeySettingsChanged": {"message": "Encryption key settings saved"}, "dangerZone": {"message": "Danger zone"}, "deauthorizeSessions": {"message": "Deauthorize sessions"}, "deauthorizeSessionsDesc": {"message": "Concerned your account is logged in on another device? Proceed below to deauthorize all computers or devices that you have previously used. This security step is recommended if you previously used a public computer or accidentally saved your password on a device that isn't yours. This step will also clear all previously remembered two-step login sessions."}, "deauthorizeSessionsWarning": {"message": "Proceeding will also log you out of your current session, requiring you to log back in. You will also be prompted for two-step login again, if set up. Active sessions on other devices may continue to remain active for up to one hour."}, "newDeviceLoginProtection": {"message": "New device login"}, "turnOffNewDeviceLoginProtection": {"message": "Turn off new device login protection"}, "turnOnNewDeviceLoginProtection": {"message": "Turn on new device login protection"}, "turnOffNewDeviceLoginProtectionModalDesc": {"message": "Proceed below to turn off the verification emails bitwarden sends when you login from a new device."}, "turnOnNewDeviceLoginProtectionModalDesc": {"message": "Proceed below to have bitwarden send you verification emails when you login from a new device."}, "turnOffNewDeviceLoginProtectionWarning": {"message": "With new device login protection turned off, anyone with your master password can access your account from any device. To protect your account without verification emails, set up two-step login."}, "accountNewDeviceLoginProtectionSaved": {"message": "New device login protection changes saved"}, "sessionsDeauthorized": {"message": "All sessions deauthorized"}, "accountIsOwnedMessage": {"message": "This account is owned by $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "Organization"}}}, "purgeVault": {"message": "Purge vault"}, "purgedOrganizationVault": {"message": "Purged organization vault."}, "vaultAccessedByProvider": {"message": "Vault accessed by Provider."}, "purgeVaultDesc": {"message": "Proceed below to delete all items and folders in your vault. Items that belong to an organization that you share with will not be deleted."}, "purgeOrgVaultDesc": {"message": "Proceed below to delete all items in the organization's vault."}, "purgeVaultWarning": {"message": "Purging your vault is permanent. It cannot be undone."}, "vaultPurged": {"message": "<PERSON><PERSON> purged."}, "deleteAccount": {"message": "Delete account"}, "deleteAccountDesc": {"message": "Proceed below to delete your account and all vault data."}, "deleteAccountWarning": {"message": "Deleting your account is permanent. It cannot be undone."}, "accountDeleted": {"message": "Account deleted"}, "accountDeletedDesc": {"message": "Your account has been closed and all associated data has been deleted."}, "deleteOrganizationWarning": {"message": "Deleting your organization is permanent. It cannot be undone."}, "myAccount": {"message": "My account"}, "tools": {"message": "Tools"}, "importData": {"message": "Import data"}, "onboardingImportDataDetailsPartOne": {"message": "If you don't have any data to import, you can create a ", "description": "This will be part of a larger sentence, that will read like this: If you don't have any data to import, you can create a new item instead. (Optional second half: You may need to wait until your administrator confirms your organization membership.)"}, "onboardingImportDataDetailsLink": {"message": "new item", "description": "This will be part of a larger sentence, that will read like this: If you don't have any data to import, you can create a new item instead. (Optional second half: You may need to wait until your administrator confirms your organization membership.)"}, "onboardingImportDataDetailsLoginLink": {"message": "new login", "description": "This will be part of a larger sentence, that will read like this: If you don't have any data to import, you can create a new login instead. (Optional second half: You may need to wait until your administrator confirms your organization membership.)"}, "onboardingImportDataDetailsPartTwoNoOrgs": {"message": " instead.", "description": "This will be part of a larger sentence, that will read like this: If you don't have any data to import, you can create a new item instead."}, "onboardingImportDataDetailsPartTwoWithOrgs": {"message": " instead. You may need to wait until your administrator confirms your organization membership.", "description": "This will be part of a larger sentence, that will read like this: If you don't have any data to import, you can create a new item instead. You may need to wait until your administrator confirms your organization membership."}, "importError": {"message": "Import error"}, "importErrorDesc": {"message": "There was a problem with the data you tried to import. Please resolve the errors listed below in your source file and try again."}, "importSuccess": {"message": "Data successfully imported"}, "importSuccessNumberOfItems": {"message": "A total of $AMOUNT$ items were imported.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "dataExportSuccess": {"message": "Data successfully exported"}, "importWarning": {"message": "You are importing data to $ORGANIZATION$. Your data may be shared with members of this organization. Do you want to proceed?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "importFormatError": {"message": "Data is not formatted correctly. Please check your import file and try again."}, "importNothingError": {"message": "Nothing was imported."}, "importEncKeyError": {"message": "Error decrypting the exported file. Your encryption key does not match the encryption key used export the data."}, "destination": {"message": "Destination"}, "learnAboutImportOptions": {"message": "Learn about your import options"}, "selectImportFolder": {"message": "Select a folder"}, "selectImportCollection": {"message": "Select a collection"}, "importTargetHint": {"message": "Select this option if you want the imported file contents moved to a $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "File contains unassigned items."}, "selectFormat": {"message": "Select the format of the import file"}, "selectImportFile": {"message": "Select the import file"}, "chooseFile": {"message": "Choose <PERSON>"}, "noFileChosen": {"message": "No file chosen"}, "orCopyPasteFileContents": {"message": "or copy/paste the import file contents"}, "instructionsFor": {"message": "$NAME$ Instructions", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "options": {"message": "Options"}, "preferences": {"message": "Preferences"}, "preferencesDesc": {"message": "Customize your web vault experience."}, "preferencesUpdated": {"message": "Preferences saved"}, "language": {"message": "Language"}, "languageDesc": {"message": "Change the language used by the web vault."}, "enableFavicon": {"message": "Show website icons"}, "faviconDesc": {"message": "Show a recognizable image next to each login."}, "default": {"message": "<PERSON><PERSON><PERSON>"}, "domainRules": {"message": "Domain rules"}, "domainRulesDesc": {"message": "If you have the same login across multiple different website domains, you can mark the website as \"equivalent\". \"Global\" domains are ones already created for you by Bitwarden."}, "globalEqDomains": {"message": "Global equivalent domains"}, "customEqDomains": {"message": "Custom equivalent domains"}, "exclude": {"message": "Exclude"}, "include": {"message": "Include"}, "customize": {"message": "Customize"}, "newCustomDomain": {"message": "New custom domain"}, "newCustomDomainDesc": {"message": "Enter a list of domains separated by commas. Only \"base\" domains are allowed. Do not enter subdomains. For example, enter \"google.com\" instead of \"www.google.com\". You can also enter \"androidapp://package.name\" to associate an android app with other website domains."}, "customDomainX": {"message": "Custom domain $INDEX$", "placeholders": {"index": {"content": "$1", "example": "2"}}}, "domainsUpdated": {"message": "Domains saved"}, "twoStepLogin": {"message": "Two-step login"}, "twoStepLoginEnforcement": {"message": "Two-step <PERSON><PERSON>"}, "twoStepLoginDesc": {"message": "Secure your account by requiring an additional step when logging in."}, "twoStepLoginTeamsDesc": {"message": "Enable two-step login for your organization."}, "twoStepLoginEnterpriseDescStart": {"message": "Enforce Bitwarden Two-step Login options for members by using the ", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Enforce Bitwarden Two-step Login options for members by using the Two-step Login Policy.'"}, "twoStepLoginPolicy": {"message": "Two-step Login <PERSON>"}, "twoStepLoginOrganizationDuoDesc": {"message": "To enforce Two-step Login through <PERSON>, use the options below."}, "twoStepLoginOrganizationSsoDesc": {"message": "If you have setup SSO or plan to, Two-step Login may already be enforced through your Identity Provider."}, "twoStepLoginRecoveryWarning": {"message": "Setting up two-step login can permanently lock you out of your Bitwarden account. A recovery code allows you to access your account in the event that you can no longer use your normal two-step login provider (example: you lose your device). Bitwarden support will not be able to assist you if you lose access to your account. We recommend you write down or print the recovery code and keep it in a safe place."}, "restrictedItemTypePolicy": {"message": "Remove card item type"}, "restrictedItemTypePolicyDesc": {"message": "Do not allow members to create card item types. Existing cards will be automatically removed."}, "restrictCardTypeImport": {"message": "Cannot import card item types"}, "restrictCardTypeImportDesc": {"message": "A policy set by 1 or more organizations prevents you from importing cards to your vaults."}, "yourSingleUseRecoveryCode": {"message": "Your single-use recovery code can be used to turn off two-step login in the event that you lose access to your two-step login provider. Bitwarden recommends you write down the recovery code and keep it in a safe place."}, "viewRecoveryCode": {"message": "View recovery code"}, "providers": {"message": "Providers", "description": "Two-step login providers such as YubiKey, Duo, Authenticator apps, Email, etc."}, "enable": {"message": "Turn on"}, "enabled": {"message": "Turned on"}, "restoreAccess": {"message": "Restore access"}, "premium": {"message": "Premium", "description": "Premium membership"}, "premiumMembership": {"message": "Premium membership"}, "premiumRequired": {"message": "Premium required"}, "premiumRequiredDesc": {"message": "A Premium membership is required to use this feature."}, "youHavePremiumAccess": {"message": "You have Premium access"}, "alreadyPremiumFromOrg": {"message": "You already have access to Premium features because of an organization you are a member of."}, "manage": {"message": "Manage"}, "manageCollection": {"message": "Manage collection"}, "viewItems": {"message": "View items"}, "viewItemsHidePass": {"message": "View items, hidden passwords"}, "editItems": {"message": "Edit items"}, "editItemsHidePass": {"message": "Edit items, hidden passwords"}, "disable": {"message": "Turn off"}, "orgUserDetailsNotFound": {"message": "Member details not found."}, "revokeAccess": {"message": "Revoke access"}, "revoke": {"message": "Revoke"}, "twoStepLoginProviderEnabled": {"message": "This two-step login provider is active on your account."}, "twoStepLoginAuthDesc": {"message": "Enter your master password to modify two-step login settings."}, "twoStepAuthenticatorInstructionPrefix": {"message": "Download an authenticator app such as"}, "twoStepAuthenticatorInstructionInfix1": {"message": ","}, "twoStepAuthenticatorInstructionInfix2": {"message": "or"}, "twoStepAuthenticatorInstructionSuffix": {"message": "."}, "continueToExternalUrlTitle": {"message": "Continue to $URL$?", "placeholders": {"url": {"content": "$1", "example": "bitwarden.com"}}}, "continueToExternalUrlDesc": {"message": "You are leaving Bitwarden and launching an external website in a new window."}, "twoStepContinueToBitwardenUrlTitle": {"message": "Continue to bitwarden.com?"}, "twoStepContinueToBitwardenUrlDesc": {"message": "Bitwarden Authenticator allows you to store authenticator keys and generate TOTP codes for 2-step verification flows. Learn more on the bitwarden.com website."}, "twoStepAuthenticatorScanCodeV2": {"message": "Scan the QR code below with your authenticator app or enter the key."}, "twoStepAuthenticatorQRCanvasError": {"message": "Could not load QR code. Try again or use the key below."}, "key": {"message": "Key"}, "twoStepAuthenticatorEnterCodeV2": {"message": "Verification code"}, "twoStepAuthenticatorReaddDesc": {"message": "In case you need to add it to another device, below is the QR code (or key) required by your authenticator app."}, "twoStepDisableDesc": {"message": "Are you sure you want to turn off this two-step login provider?"}, "twoStepDisabled": {"message": "Two-step login provider turned off."}, "twoFactorYubikeyAdd": {"message": "Add a new YubiKey to your account"}, "twoFactorYubikeyPlugIn": {"message": "Plug the YubiKey into your computer's USB port."}, "twoFactorYubikeySelectKey": {"message": "Select the first empty YubiKey input field below."}, "twoFactorYubikeyTouchButton": {"message": "Touch the YubiKey's button."}, "twoFactorYubikeySaveForm": {"message": "Save the form."}, "twoFactorYubikeyWarning": {"message": "Due to platform limitations, YubiKeys cannot be used on all Bitwarden applications. You should set up another two-step login provider so that you can access your account when YubiKeys cannot be used. Supported platforms:"}, "twoFactorYubikeySupportUsb": {"message": "Web vault, desktop application, CLI, and all browser extensions on a device with a USB port that can accept your YubiKey."}, "twoFactorYubikeySupportMobile": {"message": "Mobile apps on a device with NFC capabilities or a data port that can accept your YubiKey."}, "yubikeyX": {"message": "YubiKey $INDEX$", "placeholders": {"index": {"content": "$1", "example": "2"}}}, "u2fkeyX": {"message": "U2F Key $INDEX$", "placeholders": {"index": {"content": "$1", "example": "2"}}}, "webAuthnkeyX": {"message": "WebAuthn Key $INDEX$", "placeholders": {"index": {"content": "$1", "example": "2"}}}, "nfcSupport": {"message": "NFC Support"}, "twoFactorYubikeySupportsNfc": {"message": "One of my keys supports NFC."}, "twoFactorYubikeySupportsNfcDesc": {"message": "If one of your YubiKeys supports NFC (such as a YubiKey NEO), you will be prompted on mobile devices whenever NFC availability is detected."}, "yubikeysUpdated": {"message": "YubiKeys updated"}, "disableAllKeys": {"message": "Deactivate all keys"}, "twoFactorDuoDesc": {"message": "Enter the Bitwarden application information from your Duo Admin panel."}, "twoFactorDuoClientId": {"message": "Client Id"}, "twoFactorDuoClientSecret": {"message": "Client Secret"}, "twoFactorDuoApiHostname": {"message": "API hostname"}, "twoFactorEmailDesc": {"message": "Follow these steps to set up two-step login with email:"}, "twoFactorEmailEnterEmail": {"message": "Enter the email that you wish to receive verification codes"}, "twoFactorEmailEnterCode": {"message": "Enter the resulting 6 digit verification code from the email"}, "sendEmail": {"message": "Send email"}, "twoFactorU2fAdd": {"message": "Add a FIDO U2F security key to your account"}, "removeU2fConfirmation": {"message": "Are you sure you want to remove this security key?"}, "twoFactorWebAuthnAdd": {"message": "Add a WebAuthn security key to your account"}, "readKey": {"message": "Read key"}, "keyCompromised": {"message": "Key is compromised."}, "twoFactorU2fGiveName": {"message": "Give the security key a friendly name to identify it."}, "twoFactorU2fPlugInReadKey": {"message": "Plug the security key into your computer's USB port and click the \"Read Key\" button."}, "twoFactorU2fTouchButton": {"message": "If the security key has a button, touch it."}, "twoFactorU2fSaveForm": {"message": "Save the form."}, "twoFactorU2fWarning": {"message": "Due to platform limitations, FIDO U2F cannot be used on all Bitwarden applications. You should set up another two-step login provider so that you can access your account when FIDO U2F cannot be used. Supported platforms:"}, "twoFactorU2fSupportWeb": {"message": "Web vault and browser extensions on a desktop/laptop with a U2F supported browser (Chrome, Opera, Vivaldi, or Firefox with FIDO U2F turned on)."}, "twoFactorU2fWaiting": {"message": "Waiting for you to touch the button on your security key"}, "twoFactorU2fClickSave": {"message": "Use the \"Save\" button below to activate this security key for two-step login."}, "twoFactorU2fProblemReadingTryAgain": {"message": "There was a problem reading the security key. Try again."}, "twoFactorWebAuthnWarning1": {"message": "Due to platform limitations, WebAuthn cannot be used on all Bitwarden applications. You should set up another two-step login provider so that you can access your account when WebAuthn cannot be used."}, "twoFactorRecoveryYourCode": {"message": "Your Bitwarden two-step login recovery code"}, "twoFactorRecoveryNoCode": {"message": "You have not set up any two-step login providers yet. After you have set up a two-step login provider you can check back here for your recovery code."}, "printCode": {"message": "Print code", "description": "Print 2FA recovery code"}, "reports": {"message": "Reports"}, "reportsDesc": {"message": "Identify and close security gaps in your online accounts by clicking the reports below.", "description": "Vault health reports can be used to evaluate the security of your Bitwarden individual or organization vault."}, "orgsReportsDesc": {"message": "Identify and close security gaps in your organization's accounts by clicking the reports below.", "description": "Vault health reports can be used to evaluate the security of your Bitwarden individual or organization vault."}, "unsecuredWebsitesReport": {"message": "Unsecure websites"}, "unsecuredWebsitesReportDesc": {"message": "URLs that start with http:// don’t use the best available encryption. Change the login URIs for these accounts to https:// for safer browsing."}, "unsecuredWebsitesFound": {"message": "Unsecured websites found"}, "unsecuredWebsitesFoundReportDesc": {"message": "We found $COUNT$ items in your $VAULT$ with unsecured URIs. You should change their URI scheme to https:// if the website allows it.", "placeholders": {"count": {"content": "$1", "example": "8"}, "vault": {"content": "$2", "example": "this will be 'vault' or 'vaults'"}}}, "noUnsecuredWebsites": {"message": "No items in your vault have unsecured URIs."}, "inactive2faReport": {"message": "Inactive two-step login"}, "inactive2faReportDesc": {"message": "Two-step login adds a layer of protection to your accounts. Set up two-step login using Bitwarden authenticator for these accounts or use an alternative method."}, "inactive2faFound": {"message": "<PERSON><PERSON> without two-step login found"}, "inactive2faFoundReportDesc": {"message": "We found $COUNT$ website(s) in your $VAULT$ that may not be configured with two-step login (according to 2fa.directory). To further protect these accounts, you should set up two-step login.", "placeholders": {"count": {"content": "$1", "example": "8"}, "vault": {"content": "$2", "example": "this will be 'vault' or 'vaults'"}}}, "noInactive2fa": {"message": "No websites were found in your vault with a missing two-step login configuration."}, "instructions": {"message": "Instructions"}, "exposedPasswordsReport": {"message": "Exposed passwords"}, "exposedPasswordsReportDesc": {"message": "Passwords exposed in a data breach are easy targets for attackers. Change these passwords to prevent potential break-ins."}, "exposedPasswordsFound": {"message": "Exposed passwords found"}, "exposedPasswordsFoundReportDesc": {"message": "We found $COUNT$ items in your $VAULT$ that have passwords that were exposed in known data breaches. You should change them to use a new password.", "placeholders": {"count": {"content": "$1", "example": "8"}, "vault": {"content": "$2", "example": "this will be 'vault' or 'vaults'"}}}, "noExposedPasswords": {"message": "No items in your vault have passwords that have been exposed in known data breaches."}, "checkExposedPasswords": {"message": "Check exposed passwords"}, "timesExposed": {"message": "Times exposed"}, "exposedXTimes": {"message": "Exposed $COUNT$ time(s)", "placeholders": {"count": {"content": "$1", "example": "52"}}}, "weakPasswordsReport": {"message": "Weak passwords"}, "weakPasswordsReportDesc": {"message": "Weak passwords can be easily guessed by attackers. Change these passwords to strong ones using the password generator."}, "weakPasswordsFound": {"message": "Weak passwords found"}, "weakPasswordsFoundReportDesc": {"message": "We found $COUNT$ items in your $VAULT$ with passwords that are not strong. You should update them to use stronger passwords.", "placeholders": {"count": {"content": "$1", "example": "8"}, "vault": {"content": "$2", "example": "this will be 'vault' or 'vaults'"}}}, "noWeakPasswords": {"message": "No items in your vault have weak passwords."}, "weakness": {"message": "Weakness"}, "reusedPasswordsReport": {"message": "Reused passwords"}, "reusedPasswordsReportDesc": {"message": "Reusing passwords makes it easier for attackers to break into multiple accounts. Change these passwords so that each is unique."}, "reusedPasswordsFound": {"message": "Reused passwords found"}, "reusedPasswordsFoundReportDesc": {"message": "We found $COUNT$ passwords that are being reused in your $VAULT$. You should change them to a unique value.", "placeholders": {"count": {"content": "$1", "example": "8"}, "vault": {"content": "$2", "example": "this will be 'vault' or 'vaults'"}}}, "noReusedPasswords": {"message": "No logins in your vault have passwords that are being reused."}, "timesReused": {"message": "Times reused"}, "reusedXTimes": {"message": "Reused $COUNT$ times", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "dataBreachReport": {"message": "Data breach"}, "breachDesc": {"message": "Breached accounts can expose your personal information. Secure breached accounts by enabling 2FA or creating a stronger password."}, "breachCheckUsernameEmail": {"message": "Check any usernames or email addresses that you use."}, "checkBreaches": {"message": "Check breaches"}, "breachUsernameNotFound": {"message": "$USERNAME$ was not found in any known data breaches.", "placeholders": {"username": {"content": "$1", "example": "<EMAIL>"}}}, "goodNews": {"message": "Good news", "description": "ex. Good News, No Breached Accounts Found!"}, "breachUsernameFound": {"message": "$USERNAME$ was found in $COUNT$ different data breaches online.", "placeholders": {"username": {"content": "$1", "example": "<EMAIL>"}, "count": {"content": "$2", "example": "7"}}}, "breachFound": {"message": "Breached accounts found"}, "compromisedData": {"message": "Compromised data"}, "website": {"message": "Website"}, "affectedUsers": {"message": "Affected users"}, "breachOccurred": {"message": "Breach occurred"}, "breachReported": {"message": "Breach reported"}, "reportError": {"message": "An error occurred trying to load the report. Try again"}, "billing": {"message": "Billing"}, "billingPlanLabel": {"message": "Billing plan"}, "paymentType": {"message": "Payment type"}, "accountCredit": {"message": "Account credit", "description": "Financial term. In the case of Bitwarden, a positive balance means that you owe money, while a negative balance means that you have a credit (Bitwarden owes you money)."}, "accountBalance": {"message": "Account balance", "description": "Financial term. In the case of Bitwarden, a positive balance means that you owe money, while a negative balance means that you have a credit (Bitwarden owes you money)."}, "addCredit": {"message": "Add credit", "description": "Add more credit to your account's balance."}, "amount": {"message": "Amount", "description": "Dollar amount, or quantity."}, "creditDelayed": {"message": "Added credit will appear on your account after the payment has been fully processed. Some payment methods are delayed and can take longer to process than others."}, "makeSureEnoughCredit": {"message": "Please make sure that your account has enough credit available for this purchase. If your account does not have enough credit available, your default payment method on file will be used for the difference. You can add credit to your account from the Billing page."}, "creditAppliedDesc": {"message": "Your account's credit can be used to make purchases. Any available credit will be automatically applied towards invoices generated for this account."}, "goPremium": {"message": "Go Premium", "description": "Another way of saying \"Get a Premium membership\""}, "premiumUpdated": {"message": "You've upgraded to Premium."}, "premiumUpgradeUnlockFeatures": {"message": "Upgrade your account to a Premium membership and unlock some great additional features."}, "premiumSignUpStorage": {"message": "1 GB encrypted storage for file attachments."}, "premiumSignUpTwoStepOptions": {"message": "Proprietary two-step login options such as YubiKey and Duo."}, "premiumSignUpEmergency": {"message": "Emergency access"}, "premiumSignUpReports": {"message": "Password hygiene, account health, and data breach reports to keep your vault safe."}, "premiumSignUpTotp": {"message": "TOTP verification code (2FA) generator for logins in your vault."}, "premiumSignUpSupport": {"message": "Priority customer support."}, "premiumSignUpFuture": {"message": "All future Premium features. More coming soon!"}, "premiumPrice": {"message": "All for just $PRICE$ /year!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceWithFamilyPlan": {"message": "Go premium for just $PRICE$ /year, or get premium accounts for $FAMILYPLANUSERCOUNT$ users and unlimited family sharing with a ", "placeholders": {"price": {"content": "$1", "example": "$10"}, "familyplanusercount": {"content": "$2", "example": "6"}}}, "bitwardenFamiliesPlan": {"message": "Bitwarden Families plan."}, "addons": {"message": "Add-ons"}, "premiumAccess": {"message": "Premium access"}, "premiumAccessDesc": {"message": "You can add Premium access to all members of your organization for $PRICE$ /$INTERVAL$.", "placeholders": {"price": {"content": "$1", "example": "$3.33"}, "interval": {"content": "$2", "example": "'month' or 'year'"}}}, "additionalStorageGb": {"message": "Additional storage (GB)"}, "additionalStorageGbDesc": {"message": "# of additional GB"}, "additionalStorageIntervalDesc": {"message": "Your plan comes with $SIZE$ of encrypted file storage. You can add additional storage for $PRICE$ per GB /$INTERVAL$.", "placeholders": {"size": {"content": "$1", "example": "1 GB"}, "price": {"content": "$2", "example": "$4.00"}, "interval": {"content": "$3", "example": "'month' or 'year'"}}}, "summary": {"message": "Summary"}, "total": {"message": "Total"}, "year": {"message": "year"}, "yr": {"message": "yr"}, "month": {"message": "month"}, "monthAbbr": {"message": "mo.", "description": "Short abbreviation for 'month'"}, "paymentChargedAnnually": {"message": "Your payment method will be charged immediately and then on a recurring basis each year. You may cancel at any time."}, "paymentCharged": {"message": "Your payment method will be charged immediately and then on a recurring basis each $INTERVAL$. You may cancel at any time.", "placeholders": {"interval": {"content": "$1", "example": "month or year"}}}, "paymentChargedWithUnpaidSubscription": {"message": "Your payment method will be charged for any unpaid subscriptions."}, "paymentChargedWithTrial": {"message": "Your plan comes with a free 7 day trial. Your payment method will not be charged until the trial has ended. You may cancel at any time."}, "paymentInformation": {"message": "Payment information"}, "billingInformation": {"message": "Billing information"}, "billingTrialSubLabel": {"message": "Your payment method will not be charged during the 7 day free trial."}, "creditCard": {"message": "Credit card"}, "paypalClickSubmit": {"message": "Select the PayPal button to log into your PayPal account, then click the Submit button below to continue."}, "cancelSubscription": {"message": "Cancel subscription"}, "subscriptionExpiration": {"message": "Subscription expiration"}, "subscriptionCanceled": {"message": "The subscription has been canceled."}, "pendingCancellation": {"message": "Pending cancellation"}, "subscriptionPendingCanceled": {"message": "The subscription has been marked for cancellation at the end of the current billing period."}, "reinstateSubscription": {"message": "Reinstate subscription"}, "reinstateConfirmation": {"message": "Are you sure you want to remove the pending cancellation request and reinstate your subscription?"}, "reinstated": {"message": "The subscription has been reinstated."}, "cancelConfirmation": {"message": "Are you sure you want to cancel? You will lose access to all of this subscription's features at the end of this billing cycle."}, "canceledSubscription": {"message": "Subscription canceled"}, "neverExpires": {"message": "Never expires"}, "status": {"message": "Status"}, "nextCharge": {"message": "Next charge"}, "details": {"message": "Details"}, "downloadLicense": {"message": "Download license"}, "viewBillingToken": {"message": "View Billing Token"}, "updateLicense": {"message": "Update license"}, "manageSubscription": {"message": "Manage subscription"}, "launchCloudSubscription": {"message": "Launch Cloud Subscription"}, "storage": {"message": "Storage"}, "addStorage": {"message": "Add storage"}, "removeStorage": {"message": "Remove storage"}, "subscriptionStorage": {"message": "Your subscription has a total of $MAX_STORAGE$ GB of encrypted file storage. You are currently using $USED_STORAGE$.", "placeholders": {"max_storage": {"content": "$1", "example": "4"}, "used_storage": {"content": "$2", "example": "65 MB"}}}, "paymentMethod": {"message": "Payment method"}, "noPaymentMethod": {"message": "No payment method on file."}, "addPaymentMethod": {"message": "Add payment method"}, "changePaymentMethod": {"message": "Change payment method"}, "invoices": {"message": "Invoices"}, "noUnpaidInvoices": {"message": "No unpaid invoices."}, "noPaidInvoices": {"message": "No paid invoices."}, "paid": {"message": "Paid", "description": "Past tense status of an invoice. ex. Paid or unpaid."}, "unpaid": {"message": "Unpaid", "description": "Past tense status of an invoice. ex. Paid or unpaid."}, "transactions": {"message": "Transactions", "description": "Payment/credit transactions."}, "noTransactions": {"message": "No transactions."}, "chargeNoun": {"message": "Charge", "description": "Noun. A charge from a payment method."}, "refundNoun": {"message": "Refund", "description": "Noun. A refunded payment that was charged."}, "chargesStatement": {"message": "Any charges will appear on your statement as $STATEMENT_NAME$.", "placeholders": {"statement_name": {"content": "$1", "example": "BITWARDEN"}}}, "gbStorageAdd": {"message": "GB of storage to add"}, "gbStorageRemove": {"message": "GB of storage to remove"}, "storageAddNote": {"message": "Adding storage will result in adjustments to your billing totals and immediately charge your payment method on file. The first charge will be prorated for the remainder of the current billing cycle."}, "storageRemoveNote": {"message": "Removing storage will result in adjustments to your billing totals that will be prorated as credits toward your next billing charge."}, "adjustedStorage": {"message": "Adjusted $AMOUNT$ GB of storage.", "placeholders": {"amount": {"content": "$1", "example": "5"}}}, "contactSupport": {"message": "Contact customer support"}, "contactSupportShort": {"message": "Contact Support"}, "updatedPaymentMethod": {"message": "Updated payment method."}, "purchasePremium": {"message": "Purchase Premium"}, "licenseFile": {"message": "License file"}, "licenseFileDesc": {"message": "Your license file will be named something like $FILE_NAME$", "placeholders": {"file_name": {"content": "$1", "example": "bitwarden_premium_license.json"}}}, "uploadLicenseFilePremium": {"message": "To upgrade your account to a Premium membership you need to upload a valid license file."}, "uploadLicenseFileOrg": {"message": "To create an on-premises hosted organization you need to upload a valid license file."}, "accountEmailMustBeVerified": {"message": "Your account's email address must be verified."}, "newOrganizationDesc": {"message": "Organizations allow you to share parts of your vault with others as well as manage related users for a specific entity such as a family, small team, or large company."}, "generalInformation": {"message": "General information"}, "organizationName": {"message": "Organization name"}, "accountOwnedBusiness": {"message": "This account is owned by a business."}, "billingEmail": {"message": "Billing email"}, "businessName": {"message": "Business name"}, "chooseYourPlan": {"message": "Choose your plan"}, "users": {"message": "Users"}, "userSeats": {"message": "User seats"}, "additionalUserSeats": {"message": "Additional user seats"}, "userSeatsDesc": {"message": "# of user seats"}, "userSeatsAdditionalDesc": {"message": "Your plan comes with $BASE_SEATS$ user seats. You can add additional users for $SEAT_PRICE$ per user /month.", "placeholders": {"base_seats": {"content": "$1", "example": "5"}, "seat_price": {"content": "$2", "example": "$2.00"}}}, "userSeatsHowManyDesc": {"message": "How many user seats do you need? You can also add additional seats later if needed."}, "planNameFree": {"message": "Free", "description": "Free as in 'free beer'."}, "planDescFree": {"message": "For testing or personal users to share with $COUNT$ other user.", "placeholders": {"count": {"content": "$1", "example": "1"}}}, "planNameFamilies": {"message": "Families"}, "planDescFamilies": {"message": "For personal use, to share with family & friends."}, "planNameTeams": {"message": "Teams"}, "planDescTeams": {"message": "For businesses and other team organizations."}, "planNameTeamsStarter": {"message": "Teams Starter"}, "planNameEnterprise": {"message": "Enterprise"}, "planDescEnterprise": {"message": "For businesses and other large organizations."}, "freeForever": {"message": "Free forever"}, "includesXUsers": {"message": "includes $COUNT$ users", "placeholders": {"count": {"content": "$1", "example": "5"}}}, "additionalUsers": {"message": "Additional users"}, "costPerUser": {"message": "$COST$ per user", "placeholders": {"cost": {"content": "$1", "example": "$3"}}}, "limitedUsers": {"message": "Limited to $COUNT$ users (including you)", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "limitedCollections": {"message": "Limited to $COUNT$ collections", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "addShareLimitedUsers": {"message": "Add and share with up to $COUNT$ users", "placeholders": {"count": {"content": "$1", "example": "5"}}}, "addShareUnlimitedUsers": {"message": "Add and share with unlimited users"}, "createUnlimitedCollections": {"message": "Create unlimited collections"}, "gbEncryptedFileStorage": {"message": "$SIZE$ encrypted file storage", "placeholders": {"size": {"content": "$1", "example": "1 GB"}}}, "onPremHostingOptional": {"message": "On-premises hosting (optional)"}, "usersGetPremium": {"message": "Users get access to Premium features"}, "controlAccessWithGroups": {"message": "Control user access with groups"}, "syncUsersFromDirectory": {"message": "Sync your users and groups from a directory"}, "trackAuditLogs": {"message": "Track user actions with audit logs"}, "enforce2faDuo": {"message": "Enforce 2FA with <PERSON>"}, "priorityCustomerSupport": {"message": "Priority customer support"}, "xDayFreeTrial": {"message": "$COUNT$ day free trial, cancel anytime", "placeholders": {"count": {"content": "$1", "example": "7"}}}, "trialThankYou": {"message": "Thanks for signing up for Bitwarden for $PLAN$!", "placeholders": {"plan": {"content": "$1", "example": "Teams"}}}, "trialSecretsManagerThankYou": {"message": "Thanks for signing up for Bitwarden Secrets Manager for $PLAN$!", "placeholders": {"plan": {"content": "$1", "example": "Teams"}}}, "trialPaidInfoMessage": {"message": "Your $PLAN$ 7 day free trial will be converted to a paid subscription after 7 days.", "placeholders": {"plan": {"content": "$1", "example": "Teams"}}}, "trialConfirmationEmail": {"message": "We've sent a confirmation email to your team's billing email at "}, "monthly": {"message": "Monthly"}, "annually": {"message": "Annually"}, "annual": {"message": "Annual"}, "basePrice": {"message": "Base price"}, "organizationCreated": {"message": "Organization created"}, "organizationReadyToGo": {"message": "Your new organization is ready to go!"}, "organizationUpgraded": {"message": "Organization upgraded"}, "leave": {"message": "Leave"}, "leaveOrganizationConfirmation": {"message": "Are you sure you want to leave this organization?"}, "leftOrganization": {"message": "You left the organization"}, "defaultCollection": {"message": "Default collection"}, "myItems": {"message": "My Items"}, "getHelp": {"message": "Get help"}, "getApps": {"message": "Get the apps"}, "loggedInAs": {"message": "Logged in as"}, "eventLogs": {"message": "Event logs"}, "people": {"message": "People"}, "policies": {"message": "Policies"}, "singleSignOn": {"message": "Single sign-on"}, "editPolicy": {"message": "Edit policy"}, "groups": {"message": "Groups"}, "newGroup": {"message": "New group"}, "addGroup": {"message": "Add group"}, "editGroup": {"message": "Edit group"}, "deleteGroupConfirmation": {"message": "Are you sure you want to delete this group?"}, "deleteMultipleGroupsConfirmation": {"message": "Are you sure you want to delete the following $QUANTITY$ group(s)?", "placeholders": {"quantity": {"content": "$1", "example": "3"}}}, "removeUserConfirmation": {"message": "Are you sure you want to remove this user?"}, "removeOrgUserConfirmation": {"message": "When a member is removed, they no longer have access to organization data and this action is irreversible. To add the member back to the organization, they must be invited and onboarded again."}, "revokeUserConfirmation": {"message": "When a member is revoked, they no longer have access to organization data. To quickly restore member access, go to the Revoked tab."}, "removeUserConfirmationKeyConnector": {"message": "Warning! This user requires Key Connector to manage their encryption. Removing this user from your organization will permanently deactivate their account. This action cannot be undone. Do you want to proceed?"}, "externalId": {"message": "External ID"}, "externalIdDesc": {"message": "External ID is an unencrypted reference used by the Bitwarden Directory Connector and API."}, "ssoExternalId": {"message": "SSO External ID"}, "ssoExternalIdDesc": {"message": "SSO External ID is an unencrypted reference between Bitwarden and your configured SSO provider."}, "nestCollectionUnder": {"message": "Nest collection under"}, "accessControl": {"message": "Access control"}, "readOnly": {"message": "Read only"}, "newCollection": {"message": "New collection"}, "addCollection": {"message": "Add collection"}, "editCollection": {"message": "Edit collection"}, "collectionInfo": {"message": "Collection info"}, "deleteCollectionConfirmation": {"message": "Are you sure you want to delete this collection?"}, "editMember": {"message": "Edit member"}, "fieldOnTabRequiresAttention": {"message": "A field on the '$TAB$' tab requires your attention.", "placeholders": {"tab": {"content": "$1", "example": "Collection info"}}}, "inviteUserDesc": {"message": "Invite a new user to your organization by entering their Bitwarden account email address below. If they do not have a Bitwarden account already, they will be prompted to create a new account."}, "inviteMultipleEmailDesc": {"message": "Enter up to $COUNT$ emails by separating with a comma.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inviteSingleEmailDesc": {"message": "You have 1 invite remaining."}, "inviteZeroEmailDesc": {"message": "You have 0 invites remaining."}, "userUsingTwoStep": {"message": "This user is using two-step login to protect their account."}, "search": {"message": "Search"}, "invited": {"message": "Invited"}, "confirmed": {"message": "Confirmed"}, "clientOwnerEmail": {"message": "Client owner email"}, "owner": {"message": "Owner"}, "ownerDesc": {"message": "Manage all aspects of your organization, including billing and subscriptions"}, "clientOwnerDesc": {"message": "This user should be independent of the Provider. If the Provider is disassociated with the organization, this user will maintain ownership of the organization."}, "admin": {"message": "Admin"}, "adminDesc": {"message": "Manage organization access, all collections, members, reporting, and security settings"}, "user": {"message": "User"}, "userDesc": {"message": "Access and add items to assigned collections"}, "all": {"message": "All"}, "addAccess": {"message": "Add Access"}, "addAccessFilter": {"message": "Add Access Filter"}, "refresh": {"message": "Refresh"}, "timestamp": {"message": "Timestamp"}, "event": {"message": "Event"}, "unknown": {"message": "Unknown"}, "loadMore": {"message": "Load more"}, "mobile": {"message": "Mobile", "description": "Mobile app"}, "extension": {"message": "Extension", "description": "Browser extension/addon"}, "desktop": {"message": "Desktop", "description": "Desktop app"}, "webVault": {"message": "Web vault"}, "cli": {"message": "CLI"}, "bitWebVault": {"message": "Bitwarden Web vault"}, "bitSecretsManager": {"message": "Bitwarden Secrets Manager"}, "loggedIn": {"message": "Logged in"}, "changedPassword": {"message": "Changed account password"}, "enabledUpdated2fa": {"message": "Two-step login saved"}, "disabled2fa": {"message": "Two-step login turned off"}, "recovered2fa": {"message": "Recovered account from two-step login."}, "failedLogin": {"message": "Login attempt failed with incorrect password."}, "failedLogin2fa": {"message": "<PERSON><PERSON> attempt failed with incorrect two-step login."}, "incorrectPassword": {"message": "Incorrect password"}, "incorrectCode": {"message": "Incorrect code"}, "incorrectPin": {"message": "Incorrect PIN"}, "pin": {"message": "PIN", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "exportedVault": {"message": "Vault exported"}, "exportedOrganizationVault": {"message": "Exported organization vault."}, "editedOrgSettings": {"message": "Edited organization settings."}, "createdItemId": {"message": "Created item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "editedItemId": {"message": "Edited item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "deletedItemId": {"message": "Sent item $ID$ to trash.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "movedItemIdToOrg": {"message": "Moved item $ID$ to an organization.", "placeholders": {"id": {"content": "$1", "example": "'Google'"}}}, "viewAllLogInOptions": {"message": "View all log in options"}, "viewAllLoginOptions": {"message": "View all log in options"}, "viewedItemId": {"message": "Viewed item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "viewedPasswordItemId": {"message": "Viewed password for item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "viewedHiddenFieldItemId": {"message": "Viewed hidden field for item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "viewedCardNumberItemId": {"message": "Viewed Card Number for item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Unique ID"}}}, "viewedSecurityCodeItemId": {"message": "Viewed security code for item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "viewCollectionWithName": {"message": "View collection - $NAME$", "placeholders": {"name": {"content": "$1", "example": "Collection1"}}}, "editItemWithName": {"message": "Edit item - $NAME$", "placeholders": {"name": {"content": "$1", "example": "Google Login"}}}, "copiedPasswordItemId": {"message": "Copied password for item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "copiedHiddenFieldItemId": {"message": "Copied hidden field for item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "copiedSecurityCodeItemId": {"message": "Copied security code for item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "autofilledItemId": {"message": "Auto-filled item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "createdCollectionId": {"message": "Created collection $ID$.", "placeholders": {"id": {"content": "$1", "example": "Server Passwords"}}}, "editedCollectionId": {"message": "Edited collection $ID$.", "placeholders": {"id": {"content": "$1", "example": "Server Passwords"}}}, "deletedCollections": {"message": "Deleted collections"}, "deletedCollectionId": {"message": "Deleted collection $ID$.", "placeholders": {"id": {"content": "$1", "example": "Server Passwords"}}}, "editedPolicyId": {"message": "Edited policy $ID$.", "placeholders": {"id": {"content": "$1", "example": "Master Password"}}}, "createdGroupId": {"message": "Created group $ID$.", "placeholders": {"id": {"content": "$1", "example": "Developers"}}}, "editedGroupId": {"message": "Edited group $ID$.", "placeholders": {"id": {"content": "$1", "example": "Developers"}}}, "deletedGroupId": {"message": "Deleted group $ID$.", "placeholders": {"id": {"content": "$1", "example": "Developers"}}}, "deletedManyGroups": {"message": "Deleted $QUANTITY$ group(s).", "placeholders": {"quantity": {"content": "$1", "example": "3"}}}, "removedUserId": {"message": "Removed user $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "removeUserIdAccess": {"message": "Remove $ID$ access", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "revokedUserId": {"message": "Revoked organization access for $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "restoredUserId": {"message": "Restored organization access for $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "revokeUserId": {"message": "Revoke $ID$ access", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "createdAttachmentForItem": {"message": "Created attachment for item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "deletedAttachmentForItem": {"message": "Deleted attachment for item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "editedCollectionsForItem": {"message": "Edited collections for item $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "invitedUserId": {"message": "Invited user $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "confirmedUserId": {"message": "Confirmed user $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "editedUserId": {"message": "Edited user $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "editedGroupsForUser": {"message": "Edited groups for user $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "unlinkedSso": {"message": "Unlinked SSO."}, "unlinkedSsoUser": {"message": "Unlinked SSO for user $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "createdOrganizationId": {"message": "Created organization $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "addedOrganizationId": {"message": "Added organization $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "removedOrganizationId": {"message": "Removed organization $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "accessedClientVault": {"message": "Accessed $ID$ organization vault.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "device": {"message": "<PERSON><PERSON>"}, "loginStatus": {"message": "Login status"}, "firstLogin": {"message": "First login"}, "trusted": {"message": "Trusted"}, "needsApproval": {"message": "Needs approval"}, "areYouTryingtoLogin": {"message": "Are you trying to log in?"}, "logInAttemptBy": {"message": "Login attempt by $EMAIL$", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "deviceType": {"message": "Device Type"}, "ipAddress": {"message": "IP Address"}, "confirmLogIn": {"message": "Confirm login"}, "denyLogIn": {"message": "Deny login"}, "thisRequestIsNoLongerValid": {"message": "This request is no longer valid."}, "logInConfirmedForEmailOnDevice": {"message": "<PERSON><PERSON> confirmed for $EMAIL$ on $DEVICE$", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "device": {"content": "$2", "example": "iOS"}}}, "youDeniedALogInAttemptFromAnotherDevice": {"message": "You denied a login attempt from another device. If this really was you, try to log in with the device again."}, "loginRequestHasAlreadyExpired": {"message": "Login request has already expired."}, "justNow": {"message": "Just now"}, "requestedXMinutesAgo": {"message": "Requested $MINUTES$ minutes ago", "placeholders": {"minutes": {"content": "$1", "example": "5"}}}, "creatingAccountOn": {"message": "Creating account on"}, "checkYourEmail": {"message": "Check your email"}, "followTheLinkInTheEmailSentTo": {"message": "Follow the link in the email sent to"}, "andContinueCreatingYourAccount": {"message": "and continue creating your account."}, "noEmail": {"message": "No email?"}, "goBack": {"message": "Go back"}, "toEditYourEmailAddress": {"message": "to edit your email address."}, "view": {"message": "View"}, "invalidDateRange": {"message": "Invalid date range."}, "errorOccurred": {"message": "An error has occurred."}, "userAccess": {"message": "User access"}, "userType": {"message": "User type"}, "groupAccess": {"message": "Group access"}, "groupAccessUserDesc": {"message": "Grant member access to collections by adding them to 1 or more groups."}, "invitedUsers": {"message": "User(s) invited"}, "resendInvitation": {"message": "Resend invitation"}, "resendEmail": {"message": "Resend email"}, "hasBeenReinvited": {"message": "$USER$ reinvited", "placeholders": {"user": {"content": "$1", "example": "<PERSON>"}}}, "confirm": {"message": "Confirm"}, "confirmUser": {"message": "Confirm user"}, "hasBeenConfirmed": {"message": "$USER$ confirmed.", "placeholders": {"user": {"content": "$1", "example": "<PERSON>"}}}, "confirmUsers": {"message": "Confirm members"}, "usersNeedConfirmed": {"message": "You have members that have accepted their invitation, but still need to be confirmed. Members will not have access to the organization until they are confirmed."}, "startDate": {"message": "Start date"}, "endDate": {"message": "End date"}, "verifyEmail": {"message": "Verify email"}, "verifyEmailDesc": {"message": "Verify your account's email address to unlock access to all features."}, "verifyEmailFirst": {"message": "Your account's email address first must be verified."}, "checkInboxForVerification": {"message": "Check your email inbox for a verification link."}, "emailVerified": {"message": "Account email verified"}, "emailVerifiedV2": {"message": "Email verified"}, "emailVerifiedFailed": {"message": "Unable to verify your email. Try sending a new verification email."}, "emailVerificationRequired": {"message": "Email verification required"}, "emailVerificationRequiredDesc": {"message": "You must verify your email to use this feature."}, "updateBrowser": {"message": "Update browser"}, "generatingYourRiskInsights": {"message": "Generating your Risk Insights..."}, "updateBrowserDesc": {"message": "You are using an unsupported web browser. The web vault may not function properly."}, "youHaveAPendingLoginRequest": {"message": "You have a pending login request from another device."}, "reviewLoginRequest": {"message": "Review login request"}, "freeTrialEndPromptCount": {"message": "Your free trial ends in $COUNT$ days.", "placeholders": {"count": {"content": "$1", "example": "remaining days"}}}, "freeTrialEndPromptMultipleDays": {"message": "$ORGANIZATION$, your free trial ends in $COUNT$ days.", "placeholders": {"count": {"content": "$2", "example": "remaining days"}, "organization": {"content": "$1", "example": "organization name"}}}, "freeTrialEndPromptTomorrow": {"message": "$ORGANIZATION$, your free trial ends tomorrow.", "placeholders": {"organization": {"content": "$1", "example": "organization name"}}}, "freeTrialEndPromptTomorrowNoOrgName": {"message": "Your free trial ends tomorrow."}, "freeTrialEndPromptToday": {"message": "$ORGANIZATION$, your free trial ends today.", "placeholders": {"organization": {"content": "$1", "example": "organization name"}}}, "freeTrialEndingTodayWithoutOrgName": {"message": "Your free trial ends today."}, "clickHereToAddPaymentMethod": {"message": "Click here to add a payment method."}, "joinOrganization": {"message": "Join organization"}, "joinOrganizationName": {"message": "Join $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "joinOrganizationDesc": {"message": "You've been invited to join the organization listed above. To accept the invitation, you need to log in or create a new Bitwarden account."}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Finish joining this organization by setting a master password."}, "inviteAccepted": {"message": "Invitation accepted"}, "inviteAcceptedDesc": {"message": "You can access this organization once an administrator confirms your membership. We'll send you an email when that happens."}, "inviteInitAcceptedDesc": {"message": "You can now access this organization."}, "inviteAcceptFailed": {"message": "Unable to accept invitation. Ask an organization admin to send a new invitation."}, "inviteAcceptFailedShort": {"message": "Unable to accept invitation. $DESCRIPTION$", "placeholders": {"description": {"content": "$1", "example": "You must set up 2FA on your user account before you can join this organization."}}}, "rememberEmail": {"message": "Remember email"}, "recoverAccountTwoStepDesc": {"message": "If you cannot access your account through your normal two-step login methods, you can use your two-step login recovery code to turn off all two-step providers on your account."}, "logInBelowUsingYourSingleUseRecoveryCode": {"message": "Log in below using your single-use recovery code. This will turn off all two-step providers on your account."}, "recoverAccountTwoStep": {"message": "Recover account two-step login"}, "twoStepRecoverDisabled": {"message": "Two-step login turned off on your account."}, "learnMore": {"message": "Learn more"}, "deleteRecoverDesc": {"message": "Enter your email address below to recover and delete your account."}, "deleteRecoverEmailSent": {"message": "If your account exists, we've sent you an email with further instructions."}, "deleteRecoverConfirmDesc": {"message": "You have requested to delete your Bitwarden account. Use the button below to confirm."}, "deleteRecoverOrgConfirmDesc": {"message": "You have requested to delete your Bitwarden organization."}, "myOrganization": {"message": "My organization"}, "organizationInfo": {"message": "Organization info"}, "deleteOrganization": {"message": "Delete organization"}, "deletingOrganizationContentWarning": {"message": "Enter the master password to confirm deletion of $ORGANIZATION$ and all associated data. Vault data in $ORGANIZATION$ includes:", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "deletingOrganizationActiveUserAccountsWarning": {"message": "User accounts will remain active after deletion but will no longer be associated to this organization."}, "deletingOrganizationIsPermanentWarning": {"message": "Deleting $ORGANIZATION$ is permanent and irreversible.", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "organizationDeleted": {"message": "Organization deleted"}, "organizationDeletedDesc": {"message": "The organization and all associated data has been deleted."}, "organizationUpdated": {"message": "Organization saved"}, "taxInformation": {"message": "Tax information"}, "taxInformationDesc": {"message": "For customers within the US, ZIP code is required to satisfy sales tax requirements, for other countries you may optionally provide a tax identification number (VAT/GST) and/or address to appear on your invoices."}, "billingPlan": {"message": "Plan", "description": "A billing plan/package. For example: Families, Teams, Enterprise, etc."}, "changeBillingPlan": {"message": "Upgrade plan", "description": "A billing plan/package. For example: Families, Teams, Enterprise, etc."}, "changeBillingPlanUpgrade": {"message": "Upgrade your account to another plan by providing the information below. Please ensure that you have an active payment method added to the account.", "description": "A billing plan/package. For example: Families, Teams, Enterprise, etc."}, "invoiceNumber": {"message": "Invoice #$NUMBER$", "description": "ex. Invoice #79C66F0-0001", "placeholders": {"number": {"content": "$1", "example": "79C66F0-0001"}}}, "viewInvoice": {"message": "View invoice"}, "downloadInvoice": {"message": "Download invoice"}, "verifyBankAccount": {"message": "Verify bank account"}, "verifyBankAccountDesc": {"message": "We have made two micro-deposits to your bank account (it may take 1-2 business days to show up). Enter these amounts to verify the bank account."}, "verifyBankAccountInitialDesc": {"message": "Payment with a bank account is only available to customers in the United States. You will be required to verify your bank account. We will make two micro-deposits within the next 1-2 business days. Enter these amounts on the organization's billing page to verify the bank account."}, "verifyBankAccountFailureWarning": {"message": "Failure to verify the bank account will result in a missed payment and your subscription being suspended."}, "verifiedBankAccount": {"message": "Bank account verified"}, "bankAccount": {"message": "Bank account"}, "amountX": {"message": "Amount $COUNT$", "description": "Used in bank account verification of micro-deposits. Amount, as in a currency amount. Ex. Amount 1 is $2.00, Amount 2 is $1.50", "placeholders": {"count": {"content": "$1", "example": "1"}}}, "routingNumber": {"message": "Routing number", "description": "Bank account routing number"}, "accountNumber": {"message": "Account number"}, "accountHolderName": {"message": "Account holder name"}, "bankAccountType": {"message": "Account type"}, "bankAccountTypeCompany": {"message": "Company (business)"}, "bankAccountTypeIndividual": {"message": "Individual (personal)"}, "enterInstallationId": {"message": "Enter your installation id"}, "limitSubscriptionDesc": {"message": "Set a seat limit for your subscription. Once this limit is reached, you will not be able to invite new members."}, "limitSmSubscriptionDesc": {"message": "Set a seat limit for your Secrets Manager subscription. Once this limit is reached, you will not be able to invite new members."}, "maxSeatLimit": {"message": "Seat Limit (optional)", "description": "Upper limit of seats to allow through autoscaling"}, "maxSeatCost": {"message": "Max potential seat cost"}, "addSeats": {"message": "Add seats", "description": "Seat = User Seat"}, "removeSeats": {"message": "Remove seats", "description": "Seat = User Seat"}, "subscriptionDesc": {"message": "Adjustments to your subscription will result in prorated changes to your billing totals. If newly invited users exceed your subscription seats, you will immediately receive a prorated charge for the additional users."}, "subscriptionUserSeats": {"message": "Your subscription allows for a total of $COUNT$ members.", "placeholders": {"count": {"content": "$1", "example": "50"}}}, "limitSubscription": {"message": "Limit subscription (optional)"}, "subscriptionSeats": {"message": "Subscription seats"}, "subscriptionUpdated": {"message": "Subscription updated"}, "subscribedToSecretsManager": {"message": "Subscription updated. You now have access to Secrets Manager."}, "additionalOptions": {"message": "Additional options"}, "additionalOptionsDesc": {"message": "For additional help in managing your subscription, please contact Customer Support."}, "subscriptionUserSeatsUnlimitedAutoscale": {"message": "Adjustments to your subscription will result in prorated changes to your billing totals. If newly invited members exceed your subscription seats, you will immediately receive a prorated charge for the additional members."}, "smStandaloneTrialSeatCountUpdateMessageFragment1": {"message": "If you want to add additional"}, "smStandaloneTrialSeatCountUpdateMessageFragment2": {"message": "seats without the bundled offer, please contact"}, "subscriptionUserSeatsLimitedAutoscale": {"message": "Adjustments to your subscription will result in prorated changes to your billing totals. If newly invited members exceed your subscription seats, you will immediately receive a prorated charge for the additional members until your $MAX$ seat limit is reached.", "placeholders": {"max": {"content": "$1", "example": "50"}}}, "subscriptionUserSeatsWithoutAdditionalSeatsOption": {"message": "You can invite up to $COUNT$ members for no additional charge. Contact Customer Support to upgrade your plan and invite more members.", "placeholders": {"count": {"content": "$1", "example": "10"}}}, "subscriptionFreePlan": {"message": "You cannot invite more than $COUNT$ members without upgrading your plan.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "subscriptionUpgrade": {"message": "You cannot invite more than $COUNT$ members without upgrading your plan.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "subscriptionSponsoredFamiliesPlan": {"message": "Your subscription allows for a total of $COUNT$ members. Your plan is sponsored and billed to an external organization.", "placeholders": {"count": {"content": "$1", "example": "6"}}}, "subscriptionMaxReached": {"message": "Adjustments to your subscription will result in prorated changes to your billing totals. You cannot invite more than $COUNT$ members without increasing your subscription seats.", "placeholders": {"count": {"content": "$1", "example": "50"}}}, "subscriptionSeatMaxReached": {"message": "You cannot invite more than $COUNT$ members without increasing your subscription seats.", "placeholders": {"count": {"content": "$1", "example": "50"}}}, "seatsToAdd": {"message": "Seats to add"}, "seatsToRemove": {"message": "Seats to remove"}, "seatsAddNote": {"message": "Adding user seats will result in adjustments to your billing totals and immediately charge your payment method on file. The first charge will be prorated for the remainder of the current billing cycle."}, "seatsRemoveNote": {"message": "Removing user seats will result in adjustments to your billing totals that will be prorated as credits toward your next billing charge."}, "adjustedSeats": {"message": "Adjusted $AMOUNT$ user seats.", "placeholders": {"amount": {"content": "$1", "example": "15"}}}, "editFieldLabel": {"message": "Edit $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Reorder $LABEL$. Use arrow key to move item up or down.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderFieldUp": {"message": "$LABEL$ moved up, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "reorderFieldDown": {"message": "$LABEL$ moved down, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "updateEncryptionKeyWarning": {"message": "After updating your encryption key, you are required to log out and back in to all Bitwarden applications that you are currently using (such as the mobile app or browser extensions). Failure to log out and back in (which downloads your new encryption key) may result in data corruption. We will attempt to log you out automatically, however, it may be delayed."}, "updateEncryptionKeyAccountExportWarning": {"message": "Any account restricted exports you have saved will become invalid."}, "legacyEncryptionUnsupported": {"message": "Legacy encryption is no longer supported. Please contact support to recover your account."}, "subscription": {"message": "Subscription"}, "loading": {"message": "Loading"}, "upgrade": {"message": "Upgrade"}, "upgradeOrganization": {"message": "Upgrade organization"}, "upgradeOrganizationDesc": {"message": "This feature is not available for free organizations. Switch to a paid plan to unlock more features."}, "createOrganizationStep1": {"message": "Create organization: Step 1"}, "createOrganizationCreatePersonalAccount": {"message": "Before creating your organization, you first need to create a free personal account."}, "refunded": {"message": "Refunded"}, "nothingSelected": {"message": "You have not selected anything."}, "receiveMarketingEmailsV2": {"message": "Get advice, announcements, and research opportunities from Bitwarden in your inbox."}, "unsubscribe": {"message": "Unsubscribe"}, "atAnyTime": {"message": "at any time."}, "byContinuingYouAgreeToThe": {"message": "By continuing, you agree to the"}, "and": {"message": "and"}, "acceptPolicies": {"message": "By checking this box you agree to the following:"}, "acceptPoliciesRequired": {"message": "Terms of Service and Privacy Policy have not been acknowledged."}, "termsOfService": {"message": "Terms of Service"}, "privacyPolicy": {"message": "Privacy Policy"}, "filters": {"message": "Filters"}, "vaultTimeout": {"message": "Vault timeout"}, "vaultTimeout1": {"message": "Timeout"}, "vaultTimeoutDesc": {"message": "Choose when your vault will take the vault timeout action."}, "vaultTimeoutLogoutDesc": {"message": "Choose when your vault will be logged out."}, "oneMinute": {"message": "1 minute"}, "fiveMinutes": {"message": "5 minutes"}, "fifteenMinutes": {"message": "15 minutes"}, "thirtyMinutes": {"message": "30 minutes"}, "oneHour": {"message": "1 hour"}, "fourHours": {"message": "4 hours"}, "onRefresh": {"message": "On browser refresh"}, "dateUpdated": {"message": "Updated", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "Created", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "Password updated", "description": "ex. Date this password was updated"}, "organizationIsDisabled": {"message": "Organization suspended"}, "secretsAccessSuspended": {"message": "Suspended organizations cannot be accessed. Please contact your organization owner for assistance."}, "secretsCannotCreate": {"message": "Secrets cannot be created in suspended organizations. Please contact your organization owner for assistance."}, "projectsCannotCreate": {"message": "Projects cannot be created in suspended organizations. Please contact your organization owner for assistance."}, "serviceAccountsCannotCreate": {"message": "Service accounts cannot be created in suspended organizations. Please contact your organization owner for assistance."}, "disabledOrganizationFilterError": {"message": "Items in suspended organizations cannot be accessed. Contact your organization owner for assistance."}, "licenseIsExpired": {"message": "License is expired."}, "updatedUsers": {"message": "Updated users"}, "selected": {"message": "Selected"}, "recommended": {"message": "Recommended"}, "ownership": {"message": "Ownership"}, "whoOwnsThisItem": {"message": "Who owns this item?"}, "strong": {"message": "Strong", "description": "ex. A strong password. Scale: Very Weak -> Weak -> Good -> Strong"}, "good": {"message": "Good", "description": "ex. A good password. Scale: Very Weak -> Weak -> Good -> Strong"}, "weak": {"message": "Weak", "description": "ex. A weak password. Scale: Very Weak -> Weak -> Good -> Strong"}, "veryWeak": {"message": "Very Weak", "description": "ex. A very weak password. Scale: Very Weak -> Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "Weak master password"}, "weakMasterPasswordDesc": {"message": "Weak password identified. Use a strong password to protect your account. Are you sure you want to use a weak password?"}, "rotateAccountEncKey": {"message": "Also rotate my account's encryption key"}, "rotateEncKeyTitle": {"message": "Rotate encryption key"}, "rotateEncKeyConfirmation": {"message": "Are you sure you want to rotate your account's encryption key?"}, "attachmentsNeedFix": {"message": "This item has old file attachments that need to be fixed."}, "attachmentFixDescription": {"message": "This attachment uses outdated encryption. Select 'Fix' to download, re-encrypt, and re-upload the attachment."}, "fix": {"message": "Fix", "description": "This is a verb. ex. 'Fix The Car'"}, "oldAttachmentsNeedFixDesc": {"message": "There are old file attachments in your vault that need to be fixed before you can rotate your account's encryption key."}, "yourAccountsFingerprint": {"message": "Your account's fingerprint phrase", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "fingerprintEnsureIntegrityVerify": {"message": "To ensure the integrity of your encryption keys, please verify the user's fingerprint phrase before continuing.", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "fingerprintMatchInfo": {"message": "Please make sure your vault is unlocked and Fingerprint phrase matches the other device."}, "fingerprintPhraseHeader": {"message": "Fingerprint phrase"}, "dontAskFingerprintAgain": {"message": "Never prompt to verify fingerprint phrases for invited users (not recommended)", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "You will be notified once the request is approved"}, "free": {"message": "Free", "description": "Free, as in 'Free beer'"}, "apiKey": {"message": "API Key"}, "apiKeyDesc": {"message": "Your API key can be used to authenticate to the Bitwarden public API."}, "apiKeyRotateDesc": {"message": "Rotating the API key will invalidate the previous key. You can rotate your API key if you believe that the current key is no longer safe to use."}, "apiKeyWarning": {"message": "Your API key has full access to the organization. It should be kept secret."}, "userApiKeyDesc": {"message": "Your API key can be used to authenticate in the Bitwarden CLI."}, "userApiKeyWarning": {"message": "Your API key is an alternative authentication mechanism. It should be kept secret."}, "oauth2ClientCredentials": {"message": "OAuth 2.0 Client Credentials", "description": "'OAuth 2.0' is a programming protocol. It should probably not be translated."}, "viewApiKey": {"message": "View API key"}, "rotateApiKey": {"message": "Rotate API key"}, "selectOneCollection": {"message": "You must select at least one collection."}, "couldNotChargeCardPayInvoice": {"message": "We were not able to charge your card. Please view and pay the unpaid invoice listed below."}, "minLength": {"message": "Minimum length"}, "clone": {"message": "<PERSON><PERSON>"}, "masterPassPolicyTitle": {"message": "Master password requirements"}, "masterPassPolicyDesc": {"message": "Set requirements for master password strength."}, "passwordStrengthScore": {"message": "Password strength score $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "twoStepLoginPolicyTitle": {"message": "Require two-step login"}, "twoStepLoginPolicyDesc": {"message": "Require members to set up two-step login."}, "twoStepLoginPolicyWarning": {"message": "Organization members who are not owners or admins and do not have two-step login setup for their account will be removed from the organization and will receive an email notifying them about the change."}, "twoStepLoginPolicyUserWarning": {"message": "You are a member of an organization that requires two-step login to be setup on your user account. If you turn off all two-step login providers you will be automatically removed from these organizations."}, "passwordGeneratorPolicyDesc": {"message": "Set requirements for password generator."}, "masterPasswordPolicyInEffect": {"message": "One or more organization policies require your master password to meet the following requirements:"}, "policyInEffectMinComplexity": {"message": "Minimum complexity score of $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "Minimum length of $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "Contain one or more uppercase characters"}, "policyInEffectLowercase": {"message": "Contain one or more lowercase characters"}, "policyInEffectNumbers": {"message": "Contain one or more numbers"}, "policyInEffectSpecial": {"message": "Contain one or more of the following special characters $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "Your new master password does not meet the policy requirements."}, "minimumNumberOfWords": {"message": "Minimum number of words"}, "overridePasswordTypePolicy": {"message": "Password Type", "description": "Name of the password generator policy that overrides the user's password/passphrase selection."}, "userPreference": {"message": "User preference"}, "vaultTimeoutAction": {"message": "Vault timeout action"}, "vaultTimeoutActionLockDesc": {"message": "Master password or other unlock method is required to access your vault again."}, "vaultTimeoutActionLogOutDesc": {"message": "Re-authentication is required to access your vault again."}, "lock": {"message": "Lock", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "Trash", "description": "Noun: A special folder for holding deleted items that have not yet been permanently deleted"}, "searchTrash": {"message": "Search trash"}, "permanentlyDelete": {"message": "Permanently delete"}, "permanentlyDeleteSelected": {"message": "Permanently delete selected"}, "permanentlyDeleteItem": {"message": "Permanently delete item"}, "permanentlyDeleteItemConfirmation": {"message": "Are you sure you want to permanently delete this item?"}, "permanentlyDeletedItem": {"message": "Item permanently deleted"}, "permanentlyDeletedItems": {"message": "Items permanently deleted"}, "permanentlyDeleteSelectedItemsDesc": {"message": "You have selected $COUNT$ item(s) to permanently delete. Are you sure you want to permanently delete all of these items?", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "permanentlyDeletedItemId": {"message": "Item $ID$ permanently deleted", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "restore": {"message": "Rest<PERSON>"}, "restoreSelected": {"message": "<PERSON><PERSON> selected"}, "restoredItem": {"message": "Item restored"}, "restoredItems": {"message": "Items restored"}, "restoredItemId": {"message": "Item $ID$ restored", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "vaultTimeoutLogOutConfirmation": {"message": "Logging out will remove all access to your vault and requires online authentication after the timeout period. Are you sure you want to use this setting?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "Timeout action confirmation"}, "hidePasswords": {"message": "Hide passwords"}, "countryPostalCodeRequiredDesc": {"message": "We require this information for calculating sales tax and financial reporting only."}, "includeVAT": {"message": "Include VAT/GST Information (optional)"}, "taxIdNumber": {"message": "VAT/GST Tax ID"}, "taxInfoUpdated": {"message": "Tax information updated."}, "setMasterPassword": {"message": "Set master password"}, "identifier": {"message": "Identifier"}, "organizationIdentifier": {"message": "Organization identifier"}, "ssoLogInWithOrgIdentifier": {"message": "Log in using your organization's single sign-on portal. Please enter your organization's SSO identifier to begin."}, "singleSignOnEnterOrgIdentifier": {"message": "Enter your organization's SSO identifier to begin"}, "singleSignOnEnterOrgIdentifierText": {"message": "To log in with your SSO provider, enter your organization's SSO identifier to begin. You may need to enter this SSO identifier when you log in from a new device."}, "enterpriseSingleSignOn": {"message": "Enterprise single sign-on"}, "ssoHandOff": {"message": "You may now close this tab and continue in the extension."}, "youSuccessfullyLoggedIn": {"message": "You successfully logged in"}, "thisWindowWillCloseIn5Seconds": {"message": "This window will automatically close in 5 seconds"}, "youMayCloseThisWindow": {"message": "You may close this window"}, "includeAllTeamsFeatures": {"message": "All Teams features, plus:"}, "includeAllTeamsStarterFeatures": {"message": "All Teams Starter features, plus:"}, "chooseMonthlyOrAnnualBilling": {"message": "Choose monthly or annual billing"}, "abilityToAddMoreThanNMembers": {"message": "Ability to add more than $COUNT$ members", "placeholders": {"count": {"content": "$1", "example": "10"}}}, "includeSsoAuthentication": {"message": "SSO Authentication via SAML2.0 and OpenID Connect"}, "includeEnterprisePolicies": {"message": "Enterprise policies"}, "ssoValidationFailed": {"message": "SSO validation failed"}, "ssoIdentifierRequired": {"message": "Organization SSO identifier is required."}, "ssoIdentifier": {"message": "SSO identifier"}, "ssoIdentifierHintPartOne": {"message": "Provide this ID to your members to login with SSO. To bypass this step, set up ", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Provide this ID to your members to login with SSO. To bypass this step, set up Domain verification'"}, "unlinkSso": {"message": "Unlink SSO"}, "unlinkSsoConfirmation": {"message": "Are you sure you want to unlink SSO for this organization?"}, "linkSso": {"message": "Link SSO"}, "singleOrg": {"message": "Single organization"}, "singleOrgDesc": {"message": "Restrict members from joining other organizations."}, "singleOrgPolicyDesc": {"message": "Restrict members from joining other organizations. This policy is required for organizations that have enabled domain verification."}, "singleOrgBlockCreateMessage": {"message": "Your current organization has a policy that does not allow you to join more than one organization. Please contact your organization admins or sign up from a different Bitwarden account."}, "singleOrgPolicyMemberWarning": {"message": "Non-compliant members will be placed in revoked status until they leave all other organizations. Administrators are exempt and can restore members once compliance is met."}, "requireSso": {"message": "Require single sign-on authentication"}, "requireSsoPolicyDesc": {"message": "Require members to log in with the Enterprise single sign-on method."}, "prerequisite": {"message": "Prerequisite"}, "requireSsoPolicyReq": {"message": "The single organization Enterprise policy must be turned on before activating this policy."}, "requireSsoPolicyReqError": {"message": "Single organization policy not set up."}, "requireSsoExemption": {"message": "Organization owners and admins are exempt from this policy's enforcement."}, "limitSendViews": {"message": "Limit views"}, "limitSendViewsHint": {"message": "No one can view this Send after the limit is reached.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ views left", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "sendDetails": {"message": "Send details", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeTextToShare": {"message": "Text to share"}, "sendTypeFile": {"message": "File"}, "sendTypeText": {"message": "Text"}, "sendPasswordDescV3": {"message": "Add an optional password for recipients to access this Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "New Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "Edit Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "Send saved", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "Send saved", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletedSend": {"message": "Send deleted", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSend": {"message": "Delete Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Are you sure you want to permanently delete this Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "Deletion date"}, "deletionDateDescV2": {"message": "The Send will be permanently deleted on this date.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "Expiration date"}, "expirationDateDesc": {"message": "If set, access to this Send will expire on the specified date and time.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "maxAccessCount": {"message": "Maximum access count"}, "disabled": {"message": "Disabled"}, "revoked": {"message": "Revoked"}, "sendLink": {"message": "Send link", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "copyLink": {"message": "Copy link"}, "copySendLink": {"message": "Copy Send link", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "Remove password"}, "removedPassword": {"message": "Password removed"}, "removePasswordConfirmation": {"message": "Are you sure you want to remove the password?"}, "allSends": {"message": "All Sends"}, "maxAccessCountReached": {"message": "Max access count reached", "description": "This text will be displayed after a Send has been accessed the maximum amount of times."}, "pendingDeletion": {"message": "Pending deletion"}, "hideTextByDefault": {"message": "Hide text by default"}, "expired": {"message": "Expired"}, "searchSends": {"message": "Search Sends", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendProtectedPassword": {"message": "This Send is protected with a password. Please type the password below to continue.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendProtectedPasswordDontKnow": {"message": "Don't know the password? Ask the sender for the password needed to access this Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendHiddenByDefault": {"message": "This Send is hidden by default. You can toggle its visibility using the button below.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "downloadAttachments": {"message": "Download attachments"}, "sendAccessUnavailable": {"message": "The Send you are trying to access does not exist or is no longer available.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "missingSendFile": {"message": "The file associated with this Send could not be found.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "noSendsInList": {"message": "There are no Sends to list.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "emergencyAccess": {"message": "Emergency access"}, "emergencyAccessDesc": {"message": "Grant and manage emergency access for trusted contacts. Trusted contacts may request access to either View or Takeover your account in case of an emergency. Visit our help page for more information and details into how zero knowledge sharing works."}, "emergencyAccessOwnerWarning": {"message": "You are an owner of one or more organizations. If you give takeover access to an emergency contact, they will be able to use all your permissions as owner after a takeover."}, "trustedEmergencyContacts": {"message": "Trusted emergency contacts"}, "noTrustedContacts": {"message": "You have not added any emergency contacts yet, invite a trusted contact to get started."}, "addEmergencyContact": {"message": "Add emergency contact"}, "designatedEmergencyContacts": {"message": "Designated as emergency contact"}, "noGrantedAccess": {"message": "You have not been designated as an emergency contact for anyone yet."}, "inviteEmergencyContact": {"message": "Invite emergency contact"}, "editEmergencyContact": {"message": "Edit emergency contact"}, "inviteEmergencyContactDesc": {"message": "Invite a new emergency contact by entering their Bitwarden account email address below. If they do not have a Bitwarden account already, they will be prompted to create a new account."}, "emergencyAccessRecoveryInitiated": {"message": "Emergency access initiated"}, "emergencyAccessRecoveryApproved": {"message": "Emergency access approved"}, "viewDesc": {"message": "Can view all items in your own vault."}, "takeover": {"message": "Takeover"}, "takeoverDesc": {"message": "Can reset your account with a new master password."}, "waitTime": {"message": "Wait time"}, "waitTimeDesc": {"message": "Time required before automatically granting access."}, "oneDay": {"message": "1 day"}, "days": {"message": "$DAYS$ days", "placeholders": {"days": {"content": "$1", "example": "1"}}}, "invitedUser": {"message": "Invited user."}, "acceptEmergencyAccess": {"message": "You've been invited to become an emergency contact for the user listed above. To accept the invitation, you need to log in or create a new Bitwarden account."}, "emergencyInviteAcceptFailed": {"message": "Unable to accept invitation. Ask the user to send a new invitation."}, "emergencyInviteAcceptFailedShort": {"message": "Unable to accept invitation. $DESCRIPTION$", "placeholders": {"description": {"content": "$1", "example": "You must set up 2FA on your user account before you can join this organization."}}}, "emergencyInviteAcceptedDesc": {"message": "You can access the emergency options for this user after your identity has been confirmed. We'll send you an email when that happens."}, "requestAccess": {"message": "Request Access"}, "requestAccessConfirmation": {"message": "Are you sure you want to request emergency access? You will be provided access after $WAITTIME$ day(s) or whenever the user manually approves the request.", "placeholders": {"waittime": {"content": "$1", "example": "1"}}}, "requestSent": {"message": "Emergency access requested for $USER$. We'll notify you by email when it's possible to continue.", "placeholders": {"user": {"content": "$1", "example": "<PERSON>"}}}, "approve": {"message": "Approve"}, "reject": {"message": "Reject"}, "approveAccessConfirmation": {"message": "Are you sure you want to approve emergency access? This will allow $USER$ to $ACTION$ your account.", "placeholders": {"user": {"content": "$1", "example": "<PERSON>"}, "action": {"content": "$2", "example": "View"}}}, "emergencyApproved": {"message": "Emergency access approved"}, "emergencyRejected": {"message": "Emergency access rejected"}, "grantorDetailsNotFound": {"message": "Grantor details not found"}, "passwordResetFor": {"message": "Password reset for $USER$. You can now login using the new password.", "placeholders": {"user": {"content": "$1", "example": "<PERSON>"}}}, "organizationDataOwnership": {"message": "Enforce organization data ownership"}, "personalOwnership": {"message": "Remove individual vault"}, "personalOwnershipPolicyDesc": {"message": "Require members to save items to an organization by removing the individual vault option."}, "personalOwnershipExemption": {"message": "Organization owners and administrators are exempt from this policy's enforcement."}, "personalOwnershipSubmitError": {"message": "Due to an Enterprise policy, you are restricted from saving items to your individual vault. Change the ownership option to an organization and choose from available collections."}, "disableSend": {"message": "Remove Send"}, "disableSendPolicyDesc": {"message": "Do not allow members to create or edit Sends.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disableSendExemption": {"message": "Organization members that can manage the organization's policies are exempt from this policy's enforcement."}, "sendDisabled": {"message": "Send removed", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "Due to an Enterprise policy, you are only able to delete an existing Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendOptions": {"message": "Send options", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendOptionsPolicyDesc": {"message": "Set options for creating and editing Sends.", "description": "'Sends' is a plural noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendOptionsExemption": {"message": "Organization members that can manage the organization's policies are exempt from this policy's enforcement."}, "disableHideEmail": {"message": "Always show member’s email address with recipients when creating or editing a Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "modifiedPolicyId": {"message": "Modified policy $ID$.", "placeholders": {"id": {"content": "$1", "example": "Master Password"}}}, "planPrice": {"message": "Plan price"}, "estimatedTax": {"message": "Estimated tax"}, "custom": {"message": "Custom"}, "customDesc": {"message": "Grant customized permissions to members"}, "customDescNonEnterpriseStart": {"message": "Custom roles is an ", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Custom roles is an enterprise feature. Contact our support team to upgrade your subscription'"}, "customDescNonEnterpriseLink": {"message": "enterprise feature", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Custom roles is an enterprise feature. Contact our support team to upgrade your subscription'"}, "customDescNonEnterpriseEnd": {"message": ". Contact our support team to upgrade your subscription", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Custom roles is an enterprise feature. Contact our support team to upgrade your subscription'"}, "customNonEnterpriseError": {"message": "To enable custom permissions the organization must be on an Enterprise 2020 plan."}, "permissions": {"message": "Permissions"}, "permission": {"message": "Permission"}, "accessEventLogs": {"message": "Access event logs"}, "accessImportExport": {"message": "Access import/export"}, "accessReports": {"message": "Access reports"}, "missingPermissions": {"message": "You lack the necessary permissions to perform this action."}, "manageAllCollections": {"message": "Manage all collections"}, "createNewCollections": {"message": "Create new collections"}, "editAnyCollection": {"message": "Edit any collection"}, "deleteAnyCollection": {"message": "Delete any collection"}, "manageGroups": {"message": "Manage groups"}, "managePolicies": {"message": "Manage policies"}, "manageSso": {"message": "Manage SSO"}, "manageUsers": {"message": "Manage users"}, "manageAccountRecovery": {"message": "Manage account recovery"}, "disableRequiredError": {"message": "You must manually turn the $POLICYNAME$ policy before this policy can be turned off.", "placeholders": {"policyName": {"content": "$1", "example": "Single Sign-On Authentication"}}}, "personalOwnershipPolicyInEffect": {"message": "An organization policy is affecting your ownership options."}, "personalOwnershipPolicyInEffectImports": {"message": "An organization policy has blocked importing items into your individual vault."}, "personalOwnershipCheckboxDesc": {"message": "Remove individual ownership for organization users"}, "send": {"message": "Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendAccessTaglineProductDesc": {"message": "Bitwarden Send transmits sensitive, temporary information to others easily and securely.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendAccessTaglineLearnMore": {"message": "Learn more about", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read '**Learn more about** Bitwarden Send or sign up to try it today.'"}, "sendVaultCardProductDesc": {"message": "Share text or files directly with anyone."}, "sendVaultCardLearnMore": {"message": "Learn more", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read '**Learn more**, see how it works, or try it now. '"}, "sendVaultCardSee": {"message": "see", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Learn more, **see** how it works, or try it now.'"}, "sendVaultCardHowItWorks": {"message": "how it works", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Learn more, see **how it works**, or try it now.'"}, "sendVaultCardOr": {"message": "or", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Learn more, see how it works, **or** try it now.'"}, "developmentDevOpsAndITTeamsChooseBWSecret": {"message": "Development, DevOps, and IT teams choose Bitwarden Secrets Manager to securely manage and deploy their infrastructure and machine secrets."}, "centralizeSecretsManagement": {"message": "Centralize secrets management."}, "centralizeSecretsManagementDescription": {"message": "Securely store and manage secrets in one location to prevent secret sprawl across your organization."}, "preventSecretLeaks": {"message": "Prevent secret leaks."}, "preventSecretLeaksDescription": {"message": "Protect secrets with end-to-end encryption. No more hard coding secrets or sharing through .env files."}, "enhanceDeveloperProductivity": {"message": "Enhance developer productivity."}, "enhanceDeveloperProductivityDescription": {"message": "Programmatically retrieve and deploy secrets at runtime so developers can focus on what matters most, like improving code quality."}, "strengthenBusinessSecurity": {"message": "Strengthen business security."}, "strengthenBusinessSecurityDescription": {"message": "Maintain tight control over machine and human access to secrets with SSO integrations, event logs, and access rotation."}, "tryItNow": {"message": "Try it now"}, "sendRequest": {"message": "Send request"}, "addANote": {"message": "Add a note"}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "moreProductsFromBitwarden": {"message": "More products from Bitwarden"}, "requestAccessToSecretsManager": {"message": "Request access to Secrets Manager"}, "youNeedApprovalFromYourAdminToTrySecretsManager": {"message": "You need approval from your administrator to try Secrets Manager."}, "smAccessRequestEmailSent": {"message": "Access request for secrets manager email sent to admins."}, "requestAccessSMDefaultEmailContent": {"message": "Hi,\n\nI am requesting a subscription to Bitwarden Secrets Manager for our team. Your support would mean a great deal!\n\nBitwarden Secrets Manager is an end-to-end encrypted secrets management solution for securely storing, sharing, and deploying machine credentials like API keys, database passwords, and authentication certificates.\n\nSecrets Manager will help us to:\n\n- Improve security\n- Streamline operations\n- Prevent costly secret leaks\n\nTo request a free trial for our team, please reach out to Bitwarden.\n\nThank you for your help!"}, "giveMembersAccess": {"message": "Give members access:"}, "viewAndSelectTheMembers": {"message": "view and select the members you want to give access to Secrets Manager."}, "openYourOrganizations": {"message": "Open your organization's"}, "usingTheMenuSelect": {"message": "Using the menu, select"}, "toGrantAccessToSelectedMembers": {"message": "to grant access to selected members."}, "sendVaultCardTryItNow": {"message": "try it now", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Learn more, see how it works, or **try it now**.'"}, "sendAccessTaglineOr": {"message": "or", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Learn more about Bitwarden Send **or** sign up to try it today.'"}, "sendAccessTaglineSignUp": {"message": "sign up", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Learn more about Bitwarden Send or **sign up** to try it today.'"}, "sendAccessTaglineTryToday": {"message": "to try it today.", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read '<PERSON>rn more about Bitwarden Send or sign up to **try it today.**'"}, "sendAccessCreatorIdentifier": {"message": "Bitwarden member $USER_IDENTIFIER$ shared the following with you", "placeholders": {"user_identifier": {"content": "$1", "example": "An email address"}}}, "viewSend": {"message": "View Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "viewSendHiddenEmailWarning": {"message": "The Bitwarden user who created this Send has chosen to hide their email address. You should ensure you trust the source of this link before using or downloading its content.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDateIsInvalid": {"message": "The expiration date provided is not valid."}, "deletionDateIsInvalid": {"message": "The deletion date provided is not valid."}, "expirationDateAndTimeRequired": {"message": "An expiration date and time are required."}, "deletionDateAndTimeRequired": {"message": "A deletion date and time are required."}, "dateParsingError": {"message": "There was an error saving your deletion and expiration dates."}, "hideYourEmail": {"message": "Hide your email address from viewers."}, "webAuthnFallbackMsg": {"message": "To verify your 2FA please click the button below."}, "webAuthnAuthenticate": {"message": "Authenticate WebAuthn"}, "readSecurityKey": {"message": "Read security key"}, "awaitingSecurityKeyInteraction": {"message": "Awaiting security key interaction..."}, "webAuthnNotSupported": {"message": "WebAuthn is not supported in this browser."}, "webAuthnSuccess": {"message": "WebAuthn verified successfully! You may close this tab."}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "Your password hint cannot be the same as your password."}, "enrollAccountRecovery": {"message": "Enroll in account recovery"}, "enrolledAccountRecovery": {"message": "Enrolled in account recovery"}, "withdrawAccountRecovery": {"message": "Withdraw from account recovery"}, "enrollPasswordResetSuccess": {"message": "Enrollment success!"}, "withdrawPasswordResetSuccess": {"message": "Withdrawal success!"}, "eventEnrollAccountRecovery": {"message": "User $ID$ enrolled in account recovery.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "eventWithdrawAccountRecovery": {"message": "User $ID$ withdrew from account recovery.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "eventAdminPasswordReset": {"message": "Master password reset for user $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "eventResetSsoLink": {"message": "Reset SSO link for user $ID$", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "firstSsoLogin": {"message": "$ID$ logged in using Sso for the first time", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "resetPassword": {"message": "Reset password"}, "resetPasswordLoggedOutWarning": {"message": "Proceeding will log $NAME$ out of their current session, requiring them to log back in. Active sessions on other devices may continue to remain active for up to one hour.", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}}}, "emergencyAccessLoggedOutWarning": {"message": "Proceeding will log $NAME$ out of their current session, requiring them to log back in. Active sessions on other devices may continue to remain active for up to one hour.", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}}}, "thisUser": {"message": "this user"}, "resetPasswordMasterPasswordPolicyInEffect": {"message": "One or more organization policies require the master password to meet the following requirements:"}, "changePasswordDelegationMasterPasswordPolicyInEffect": {"message": "One or more organization policies require the master password to meet the following requirements:"}, "resetPasswordSuccess": {"message": "Password reset success!"}, "resetPasswordEnrollmentWarning": {"message": "Enrollment will allow organization administrators to change your master password"}, "accountRecoveryPolicy": {"message": "Account recovery administration"}, "accountRecoveryPolicyDesc": {"message": "Based on the encryption method, recover accounts when master passwords or trusted devices are forgotten or lost."}, "accountRecoveryPolicyWarning": {"message": "Existing accounts with master passwords will require members to self-enroll before administrators can recover their accounts. Automatic enrollment will turn on account recovery for new members."}, "accountRecoverySingleOrgRequirementDesc": {"message": "The single organization Enterprise policy must be turned on before activating this policy."}, "resetPasswordPolicyAutoEnroll": {"message": "Automatic enrollment"}, "resetPasswordPolicyAutoEnrollCheckbox": {"message": "Require new members to be enrolled automatically"}, "resetPasswordAutoEnrollInviteWarning": {"message": "This organization has an Enterprise policy that will automatically enroll you in password reset. Enrollment will allow organization administrators to change your master password."}, "resetPasswordOrgKeysError": {"message": "Organization keys response is null"}, "resetPasswordDetailsError": {"message": "Reset password details response is null"}, "trashCleanupWarning": {"message": "Items that have been in trash more than 30 days will be automatically deleted."}, "trashCleanupWarningSelfHosted": {"message": "Items that have been in trash for a while will be automatically deleted."}, "passwordPrompt": {"message": "Master password re-prompt"}, "passwordConfirmation": {"message": "Master password confirmation"}, "passwordConfirmationDesc": {"message": "This action is protected. To continue, please re-enter your master password to verify your identity."}, "reinviteSelected": {"message": "Resend invitations"}, "resendNotification": {"message": "Resend notification"}, "noSelectedUsersApplicable": {"message": "This action is not applicable to any of the selected users."}, "removeUsersWarning": {"message": "Are you sure you want to remove the following users? The process may take a few seconds to complete and cannot be interrupted or canceled."}, "removeOrgUsersConfirmation": {"message": "When member(s) are removed, they no longer have access to organization data and this action is irreversible. To add the member back to the organization, they must be invited and onboarded again. The process may take a few seconds to complete and cannot be interrupted or canceled."}, "revokeUsersWarning": {"message": "When member(s) are revoked, they no longer have access to organization data. To quickly restore member access, go to the Revoked tab. The process may take a few seconds to complete and cannot be interrupted or canceled."}, "theme": {"message": "Theme"}, "themeDesc": {"message": "Choose a theme for your web vault."}, "themeSystem": {"message": "Use system theme"}, "themeDark": {"message": "Dark"}, "themeLight": {"message": "Light"}, "confirmSelected": {"message": "Confirm selected"}, "bulkConfirmStatus": {"message": "Bulk action status"}, "bulkConfirmMessage": {"message": "Confirmed successfully"}, "bulkReinviteMessage": {"message": "Reinvited successfully"}, "bulkRemovedMessage": {"message": "Removed successfully"}, "bulkRevokedMessage": {"message": "Revoked organization access successfully"}, "bulkRestoredMessage": {"message": "Restored organization access successfully"}, "bulkFilteredMessage": {"message": "Excluded, not applicable for this action"}, "nonCompliantMembersTitle": {"message": "Non-compliant members"}, "nonCompliantMembersError": {"message": "Members that are non-compliant with the Single organization or Two-step login policy cannot be restored until they adhere to the policy requirements"}, "fingerprint": {"message": "Fingerprint"}, "fingerprintPhrase": {"message": "Fingerprint phrase:"}, "error": {"message": "Error"}, "decryptionError": {"message": "Decryption error"}, "couldNotDecryptVaultItemsBelow": {"message": "<PERSON><PERSON><PERSON> could not decrypt the vault item(s) listed below."}, "contactCSToAvoidDataLossPart1": {"message": "Contact customer success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to avoid additional data loss.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "accountRecoveryManageUsers": {"message": "Manage users must also be granted with the manage account recovery permission"}, "setupProvider": {"message": "Provider setup"}, "setupProviderLoginDesc": {"message": "You've been invited to setup a new Provider. To continue, you need to log in or create a new Bitwarden account."}, "setupProviderDesc": {"message": "Please enter the details below to complete the Provider setup. Contact Customer Support if you have any questions."}, "providerName": {"message": "Provider name"}, "providerSetup": {"message": "Provider successfully set up"}, "clients": {"message": "Clients"}, "client": {"message": "Client", "description": "This is used as a table header to describe which client application created an event log."}, "providerAdmin": {"message": "Provider admin"}, "providerAdminDesc": {"message": "The highest access user that can manage all aspects of your Provider as well as access and manage client organizations."}, "serviceUser": {"message": "Service user"}, "serviceUserDesc": {"message": "Service users can access and manage all client organizations."}, "providerInviteUserDesc": {"message": "Invite a new user to your Provider by entering their Bitwarden account email address below. If they do not have a Bitwarden account already, they will be prompted to create a new account."}, "joinProvider": {"message": "Join <PERSON>"}, "joinProviderDesc": {"message": "You've been invited to join the Provider listed above. To accept the invitation, you need to log in or create a new Bitwarden account."}, "providerInviteAcceptFailed": {"message": "Unable to accept invitation. Ask a Provider admin to send a new invitation."}, "providerInviteAcceptedDesc": {"message": "You can access this Provider once an administrator confirms your membership. We'll send you an email when that happens."}, "providerUsersNeedConfirmed": {"message": "You have users that have accepted their invitation, but still need to be confirmed. Users will not have access to the Provider until they are confirmed."}, "provider": {"message": "Provider"}, "newClientOrganization": {"message": "New client organization"}, "newClientOrganizationDesc": {"message": "Create a new client organization that will be associated with you as the Provider. You will be able to access and manage this organization."}, "newClient": {"message": "New client"}, "addExistingOrganization": {"message": "Add existing organization"}, "addNewOrganization": {"message": "Add new organization"}, "myProvider": {"message": "My Provider"}, "addOrganizationConfirmation": {"message": "Are you sure you want to add $ORGANIZATION$ as a client to $PROVIDER$?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}, "provider": {"content": "$2", "example": "My Provider Name"}}}, "organizationJoinedProvider": {"message": "Organization was successfully added to the Provider"}, "accessingUsingProvider": {"message": "Accessing organization using Provider $PROVIDER$", "placeholders": {"provider": {"content": "$1", "example": "My Provider Name"}}}, "providerIsDisabled": {"message": "Provider suspended"}, "providerUpdated": {"message": "Provider saved"}, "yourProviderIs": {"message": "Your Provider is $PROVIDER$. They have administrative and billing privileges for your organization.", "placeholders": {"provider": {"content": "$1", "example": "My Provider Name"}}}, "detachedOrganization": {"message": "The organization $ORGANIZATION$ has been detached from your Provider.", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "detachOrganizationConfirmation": {"message": "Are you sure you want to detach this organization? The organization will continue to exist but will no longer be managed by the Provider."}, "add": {"message": "Add"}, "updatedMasterPassword": {"message": "Master password saved"}, "updateMasterPassword": {"message": "Update master password"}, "updateMasterPasswordWarning": {"message": "Your master password was recently changed by an administrator in your organization. In order to access the vault, you must update your master password now. Proceeding will log you out of your current session, requiring you to log back in. Active sessions on other devices may continue to remain active for up to one hour."}, "masterPasswordInvalidWarning": {"message": "Your master password does not meet the policy requirements of this organization. In order to join the organization, you must update your master password now. Proceeding will log you out of your current session, requiring you to log back in. Active sessions on other devices may continue to remain active for up to one hour."}, "updateWeakMasterPasswordWarning": {"message": "Your master password does not meet one or more of your organization policies. In order to access the vault, you must update your master password now. Proceeding will log you out of your current session, requiring you to log back in. Active sessions on other devices may continue to remain active for up to one hour."}, "automaticAppLogin": {"message": "Automatically log in users for allowed applications"}, "automaticAppLoginDesc": {"message": "Login forms will automatically be filled and submitted for apps launched from your configured identity provider."}, "automaticAppLoginIdpHostLabel": {"message": "Identity provider host"}, "automaticAppLoginIdpHostDesc": {"message": "Enter your identity provider host URL. Enter multiple URLs by separating with a comma."}, "tdeDisabledMasterPasswordRequired": {"message": "Your organization has updated your decryption options. Please set a master password to access your vault."}, "maximumVaultTimeout": {"message": "Vault timeout"}, "maximumVaultTimeoutDesc": {"message": "Set a maximum vault timeout for members."}, "maximumVaultTimeoutLabel": {"message": "Maximum vault timeout"}, "invalidMaximumVaultTimeout": {"message": "Invalid maximum vault timeout."}, "hours": {"message": "Hours"}, "minutes": {"message": "Minutes"}, "vaultTimeoutPolicyInEffect": {"message": "Your organization policies have set your maximum allowed vault timeout to $HOURS$ hour(s) and $MINUTES$ minute(s).", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "$HOURS$ hour(s) and $MINUTES$ minute(s) maximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Your organization policies are affecting your vault timeout. Maximum allowed vault timeout is $HOURS$ hour(s) and $MINUTES$ minute(s). Your vault timeout action is set to $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Your organization policies have set your vault timeout action to $ACTION$.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutToLarge": {"message": "Your vault timeout exceeds the restriction set by your organization."}, "vaultCustomTimeoutMinimum": {"message": "Minimum custom timeout is 1 minute."}, "vaultTimeoutRangeError": {"message": "Vault timeout is not within allowed range."}, "disablePersonalVaultExport": {"message": "Remove individual vault export"}, "disablePersonalVaultExportDescription": {"message": "Do not allow members to export data from their individual vault."}, "vaultExportDisabled": {"message": "Vault export removed"}, "personalVaultExportPolicyInEffect": {"message": "One or more organization policies prevents you from exporting your individual vault."}, "activateAutofill": {"message": "Activate auto-fill"}, "activateAutofillPolicyDesc": {"message": "Activate the auto-fill on page load setting on the browser extension for all existing and new members."}, "experimentalFeature": {"message": "Compromised or untrusted websites can exploit auto-fill on page load."}, "learnMoreAboutAutofill": {"message": "Learn more about auto-fill"}, "selectType": {"message": "Select SSO type"}, "type": {"message": "Type"}, "openIdConnectConfig": {"message": "OpenID connect configuration"}, "samlSpConfig": {"message": "SAML service provider configuration"}, "samlIdpConfig": {"message": "SAML identity provider configuration"}, "callbackPath": {"message": "Callback path"}, "signedOutCallbackPath": {"message": "Signed out callback path"}, "authority": {"message": "Authority"}, "clientId": {"message": "Client ID"}, "clientSecret": {"message": "Client secret"}, "metadataAddress": {"message": "Metadata address"}, "oidcRedirectBehavior": {"message": "OIDC redirect behavior"}, "getClaimsFromUserInfoEndpoint": {"message": "Get claims from user info endpoint"}, "additionalScopes": {"message": "Custom scopes"}, "additionalUserIdClaimTypes": {"message": "Custom user ID claim types"}, "additionalEmailClaimTypes": {"message": "Email claim types"}, "additionalNameClaimTypes": {"message": "Custom name claim types"}, "acrValues": {"message": "Requested authentication context class reference values"}, "expectedReturnAcrValue": {"message": "Expected \"acr\" claim value in response"}, "spEntityId": {"message": "SP entity ID"}, "spMetadataUrl": {"message": "SAML 2.0 metadata URL"}, "spAcsUrl": {"message": "Assertion consumer service (ACS) URL"}, "spNameIdFormat": {"message": "Name ID format"}, "spOutboundSigningAlgorithm": {"message": "Outbound signing algorithm"}, "spSigningBehavior": {"message": "Signing behavior"}, "spMinIncomingSigningAlgorithm": {"message": "Minimum incoming signing algorithm"}, "spWantAssertionsSigned": {"message": "Expect signed assertions"}, "spValidateCertificates": {"message": "Validate certificates"}, "spUniqueEntityId": {"message": "Set a unique SP entity ID"}, "spUniqueEntityIdDesc": {"message": "Generate an identifier that is unique to your organization"}, "idpEntityId": {"message": "Entity ID"}, "idpBindingType": {"message": "Binding type"}, "idpSingleSignOnServiceUrl": {"message": "Single sign-on service URL"}, "idpSingleLogoutServiceUrl": {"message": "Single log-out service URL"}, "idpX509PublicCert": {"message": "X509 public certificate"}, "idpOutboundSigningAlgorithm": {"message": "Outbound signing algorithm"}, "idpAllowUnsolicitedAuthnResponse": {"message": "Allow unsolicited authentication response"}, "idpAllowOutboundLogoutRequests": {"message": "Allow outbound logout requests"}, "idpSignAuthenticationRequests": {"message": "Sign authentication requests"}, "ssoSettingsSaved": {"message": "Single sign-on configuration saved"}, "sponsoredFamilies": {"message": "Free Bitwarden Families"}, "sponsoredBitwardenFamilies": {"message": "Sponsored families"}, "noSponsoredFamiliesMessage": {"message": "No sponsored families"}, "nosponsoredFamiliesDetails": {"message": "Sponsored non-member families plans will display here"}, "sponsorshipFreeBitwardenFamilies": {"message": "Members of your organization are eligible for Free Bitwarden Families. You can sponsor Free Bitwarden Families for employees who are not a member of your Bitwarden organization. Sponsoring a non-member requires an available seat within your organization."}, "sponsoredFamiliesRemoveActiveSponsorship": {"message": "When you remove an active sponsorship, a seat within your organization will be available after the renewal date of the sponsored organization."}, "sponsoredFamiliesEligible": {"message": "You and your family are eligible for Free Bitwarden Families. Redeem with your personal email to keep your data secure even when you are not at work."}, "sponsoredFamiliesEligibleCard": {"message": "Redeem your Free Bitwarden for Families plan today to keep your data secure even when you are not at work."}, "sponsoredFamiliesIncludeMessage": {"message": "The Bitwarden for Families plan includes"}, "sponsoredFamiliesPremiumAccess": {"message": "Premium access for up to 6 users"}, "sponsoredFamiliesSharedCollectionsForFamilyMembers": {"message": "Shared collections for family members"}, "memberFamilies": {"message": "Member families"}, "noMemberFamilies": {"message": "No member families"}, "noMemberFamiliesDescription": {"message": "Members who have redeemed family plans will display here"}, "membersWithSponsoredFamilies": {"message": "Members of your organization are eligible for Free Bitwarden Families. Here you can see members who have sponsored a Families organization."}, "organizationHasMemberMessage": {"message": "A sponsorship cannot be sent to $EMAIL$ because they are a member of your organization.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "badToken": {"message": "The link is no longer valid. Please have the sponsor resend the offer."}, "reclaimedFreePlan": {"message": "Reclaimed free plan"}, "redeem": {"message": "Redeem"}, "sponsoredFamiliesSelectOffer": {"message": "Select the organization you would like sponsored"}, "familiesSponsoringOrgSelect": {"message": "Which Free Families offer would you like to redeem?"}, "sponsoredFamiliesEmail": {"message": "Enter your personal email to redeem Bitwarden Families"}, "sponsoredFamiliesLeaveCopy": {"message": "If you remove an offer or are removed from the sponsoring organization, your Families sponsorship will expire at the next renewal date."}, "acceptBitwardenFamiliesHelp": {"message": "Accept offer for an existing organization or create a new Families organization."}, "setupSponsoredFamiliesLoginDesc": {"message": "You've been offered a free Bitwarden Families plan organization. To continue, you need to log in to the account that received the offer."}, "sponsoredFamiliesAcceptFailed": {"message": "Unable to accept offer. Please resend the offer email from your Enterprise account and try again."}, "sponsoredFamiliesAcceptFailedShort": {"message": "Unable to accept offer. $DESCRIPTION$", "placeholders": {"description": {"content": "$1", "example": "You must have at least one existing Families organization."}}}, "sponsoredFamiliesOffer": {"message": "Accept Free Bitwarden Families"}, "sponsoredFamiliesOfferRedeemed": {"message": "Free Bitwarden Families offer successfully redeemed"}, "redeemed": {"message": "Redeemed"}, "redeemedAccount": {"message": "Account redeemed"}, "revokeAccountMessage": {"message": "Revoke account $NAME$", "placeholders": {"name": {"content": "$1", "example": "My Sponsorship Name"}}}, "resendEmailLabel": {"message": "Resend sponsorship email to $NAME$ sponsorship", "placeholders": {"name": {"content": "$1", "example": "My Sponsorship Name"}}}, "freeFamiliesPlan": {"message": "Free Families plan"}, "redeemNow": {"message": "Redeem now"}, "recipient": {"message": "Recipient"}, "removeSponsorship": {"message": "Remove sponsorship"}, "removeSponsorshipConfirmation": {"message": "After removing a sponsorship, you will be responsible for this subscription and related invoices. Are you sure you want to continue?"}, "sponsorshipCreated": {"message": "Sponsorship created"}, "emailSent": {"message": "Email sent"}, "removeSponsorshipSuccess": {"message": "Sponsorship removed"}, "ssoKeyConnectorError": {"message": "Key Connector error: make sure Key Connector is available and working correctly."}, "keyConnectorUrl": {"message": "Key Connector URL"}, "sendVerificationCode": {"message": "Send a verification code to your email"}, "sendCode": {"message": "Send code"}, "codeSent": {"message": "Code sent"}, "verificationCode": {"message": "Verification code"}, "confirmIdentity": {"message": "Confirm your identity to continue."}, "verificationCodeRequired": {"message": "Verification code is required."}, "webauthnCancelOrTimeout": {"message": "The authentication was cancelled or took too long. Please try again."}, "invalidVerificationCode": {"message": "Invalid verification code"}, "removeMasterPasswordForOrganizationUserKeyConnector": {"message": "A master password is no longer required for members of the following organization. Please confirm the domain below with your organization administrator."}, "keyConnectorDomain": {"message": "Key Connector domain"}, "leaveOrganization": {"message": "Leave organization"}, "removeMasterPassword": {"message": "Remove master password"}, "removedMasterPassword": {"message": "Master password removed"}, "allowSso": {"message": "Allow SSO authentication"}, "allowSsoDesc": {"message": "Once set up, your configuration will be saved and members will be able to authenticate using their Identity Provider credentials."}, "ssoPolicyHelpStart": {"message": "Use the", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Use the require single-sign-on authentication policy to require all members to log in with SSO.'"}, "ssoPolicyHelpAnchor": {"message": "require single sign-on authentication policy", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Use the require single-sign-on authentication policy to require all members to log in with SSO.'"}, "ssoPolicyHelpEnd": {"message": "to require all members to log in with SSO.", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Use the require single-sign-on authentication policy to require all members to log in with SSO.'"}, "memberDecryptionOption": {"message": "Member decryption options"}, "memberDecryptionPassDesc": {"message": "Once authenticated, members will decrypt vault data using their master passwords."}, "keyConnector": {"message": "Key Connector"}, "memberDecryptionKeyConnectorDescStart": {"message": "Connect login with SSO to your self-hosted decryption key server. Using this option, members won’t need to use their master passwords to decrypt vault data. The", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Connect login with SSO to your self-hosted decryption key server. Using this option, members won’t need to use their master passwords to decrypt vault data. The require SSO authentication and single organization policies are required to set up Key Connector decryption. Contact Bitwarden Support for set up assistance.'"}, "memberDecryptionKeyConnectorDescLink": {"message": "require SSO authentication and single organization policies", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Connect login with SSO to your self-hosted decryption key server. Using this option, members won’t need to use their master passwords to decrypt vault data. The require SSO authentication and single organization policies are required to set up Key Connector decryption. Contact Bitwarden Support for set up assistance.'"}, "memberDecryptionKeyConnectorDescEnd": {"message": "are required to set up Key Connector decryption. Contact Bitwarden Support for set up assistance.", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Connect login with SSO to your self-hosted decryption key server. Using this option, members won’t need to use their master passwords to decrypt vault data. The require SSO authentication and single organization policies are required to set up Key Connector decryption. Contact Bitwarden Support for set up assistance.'"}, "keyConnectorPolicyRestriction": {"message": "\"Login with SSO and Key Connector Decryption\" is activated. This policy will only apply to owners and admins."}, "enabledSso": {"message": "SSO turned on"}, "disabledSso": {"message": "SSO turned on"}, "enabledKeyConnector": {"message": "Key Connector activated"}, "disabledKeyConnector": {"message": "Key Connector deactivated"}, "keyConnectorWarning": {"message": "Once members begin using Key Connector, your organization cannot revert to master password decryption. Proceed only if you are comfortable deploying and managing a key server."}, "migratedKeyConnector": {"message": "Migrated to Key Connector"}, "paymentSponsored": {"message": "Please provide a payment method to associate with the organization. Don't worry, we won't charge you anything unless you select additional features or your sponsorship expires. "}, "orgCreatedSponsorshipInvalid": {"message": "The sponsorship offer has expired. You may delete the organization you created to avoid a charge at the end of your 7 day trial. Otherwise you may close this prompt to keep the organization and assume billing responsibility."}, "newFamiliesOrganization": {"message": "New Families organization"}, "acceptOffer": {"message": "Accept offer"}, "sponsoringOrg": {"message": "Sponsoring organization"}, "keyConnectorTest": {"message": "Test"}, "keyConnectorTestSuccess": {"message": "Success! Key Connector reached."}, "keyConnectorTestFail": {"message": "Cannot reach Key Connector. Check URL."}, "sponsorshipTokenHasExpired": {"message": "The sponsorship offer has expired."}, "freeWithSponsorship": {"message": "FREE with sponsorship"}, "viewBillingSyncToken": {"message": "View billing sync token"}, "generateBillingToken": {"message": "Generate billing token"}, "copyPasteBillingSync": {"message": "Copy and paste this token into the billing sync settings of your self-hosted organization."}, "billingSyncCanAccess": {"message": "Your billing sync token can access and edit this organization's subscription settings."}, "manageBillingTokenSync": {"message": "Manage Billing Token"}, "setUpBillingSync": {"message": "Set up billing sync"}, "generateToken": {"message": "Generate token"}, "rotateToken": {"message": "Rotate token"}, "rotateBillingSyncTokenWarning": {"message": "If you proceed, you will need to re-setup billing sync on your self-hosted server."}, "rotateBillingSyncTokenTitle": {"message": "Rotating the billing sync token will invalidate the previous token."}, "selfHostedServer": {"message": "self-hosted"}, "customEnvironment": {"message": "Custom environment"}, "selfHostedBaseUrlHint": {"message": "Specify the base URL of your on-premises hosted Bitwarden installation. Example: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "For advanced configuration, you can specify the base URL of each service independently."}, "selfHostedEnvFormInvalid": {"message": "You must add either the base Server URL or at least one custom environment."}, "apiUrl": {"message": "API server URL"}, "webVaultUrl": {"message": "Web vault server URL"}, "identityUrl": {"message": "Identity server URL"}, "notificationsUrl": {"message": "Notifications server URL"}, "iconsUrl": {"message": "Icons server URL"}, "environmentSaved": {"message": "Environment URLs saved"}, "selfHostingTitle": {"message": "Self-hosting"}, "selfHostingEnterpriseOrganizationSectionCopy": {"message": "To set-up your organization on your own server, you will need to upload your license file. To support Free Families plans and advanced billing capabilities for your self-hosted organization, you will need to set up billing sync."}, "billingSyncApiKeyRotated": {"message": "<PERSON><PERSON> rotated"}, "billingSyncKeyDesc": {"message": "A billing sync token from your cloud organization's subscription settings is required to complete this form."}, "billingSyncKey": {"message": "Billing sync token"}, "automaticBillingSyncDesc": {"message": "Automatic sync unlocks Families sponsorships and allows you to sync your license without uploading a file. After making updates in the Bitwarden cloud server, select Sync License to apply changes."}, "active": {"message": "Active"}, "inactive": {"message": "Inactive"}, "sentAwaitingSync": {"message": "<PERSON><PERSON> (awaiting sync)"}, "sent": {"message": "<PERSON><PERSON>"}, "requestRemoved": {"message": "Removed (awaiting sync)"}, "requested": {"message": "Requested"}, "formErrorSummaryPlural": {"message": "$COUNT$ fields above need your attention.", "placeholders": {"count": {"content": "$1", "example": "5"}}}, "formErrorSummarySingle": {"message": "1 field above needs your attention."}, "fieldRequiredError": {"message": "$FIELDNAME$ is required.", "placeholders": {"fieldname": {"content": "$1", "example": "Full name"}}}, "required": {"message": "required"}, "charactersCurrentAndMaximum": {"message": "$CURRENT$/$MAX$ character maximum", "placeholders": {"current": {"content": "$1", "example": "0"}, "max": {"content": "$2", "example": "100"}}}, "characterMaximum": {"message": "$MAX$ character maximum", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "idpSingleSignOnServiceUrlRequired": {"message": "Required if Entity ID is not a URL."}, "offerNoLongerValid": {"message": "This offer is no longer valid. Contact your organization administrators for more information."}, "openIdOptionalCustomizations": {"message": "Optional customizations"}, "openIdAuthorityRequired": {"message": "Required if Authority is not valid."}, "separateMultipleWithComma": {"message": "Separate multiple with a comma."}, "sessionTimeout": {"message": "Your session has timed out. Please go back and try logging in again."}, "exportingPersonalVaultTitle": {"message": "Exporting individual vault"}, "exportingOrganizationVaultTitle": {"message": "Exporting organization vault"}, "exportingIndividualVaultDescription": {"message": "Only the individual vault items associated with $EMAIL$ will be exported. Organization vault items will not be included. Only vault item information will be exported and will not include associated attachments.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Only the individual vault items including attachments associated with $EMAIL$ will be exported. Organization vault items will not be included", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultDesc": {"message": "Only the organization vault associated with $ORGANIZATION$ will be exported. Items in individual vaults or other organizations will not be included.", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "accessDenied": {"message": "Access denied. You do not have permission to view this page."}, "masterPassword": {"message": "Master password"}, "security": {"message": "Security"}, "keys": {"message": "Keys"}, "billingHistory": {"message": "Billing history"}, "backToReports": {"message": "Back to reports"}, "organizationPicker": {"message": "Organization picker"}, "currentOrganization": {"message": "Current organization", "description": "This is used by screen readers to indicate the organization that is currently being shown to the user."}, "accountLoggedInAsName": {"message": "Account: Logged in as $NAME$", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}}}, "accountSettings": {"message": "Account settings"}, "generator": {"message": "Generator", "description": "Short for 'credential generator'."}, "generateUsername": {"message": "Generate username"}, "generateEmail": {"message": "Generate email"}, "generatePassword": {"message": "Generate password"}, "generatePassphrase": {"message": "Generate passphrase"}, "passwordGenerated": {"message": "Password generated"}, "passphraseGenerated": {"message": "Passphrase generated"}, "usernameGenerated": {"message": "Username generated"}, "emailGenerated": {"message": "Email generated"}, "spinboxBoundariesHint": {"message": "Value must be between $MIN$ and $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Use $RECOMMENDED$ characters or more to generate a strong password.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Use $RECOMMENDED$ words or more to generate a strong passphrase.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "Plus addressed email", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Use your email provider's sub-addressing capabilities."}, "catchallEmail": {"message": "Catch-all email"}, "catchallEmailDesc": {"message": "Use your domain's configured catch-all inbox."}, "useThisEmail": {"message": "Use this email"}, "random": {"message": "Random", "description": "Generates domain-based username using random letters"}, "randomWord": {"message": "Random word"}, "usernameGenerator": {"message": "Username generator"}, "useThisPassword": {"message": "Use this password"}, "useThisPassphrase": {"message": "Use this passphrase"}, "useThisUsername": {"message": "Use this username"}, "securePasswordGenerated": {"message": "Secure password generated! Don't forget to also update your password on the website."}, "useGeneratorHelpTextPartOne": {"message": "Use the generator", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "to create a strong unique password", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "service": {"message": "Service"}, "unknownCipher": {"message": "Unknown item, you may need to request permission to access this item."}, "cannotSponsorSelf": {"message": "You cannot redeem for the active account. Enter a different email."}, "revokeWhenExpired": {"message": "Expires $DATE$", "placeholders": {"date": {"content": "$1", "example": "12/31/2020"}}}, "awaitingSyncSingular": {"message": "Token rotated $DAYS$ day ago. Update the billing sync token in your self-hosted organization settings.", "placeholders": {"days": {"content": "$1", "example": "1"}}}, "awaitingSyncPlural": {"message": "Token rotated $DAYS$ days ago. Update the billing sync token in your self-hosted organization settings.", "placeholders": {"days": {"content": "$1", "example": "1"}}}, "lastSync": {"message": "Last sync", "description": "Used as a prefix to indicate the last time a sync occurred. Example \"Last sync 1968-11-16 00:00:00\""}, "sponsorshipsSynced": {"message": "Self-hosted sponsorships synced."}, "billingManagedByProvider": {"message": "Managed by $PROVIDER$", "placeholders": {"provider": {"content": "$1", "example": "Managed Services Company"}}}, "billingContactProviderForAssistance": {"message": "Please reach out to them for further assistance", "description": "This text is displayed if an organization's billing is managed by a Provider. It tells the user to contact the Provider for assistance."}, "forwardedEmail": {"message": "Forwarded email alias"}, "forwardedEmailDesc": {"message": "Generate an email alias with an external forwarding service."}, "forwarderDomainName": {"message": "Email domain", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Choose a domain that is supported by the selected service", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ error: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Generated by <PERSON><PERSON><PERSON>.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Website: $WEBSITE$. Generated by Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Invalid $SERVICENAME$ API token", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Invalid $SERVICENAME$ API token: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ refused your request. Please contact your service provider for assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ refused your request: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Unable to obtain $SERVICENAME$ masked email account ID.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Invalid $SERVICENAME$ domain.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "Invalid $SERVICENAME$ url.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Unknown $SERVICENAME$ error occurred.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Unknown forwarder: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "Hostname", "description": "Part of a URL."}, "deviceVerification": {"message": "Device verification"}, "enableDeviceVerification": {"message": "Turn on device verification"}, "deviceVerificationDesc": {"message": "Verification codes are sent to your email address when logging in from an unrecognized device"}, "updatedDeviceVerification": {"message": "Updated device verification"}, "areYouSureYouWantToEnableDeviceVerificationTheVerificationCodeEmailsWillArriveAtX": {"message": "Are you sure you want to turn on device verification? The verification code emails will arrive at: $EMAIL$", "placeholders": {"email": {"content": "$1", "example": "My Email"}}}, "premiumSubcriptionRequired": {"message": "Premium subscription required"}, "scim": {"message": "SCIM provisioning", "description": "The text, 'SCIM', is an acronym and should not be translated."}, "scimDescription": {"message": "Automatically provision users and groups with your preferred identity provider via SCIM provisioning", "description": "the text, 'SCIM', is an acronym and should not be translated."}, "scimIntegrationDescription": {"message": "Automatically provision users and groups with your preferred identity provider via SCIM provisioning. Find supported integrations", "description": "the text, 'SCIM', is an acronym and should not be translated."}, "scimEnabledCheckboxDesc": {"message": "Enable SCIM", "description": "the text, 'SCIM', is an acronym and should not be translated."}, "scimEnabledCheckboxDescHelpText": {"message": "Set up your preferred identity provider by configuring the URL and SCIM API Key", "description": "the text, 'SCIM', is an acronym and should not be translated."}, "scimApiKeyHelperText": {"message": "This API key has access to manage users within your organization. It should be kept secret."}, "copyScimKey": {"message": "Copy the SCIM API key to your clipboard", "description": "the text, 'SCIM' and 'API', are acronyms and should not be translated."}, "rotateScimKey": {"message": "Rotate the SCIM API key", "description": "the text, 'SCIM' and 'API', are acronyms and should not be translated."}, "rotateScimKeyWarning": {"message": "Are you sure you want to rotate the SCIM API Key? The current key will no longer work for any existing integrations.", "description": "the text, 'SCIM' and 'API', are acronyms and should not be translated."}, "rotateKey": {"message": "Rotate key"}, "scimApiKey": {"message": "SCIM API key", "description": "the text, 'SCIM' and 'API', are acronyms and should not be translated."}, "copyScimUrl": {"message": "Copy the SCIM endpoint URL to your clipboard", "description": "the text, 'SCIM' and 'URL', are acronyms and should not be translated."}, "scimUrl": {"message": "SCIM URL", "description": "the text, 'SCIM' and 'URL', are acronyms and should not be translated."}, "scimApiKeyRotated": {"message": "SCIM API key successfully rotated", "description": "the text, 'SCIM' and 'API', are acronyms and should not be translated."}, "scimSettingsSaved": {"message": "SCIM settings saved", "description": "the text, 'SCIM', is an acronym and should not be translated."}, "inputRequired": {"message": "Input is required."}, "inputEmail": {"message": "Input is not an email address."}, "inputMinLength": {"message": "Input must be at least $COUNT$ characters long.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "Input must not exceed $COUNT$ characters in length.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "The following characters are not allowed: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "Input value must be at least $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "Input value must not exceed $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "1 or more emails are invalid"}, "tooManyEmails": {"message": "You can only submit up to $COUNT$ emails at a time", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "fieldsNeedAttention": {"message": "$COUNT$ field(s) above need your attention.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 field needs your attention."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ fields need your attention.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Error connecting with the Duo service. Use a different two-step login method or contact Duo for assistance."}, "duoRequiredByOrgForAccount": {"message": "Duo two-step login is required for your account."}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo two-step login is required for your account. Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Follow the steps below to finish logging in with your security key."}, "launchDuo": {"message": "Launch Duo"}, "turnOn": {"message": "Turn on"}, "on": {"message": "On"}, "off": {"message": "Off"}, "members": {"message": "Members"}, "reporting": {"message": "Reporting"}, "numberOfUsers": {"message": "Number of users"}, "pickAnAvatarColor": {"message": "Pick an avatar color"}, "customizeAvatar": {"message": "Customize avatar"}, "avatarUpdated": {"message": "Avatar updated"}, "brightBlue": {"message": "Bright Blue"}, "green": {"message": "Green"}, "orange": {"message": "Orange"}, "lavender": {"message": "Lavender"}, "yellow": {"message": "Yellow"}, "indigo": {"message": "Indigo"}, "teal": {"message": "<PERSON><PERSON>"}, "salmon": {"message": "Salmon"}, "pink": {"message": "Pink"}, "customColor": {"message": "Custom Color"}, "selectPlaceholder": {"message": "-- Select --"}, "multiSelectPlaceholder": {"message": "-- Type to filter --"}, "multiSelectLoading": {"message": "Retrieving options..."}, "multiSelectNotFound": {"message": "No items found"}, "multiSelectClearAll": {"message": "Clear all"}, "toggleCharacterCount": {"message": "Toggle character count", "description": "'Character count' describes a feature that displays a number next to each character of the password."}, "passwordCharacterCount": {"message": "Password character count", "description": "'Character count' describes a feature that displays a number next to each character of the password."}, "hide": {"message": "<PERSON>de"}, "projects": {"message": "Projects", "description": "Description for the Projects field."}, "lastEdited": {"message": "Last edited", "description": "The label for the date and time when a item was last edited."}, "editSecret": {"message": "Edit secret", "description": "Action to modify an existing secret."}, "addSecret": {"message": "Add secret", "description": "Action to create a new secret."}, "copySecretName": {"message": "Copy secret name", "description": "Action to copy the name of a secret to the system's clipboard."}, "copySecretValue": {"message": "Copy secret value", "description": "Action to copy the value of a secret to the system's clipboard."}, "deleteSecret": {"message": "Delete secret", "description": "Action to delete a single secret from the system."}, "deleteSecrets": {"message": "Delete secrets", "description": "The action to delete multiple secrets from the system."}, "hardDeleteSecret": {"message": "Permanently delete secret"}, "hardDeleteSecrets": {"message": "Permanently delete secrets"}, "secretProjectAssociationDescription": {"message": "Select projects that the secret will be associated with. Only organization users with access to these projects will be able to see the secret.", "description": "A prompt explaining how secrets can be associated with projects."}, "selectProjects": {"message": "Select projects", "description": "A label for a type-to-filter input field to choose projects."}, "searchProjects": {"message": "Search projects", "description": "Label for the search bar used to search projects."}, "project": {"message": "Project", "description": "Similar to collections, projects can be used to group secrets."}, "editProject": {"message": "Edit project", "description": "The action to modify an existing project."}, "viewProject": {"message": "View project", "description": "The action to view details of a project."}, "deleteProject": {"message": "Delete project", "description": "The action to delete a project from the system."}, "deleteProjects": {"message": "Delete projects", "description": "The action to delete multiple projects from the system."}, "secret": {"message": "Secret", "description": "Label for a secret (key/value pair)"}, "serviceAccount": {"message": "Service account", "description": "A machine user which can be used to automate processes and access secrets in the system."}, "serviceAccounts": {"message": "Service accounts", "description": "The title for the section that deals with service accounts."}, "secrets": {"message": "Secrets", "description": "The title for the section of the application that deals with secrets."}, "nameValuePair": {"message": "Name/Value pair", "description": "Title for a name/ value pair. Secrets typically consist of a name and value pair."}, "secretEdited": {"message": "Secret edited", "description": "Notification for the successful editing of a secret."}, "secretCreated": {"message": "Secret created", "description": "Notification for the successful creation of a secret."}, "newSecret": {"message": "New secret", "description": "Title for creating a new secret."}, "newServiceAccount": {"message": "New service account", "description": "Title for creating a new service account."}, "secretsNoItemsTitle": {"message": "No secrets to show", "description": "Empty state to indicate that there are no secrets to display."}, "secretsNoItemsMessage": {"message": "To get started, add a new secret or import secrets.", "description": "Message to encourage the user to start adding secrets."}, "secretsTrashNoItemsMessage": {"message": "There are no secrets in the trash."}, "serviceAccountsNoItemsMessage": {"message": "Create a new service account to get started automating secret access.", "description": "Message to encourage the user to start creating service accounts."}, "serviceAccountsNoItemsTitle": {"message": "Nothing to show yet", "description": "Title to indicate that there are no service accounts to display."}, "searchSecrets": {"message": "Search secrets", "description": "Placeholder text for searching secrets."}, "deleteServiceAccounts": {"message": "Delete service accounts", "description": "Title for the action to delete one or multiple service accounts."}, "deleteServiceAccount": {"message": "Delete service account", "description": "Title for the action to delete a single service account."}, "viewServiceAccount": {"message": "View service account", "description": "Action to view the details of a service account."}, "deleteServiceAccountDialogMessage": {"message": "Deleting service account $SERVICE_ACCOUNT$ is permanent and irreversible.", "placeholders": {"service_account": {"content": "$1", "example": "Service account name"}}}, "deleteServiceAccountsDialogMessage": {"message": "Deleting service accounts is permanent and irreversible."}, "deleteServiceAccountsConfirmMessage": {"message": "Delete $COUNT$ service accounts", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "deleteServiceAccountToast": {"message": "Service account deleted"}, "deleteServiceAccountsToast": {"message": "Service accounts deleted"}, "searchServiceAccounts": {"message": "Search service accounts", "description": "Placeholder text for searching service accounts."}, "editServiceAccount": {"message": "Edit service account", "description": "Title for editing a service account."}, "addProject": {"message": "Add project", "description": "Title for creating a new project."}, "projectEdited": {"message": "Project edited", "description": "Notification for the successful editing of a project."}, "projectSaved": {"message": "Project saved", "description": "Notification for the successful saving of a project."}, "projectCreated": {"message": "Project created", "description": "Notification for the successful creation of a project."}, "projectName": {"message": "Project name", "description": "Label for entering the name of a project."}, "newProject": {"message": "New project", "description": "Title for creating a new project."}, "softDeleteSecretWarning": {"message": "Deleting secrets can affect existing integrations.", "description": "Warns that deleting secrets can have consequences on integrations"}, "softDeletesSuccessToast": {"message": "Secrets sent to trash", "description": "Notifies that the selected secrets have been moved to the trash"}, "hardDeleteSecretConfirmation": {"message": "Are you sure you want to permanently delete this secret?"}, "hardDeleteSecretsConfirmation": {"message": "Are you sure you want to permanently delete these secrets?"}, "hardDeletesSuccessToast": {"message": "Secrets permanently deleted"}, "smAccess": {"message": "Access", "description": "Title indicating what permissions a service account has"}, "projectCommaSecret": {"message": "Project, Secret", "description": ""}, "serviceAccountName": {"message": "Service account name", "description": "Label for the name of a service account"}, "serviceAccountCreated": {"message": "Service account created", "description": "Notifies that a new service account has been created"}, "serviceAccountUpdated": {"message": "Service account updated", "description": "Notifies that a service account has been updated"}, "typeOrSelectProjects": {"message": "Type or select projects", "description": "Instructions for selecting projects for a service account"}, "newSaTypeToFilter": {"message": "Type to filter", "description": "Instructions for filtering a list of projects or secrets"}, "deleteProjectsToast": {"message": "Projects deleted", "description": "Notifies that the selected projects have been deleted"}, "deleteProjectToast": {"message": "Project deleted", "description": "Notifies that a project has been deleted"}, "deleteProjectDialogMessage": {"message": "Deleting project $PROJECT$ is permanent and irreversible.", "description": "Informs users that projects are hard deleted and not sent to trash", "placeholders": {"project": {"content": "$1", "example": "project name"}}}, "deleteProjectInputLabel": {"message": "Type \"$CONFIRM$\" to continue", "description": "Users are prompted to type 'confirm' to delete a project", "placeholders": {"confirm": {"content": "$1", "example": "Delete 3 projects"}}}, "deleteProjectConfirmMessage": {"message": "Delete $PROJECT$", "description": "Confirmation prompt to delete a specific project, where '$PROJECT$' is a placeholder for the name of the project.", "placeholders": {"project": {"content": "$1", "example": "project name"}}}, "deleteProjectsConfirmMessage": {"message": "Delete $COUNT$ Projects", "description": "Confirmation prompt to delete multiple projects, where '$COUNT$' is a placeholder for the number of projects to be deleted.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "deleteProjectsDialogMessage": {"message": "Deleting projects is permanent and irreversible.", "description": "This message is displayed in a dialog box as a warning before proceeding with project deletion."}, "projectsNoItemsTitle": {"message": "No projects to display", "description": "Empty state to be displayed when there are no projects to display in the list."}, "projectsNoItemsMessage": {"message": "Add a new project to get started organizing secrets.", "description": "Message to be displayed when there are no projects to display in the list."}, "smConfirmationRequired": {"message": "Confirmation required", "description": "Indicates that user confirmation is required for an action to proceed."}, "bulkDeleteProjectsErrorMessage": {"message": "The following projects could not be deleted:", "description": "Message to be displayed when there is an error during bulk project deletion."}, "softDeleteSuccessToast": {"message": "Secret sent to trash", "description": "Notification to be displayed when a secret is successfully sent to the trash."}, "hardDeleteSuccessToast": {"message": "Secret permanently deleted"}, "accessTokens": {"message": "Access tokens", "description": "Title for the section displaying access tokens."}, "createAccessToken": {"message": "Create access token", "description": "Button label for creating a new access token."}, "expires": {"message": "Expires", "description": "Label for the expiration date of an access token."}, "canRead": {"message": "Can read", "description": "Label for the access level of an access token (Read only)."}, "accessTokensNoItemsTitle": {"message": "No access tokens to show", "description": "Title to be displayed when there are no access tokens to display in the list."}, "accessTokensNoItemsDesc": {"message": "To get started, create an access token", "description": "Message to be displayed when there are no access tokens to display in the list."}, "downloadAccessToken": {"message": "Download or copy before closing.", "description": "Message to be displayed before closing an access token, reminding the user to download or copy it."}, "expiresOnAccessToken": {"message": "Expires on:", "description": "Label for the expiration date of an access token."}, "accessTokenCallOutTitle": {"message": "Access tokens are not stored and cannot be retrieved", "description": "Notification to inform the user that access tokens are only displayed once and cannot be retrieved again."}, "copyToken": {"message": "Copy token", "description": "Copies the generated access token to the user's clipboard."}, "accessToken": {"message": "Access token", "description": "A unique string that gives a client application (eg. CLI) access to a secret or set of secrets."}, "accessTokenExpirationRequired": {"message": "Expiration date required", "description": "Error message indicating that an expiration date for the access token must be set."}, "accessTokenCreatedAndCopied": {"message": "Access token created and copied to clipboard", "description": "Notification to inform the user that the access token has been created and copied to the clipboard."}, "revokeAccessToken": {"message": "Revoke access token", "description": "Invalidates / cancels an access token and as such removes access to secrets for the client application."}, "revokeAccessTokens": {"message": "Revoke access tokens"}, "revokeAccessTokenDesc": {"message": "Revoking access tokens is permanent and irreversible."}, "accessTokenRevoked": {"message": "Access tokens revoked", "description": "Toast message after deleting one or multiple access tokens."}, "noAccessTokenSelected": {"message": "No access token selected to revoke", "description": "Toast error message after trying to delete access tokens but not selecting any access tokens."}, "submenu": {"message": "Submenu"}, "from": {"message": "From"}, "to": {"message": "To"}, "member": {"message": "Member"}, "update": {"message": "Update"}, "plusNMore": {"message": "+ $QUANTITY$ more", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "groupInfo": {"message": "Group info"}, "editGroupMembersDesc": {"message": "Grant members access to the group's assigned collections."}, "editGroupCollectionsDesc": {"message": "Grant access to collections by adding them to this group."}, "restrictedCollectionAssignmentDesc": {"message": "You can only assign collections you manage."}, "selectMembers": {"message": "Select members"}, "selectCollections": {"message": "Select collections"}, "role": {"message": "Role"}, "removeMember": {"message": "Remove member"}, "collection": {"message": "Collection"}, "noCollection": {"message": "No collection"}, "noCollectionsAdded": {"message": "No collections added"}, "noMembersAdded": {"message": "No members added"}, "noGroupsAdded": {"message": "No groups added"}, "group": {"message": "Group"}, "domainVerification": {"message": "Domain verification"}, "newDomain": {"message": "New domain"}, "noDomains": {"message": "No domains"}, "noDomainsSubText": {"message": "Connecting a domain allows members to skip the SSO identifier field during Login with SSO."}, "copyDnsTxtRecord": {"message": "Copy DNS TXT record"}, "dnsTxtRecord": {"message": "DNS TXT record"}, "dnsTxtRecordInputHint": {"message": "Copy and paste the TXT record into your DNS Provider."}, "removeDomain": {"message": "Remove domain"}, "removeDomainWarning": {"message": "Removing a domain cannot be undone. Are you sure you want to continue?"}, "domainRemoved": {"message": "Domain removed"}, "domainSaved": {"message": "Domain saved"}, "duplicateDomainError": {"message": "You can't claim the same domain twice."}, "domainNotAvailable": {"message": "Someone else is using $DOMAIN$. Use a different domain to continue.", "placeholders": {"DOMAIN": {"content": "$1", "example": "bitwarden.com"}}}, "domainNameTh": {"message": "Name"}, "domainStatusTh": {"message": "Status"}, "lastChecked": {"message": "Last checked"}, "editDomain": {"message": "Edit domain"}, "domainFormInvalid": {"message": "There are form errors that need your attention"}, "addedDomain": {"message": "Added domain $DOMAIN$", "placeholders": {"DOMAIN": {"content": "$1", "example": "bitwarden.com"}}}, "removedDomain": {"message": "Removed domain $DOMAIN$", "placeholders": {"DOMAIN": {"content": "$1", "example": "bitwarden.com"}}}, "verificationRequiredForActionSetPinToContinue": {"message": "Verification required for this action. Set a PIN to continue."}, "setPin": {"message": "Set PIN"}, "verifyWithBiometrics": {"message": "Verify with biometrics"}, "awaitingConfirmation": {"message": "Awaiting confirmation"}, "couldNotCompleteBiometrics": {"message": "Could not complete biometrics."}, "needADifferentMethod": {"message": "Need a different method?"}, "useMasterPassword": {"message": "Use master password"}, "usePin": {"message": "Use PIN"}, "useBiometrics": {"message": "Use biometrics"}, "enterVerificationCodeSentToEmail": {"message": "Enter the verification code that was sent to your email."}, "resendCode": {"message": "Resend code"}, "memberColumnHeader": {"message": "Member"}, "groupSlashMemberColumnHeader": {"message": "Group/Member"}, "selectGroupsAndMembers": {"message": "Select groups and members"}, "selectGroups": {"message": "Select groups"}, "userPermissionOverrideHelperDesc": {"message": "Permissions set for a member will replace permissions set by that member's group."}, "noMembersOrGroupsAdded": {"message": "No members or groups added"}, "deleted": {"message": "Deleted"}, "memberStatusFilter": {"message": "Member status filter"}, "inviteMember": {"message": "Invite member"}, "addSponsorship": {"message": "Add sponsorship"}, "needsConfirmation": {"message": "Needs confirmation"}, "memberRole": {"message": "Member role"}, "moreFromBitwarden": {"message": "More from Bitwarden"}, "switchProducts": {"message": "Switch products"}, "freeOrgInvLimitReachedManageBilling": {"message": "Free organizations may have up to $SEATCOUNT$ members. Upgrade to a paid plan to invite more members.", "placeholders": {"seatcount": {"content": "$1", "example": "2"}}}, "freeOrgInvLimitReachedNoManageBilling": {"message": "Free organizations may have up to $SEATCOUNT$ members. Contact your organization owner to upgrade.", "placeholders": {"seatcount": {"content": "$1", "example": "2"}}}, "teamsStarterPlanInvLimitReachedManageBilling": {"message": "Teams Starter plans may have up to $SEATCOUNT$ members. Upgrade to your plan to invite more members.", "placeholders": {"seatcount": {"content": "$1", "example": "10"}}}, "teamsStarterPlanInvLimitReachedNoManageBilling": {"message": "Teams Starter plans may have up to $SEATCOUNT$ members. Contact your organization owner to upgrade your plan and invite more members.", "placeholders": {"seatcount": {"content": "$1", "example": "10"}}}, "freeOrgMaxCollectionReachedManageBilling": {"message": "Free organizations may have up to $COLLECTIONCOUNT$ collections. Upgrade to a paid plan to add more collections.", "placeholders": {"COLLECTIONCOUNT": {"content": "$1", "example": "2"}}}, "freeOrgMaxCollectionReachedNoManageBilling": {"message": "Free organizations may have up to $COLLECTIONCOUNT$ collections. Contact your organization owner to upgrade.", "placeholders": {"COLLECTIONCOUNT": {"content": "$1", "example": "2"}}}, "server": {"message": "Server"}, "exportData": {"message": "Export data"}, "exportingOrganizationSecretDataTitle": {"message": "Exporting Organization Secret Data"}, "exportingOrganizationSecretDataDescription": {"message": "Only the Secrets Manager data associated with $ORGANIZATION$ will be exported. Items in other products or from other organizations will not be included.", "placeholders": {"ORGANIZATION": {"content": "$1", "example": "My Org Name"}}}, "fileUpload": {"message": "File upload"}, "upload": {"message": "Upload"}, "acceptedFormats": {"message": "Accepted Formats:"}, "copyPasteImportContents": {"message": "Copy & paste import contents:"}, "or": {"message": "or"}, "unlockWithBiometrics": {"message": "Unlock with biometrics"}, "unlockWithPin": {"message": "Unlock with PIN"}, "unlockWithMasterPassword": {"message": "Unlock with master password"}, "licenseAndBillingManagement": {"message": "License and billing management"}, "automaticSync": {"message": "Automatic sync"}, "manualUpload": {"message": "Manual upload"}, "manualBillingTokenUploadDesc": {"message": "If you do not want to opt into billing sync, manually upload your license here. This will not automatically unlock Families sponsorships."}, "syncLicense": {"message": "Sync License"}, "licenseSyncSuccess": {"message": "Successfully synced license"}, "licenseUploadSuccess": {"message": "Successfully uploaded license"}, "lastLicenseSync": {"message": "Last license sync"}, "billingSyncHelp": {"message": "Billing Sync help"}, "licensePaidFeaturesHelp": {"message": "License paid features help"}, "selfHostGracePeriodHelp": {"message": "After your subscription expires, you have 60 days to apply an updated license file to your organization. Grace period ends $GRACE_PERIOD_END_DATE$.", "placeholders": {"GRACE_PERIOD_END_DATE": {"content": "$1", "example": "May 12, 2024"}}}, "uploadLicense": {"message": "Upload license"}, "projectPeopleDescription": {"message": "Grant groups or people access to this project."}, "projectPeopleSelectHint": {"message": "Type or select people or groups"}, "projectServiceAccountsDescription": {"message": "Grant service accounts access to this project."}, "projectServiceAccountsSelectHint": {"message": "Type or select service accounts"}, "projectEmptyPeopleAccessPolicies": {"message": "Add people or groups to start collaborating"}, "projectEmptyServiceAccountAccessPolicies": {"message": "Add service accounts to grant access"}, "serviceAccountPeopleDescription": {"message": "Grant groups or people access to this service account."}, "serviceAccountProjectsDescription": {"message": "Assign projects to this service account. "}, "serviceAccountEmptyProjectAccesspolicies": {"message": "Add projects to grant access"}, "canReadWrite": {"message": "Can read, write"}, "groupSlashUser": {"message": "Group/User"}, "lowKdfIterations": {"message": "Low KDF Iterations"}, "updateLowKdfIterationsDesc": {"message": "Update your encryption settings to meet new security recommendations and improve account protection."}, "kdfSettingsChangeLogoutWarning": {"message": "Proceeding will log you out of all active sessions. You will need to log back in and complete two-step login, if any. We recommend exporting your vault before changing your encryption settings to prevent data loss."}, "secretsManager": {"message": "Secrets Manager"}, "secretsManagerAccessDescription": {"message": "Activate user access to Secrets Manager."}, "userAccessSecretsManagerGA": {"message": "This user can access Secrets Manager"}, "important": {"message": "Important:"}, "viewAll": {"message": "View all"}, "showingPortionOfTotal": {"message": "Showing $PORTION$ of $TOTAL$", "placeholders": {"portion": {"content": "$1", "example": "2"}, "total": {"content": "$2", "example": "5"}}}, "resolveTheErrorsBelowAndTryAgain": {"message": "Resolve the errors below and try again."}, "description": {"message": "Description"}, "errorReadingImportFile": {"message": "An error occurred when trying to read the import file"}, "accessedSecret": {"message": "Accessed secret $SECRET_ID$.", "placeholders": {"secret_id": {"content": "$1", "example": "4d34e8a8"}}}, "sdk": {"message": "SDK", "description": "Software Development Kit"}, "createAnAccount": {"message": "Create an account"}, "createSecret": {"message": "Create a secret"}, "createProject": {"message": "Create a project"}, "createServiceAccount": {"message": "Create a service account"}, "downloadThe": {"message": "Download the", "description": "Link to a downloadable resource. This will be used as part of a larger phrase. Example: Download the Secrets Manager CLI"}, "smCLI": {"message": "Secrets Manager CLI"}, "importSecrets": {"message": "Import secrets"}, "getStarted": {"message": "Get started"}, "complete": {"message": "$COMPLETED$/$TOTAL$ Complete", "placeholders": {"COMPLETED": {"content": "$1", "example": "1"}, "TOTAL": {"content": "$2", "example": "4"}}}, "restoreSecret": {"message": "Restore secret"}, "restoreSecrets": {"message": "Restore secrets"}, "restoreSecretPrompt": {"message": "Are you sure you want to restore this secret?"}, "restoreSecretsPrompt": {"message": "Are you sure you want to restore these secrets?"}, "secretRestoredSuccessToast": {"message": "Secret restored"}, "secretsRestoredSuccessToast": {"message": "Secrets restored"}, "selectionIsRequired": {"message": "Selection is required."}, "saPeopleWarningTitle": {"message": "Access tokens still available"}, "saPeopleWarningMessage": {"message": "Removing people from a service account does not remove the access tokens they created. For security best practice, it is recommended to revoke access tokens created by people removed from a service account."}, "smAccessRemovalWarningProjectTitle": {"message": "Remove access to this project"}, "smAccessRemovalWarningProjectMessage": {"message": "This action will remove your access to the project."}, "smAccessRemovalWarningSaTitle": {"message": "Remove access to this service account"}, "smAccessRemovalWarningSaMessage": {"message": "This action will remove your access to the service account."}, "removeAccess": {"message": "Remove access"}, "checkForBreaches": {"message": "Check known data breaches for this password"}, "exposedMasterPassword": {"message": "Exposed Master Password"}, "exposedMasterPasswordDesc": {"message": "Password found in a data breach. Use a unique password to protect your account. Are you sure you want to use an exposed password?"}, "weakAndExposedMasterPassword": {"message": "Weak and Exposed Master Password"}, "weakAndBreachedMasterPasswordDesc": {"message": "Weak password identified and found in a data breach. Use a strong and unique password to protect your account. Are you sure you want to use this password?"}, "characterMinimum": {"message": "$LENGTH$ character minimum", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "masterPasswordMinimumlength": {"message": "Master password must be at least $LENGTH$ characters long.", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "inputTrimValidator": {"message": "Input must not contain only whitespace.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "notAvailableForFreeOrganization": {"message": "This feature is not available for free organizations. Contact your organization owner to upgrade."}, "smProjectSecretsNoItemsNoAccess": {"message": "Contact your organization's admin to manage secrets for this project.", "description": "The message shown to the user under a project's secrets tab when the user only has read access to the project."}, "enforceOnLoginDesc": {"message": "Require existing members to change their passwords"}, "smProjectDeleteAccessRestricted": {"message": "You don't have permissions to delete this project", "description": "The individual description shown to the user when the user doesn't have access to delete a project."}, "smProjectsDeleteBulkConfirmation": {"message": "The following projects can not be deleted. Would you like to continue?", "description": "The message shown to the user when bulk deleting projects and the user doesn't have access to some projects."}, "updateKdfSettings": {"message": "Update KDF settings"}, "loginInitiated": {"message": "<PERSON><PERSON> initiated"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Remember this device to make future logins seamless"}, "deviceApprovalRequired": {"message": "Device approval required. Select an approval option below:"}, "deviceApprovalRequiredV2": {"message": "Device approval required"}, "selectAnApprovalOptionBelow": {"message": "Select an approval option below"}, "rememberThisDevice": {"message": "Remember this device"}, "uncheckIfPublicDevice": {"message": "Uncheck if using a public device"}, "approveFromYourOtherDevice": {"message": "Approve from your other device"}, "requestAdminApproval": {"message": "Request admin approval"}, "trustedDeviceEncryption": {"message": "Trusted device encryption"}, "trustedDevices": {"message": "Trusted devices"}, "memberDecryptionOptionTdeDescPart1": {"message": "Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "memberDecryptionOptionTdeDescLink1": {"message": "single organization", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "memberDecryptionOptionTdeDescPart2": {"message": "policy,", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "memberDecryptionOptionTdeDescLink2": {"message": "SSO required", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "memberDecryptionOptionTdeDescPart3": {"message": "policy, and", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "memberDecryptionOptionTdeDescLink3": {"message": "account recovery administration", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "memberDecryptionOptionTdeDescPart4": {"message": "policy will turn on when this option is used.", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Your organization permissions were updated, requiring you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Your organization requires you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "out of $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "notFound": {"message": "$RESOURCE$ not found", "placeholders": {"resource": {"content": "$1", "example": "Service Account"}}}, "verificationRequired": {"message": "Verification required", "description": "Default title for the user verification dialog."}, "recoverAccount": {"message": "Recover account"}, "updatedTempPassword": {"message": "User updated a password issued through account recovery."}, "activatedAccessToSecretsManager": {"message": "Activated access to Secrets Manager", "description": "Confirmation message that one or more users gained access to Secrets Manager"}, "activateAccess": {"message": "Activate access"}, "bulkEnableSecretsManagerDescription": {"message": "Grant the following members access to Secrets Manager. The role granted in the Password Manager will apply to Secrets Manager.", "description": "This description is shown to an admin when they are attempting to add more users to Secrets Manager."}, "activateSecretsManager": {"message": "Activate Secrets Manager"}, "yourOrganizationsFingerprint": {"message": "Your organization's fingerprint phrase", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their organization's public key with another user, for the purposes of sharing."}, "deviceApprovals": {"message": "Device approvals"}, "deviceApprovalsDesc": {"message": "Approve login requests below to allow the requesting member to finish logging in. Unapproved requests expire after 1 week. Verify the member’s information before approving."}, "deviceInfo": {"message": "Device info"}, "time": {"message": "Time"}, "denyAllRequests": {"message": "Deny all requests"}, "denyRequest": {"message": "Deny request"}, "approveRequest": {"message": "Approve request"}, "deviceApproved": {"message": "Device approved"}, "deviceRemoved": {"message": "<PERSON>ce removed"}, "removeDevice": {"message": "Remove device"}, "removeDeviceConfirmation": {"message": "Are you sure you want to remove this device?"}, "noDeviceRequests": {"message": "No device requests"}, "noDeviceRequestsDesc": {"message": "Member device approval requests will appear here"}, "loginRequestDenied": {"message": "<PERSON><PERSON> request denied"}, "allLoginRequestsDenied": {"message": "All login requests denied"}, "loginRequestApproved": {"message": "<PERSON>gin request approved"}, "removeOrgUserNoMasterPasswordTitle": {"message": "Account does not have master password"}, "removeOrgUserNoMasterPasswordDesc": {"message": "Removing $USER$ without setting a master password for them may restrict access to their full account. Are you sure you want to continue?", "placeholders": {"user": {"content": "$1", "example": "<PERSON>"}}}, "noMasterPassword": {"message": "No master password"}, "removeMembersWithoutMasterPasswordWarning": {"message": "Removing members who do not have master passwords without setting one for them may restrict access to their full account."}, "approvedAuthRequest": {"message": "Approved device for $ID$.", "placeholders": {"id": {"content": "$1", "example": "First 8 Character of a GUID"}}}, "rejectedAuthRequest": {"message": "Denied device for $ID$.", "placeholders": {"id": {"content": "$1", "example": "First 8 Character of a GUID"}}}, "requestedDeviceApproval": {"message": "Requested device approval."}, "tdeOffboardingPasswordSet": {"message": "User set a master password during TDE offboarding."}, "startYour7DayFreeTrialOfBitwardenFor": {"message": "Start your 7-Day free trial of Bitwarden for $ORG$", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "startYour7DayFreeTrialOfBitwardenSecretsManagerFor": {"message": "Start your 7-Day free trial of Bitwarden Secrets Manager for $ORG$", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "next": {"message": "Next"}, "ssoLoginIsRequired": {"message": "SSO login is required"}, "selectedRegionFlag": {"message": "Selected region flag"}, "accountSuccessfullyCreated": {"message": "Account successfully created!"}, "adminApprovalRequested": {"message": "Admin approval requested"}, "adminApprovalRequestSentToAdmins": {"message": "Your request has been sent to your admin."}, "troubleLoggingIn": {"message": "Trouble logging in?"}, "loginApproved": {"message": "Login approved"}, "userEmailMissing": {"message": "User email missing"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Active user email not found. Logging you out."}, "deviceTrusted": {"message": "<PERSON><PERSON> trusted"}, "inviteUsers": {"message": "Invite Users"}, "secretsManagerForPlan": {"message": "Secrets Manager for $PLAN$", "placeholders": {"plan": {"content": "$1", "example": "Teams"}}}, "secretsManagerForPlanDesc": {"message": "For engineering and DevOps teams to manage secrets throughout the software development lifecycle."}, "free2PersonOrganization": {"message": "Free 2-person Organizations"}, "unlimitedSecrets": {"message": "Unlimited secrets"}, "unlimitedProjects": {"message": "Unlimited projects"}, "projectsIncluded": {"message": "$COUNT$ projects included", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "serviceAccountsIncluded": {"message": "$COUNT$ service accounts included", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "additionalServiceAccountCost": {"message": "$COST$ per month for additional service accounts", "placeholders": {"cost": {"content": "$1", "example": "$0.50"}}}, "subscribeToSecretsManager": {"message": "Subscribe to Secrets Manager"}, "addSecretsManagerUpgradeDesc": {"message": "Add Secrets Manager to your upgraded plan to maintain access to any secrets created with your previous plan."}, "additionalServiceAccounts": {"message": "Additional service accounts"}, "includedServiceAccounts": {"message": "Your plan comes with $COUNT$ service accounts.", "placeholders": {"count": {"content": "$1", "example": "50"}}}, "addAdditionalServiceAccounts": {"message": "You can add additional service accounts for $COST$ per month.", "placeholders": {"cost": {"content": "$1", "example": "$0.50"}}}, "collectionManagement": {"message": "Collection management"}, "collectionManagementDesc": {"message": "Manage the collection behavior for the organization"}, "limitCollectionCreationDesc": {"message": "Limit collection creation to owners and admins"}, "limitCollectionDeletionDesc": {"message": "Limit collection deletion to owners and admins"}, "limitItemDeletionDescription": {"message": "Limit item deletion to members with the Manage collection permissions"}, "allowAdminAccessToAllCollectionItemsDesc": {"message": "Owners and admins can manage all collections and items"}, "updatedCollectionManagement": {"message": "Updated collection management setting"}, "passwordManagerPlanPrice": {"message": "Password Manager plan price"}, "secretsManagerPlanPrice": {"message": "Secrets Manager plan price"}, "passwordManager": {"message": "Password Manager"}, "freeOrganization": {"message": "Free Organization"}, "limitServiceAccounts": {"message": "Limit service accounts (optional)"}, "limitServiceAccountsDesc": {"message": "Set a limit for your service accounts. Once this limit is reached, you will not be able to create new service accounts."}, "serviceAccountLimit": {"message": "Service account limit (optional)"}, "maxServiceAccountCost": {"message": "Max potential service account cost"}, "loggedInExclamation": {"message": "Logged in!"}, "beta": {"message": "Beta"}, "assignCollectionAccess": {"message": "Assign collection access"}, "editedCollections": {"message": "Edited collections"}, "baseUrl": {"message": "Server URL"}, "selfHostBaseUrl": {"message": "Self-host server URL", "description": "Label for field requesting a self-hosted integration service URL"}, "alreadyHaveAccount": {"message": "Already have an account?"}, "toggleSideNavigation": {"message": "Toggle side navigation"}, "skipToContent": {"message": "Skip to content"}, "managePermissionRequired": {"message": "At least one member or group must have can manage permission."}, "typePasskey": {"message": "Passkey"}, "passkeyNotCopied": {"message": "Passkey will not be copied"}, "passkeyNotCopiedAlert": {"message": "The passkey will not be copied to the cloned item. Do you want to continue cloning this item?"}, "modifiedCollectionManagement": {"message": "Modified collection management setting $ID$.", "placeholders": {"id": {"content": "$1", "example": "Unique ID"}}}, "seeDetailedInstructions": {"message": "See detailed instructions on our help site at", "description": "This is followed a by a hyperlink to the help website."}, "installBrowserExtension": {"message": "Install browser extension"}, "installBrowserExtensionDetails": {"message": "Use the extension to quickly save logins and auto-fill forms without opening the web app."}, "projectAccessUpdated": {"message": "Project access updated"}, "unexpectedErrorSend": {"message": "An unexpected error has occurred while loading this Send. Try again later."}, "seatLimitReached": {"message": "Seat limit has been reached"}, "contactYourProvider": {"message": "Contact your provider to purchase additional seats."}, "seatLimitReachedContactYourProvider": {"message": "Seat limit has been reached. Contact your provider to purchase additional seats."}, "collectionAccessRestricted": {"message": "Collection access is restricted"}, "readOnlyCollectionAccess": {"message": "You do not have access to manage this collection."}, "grantManageCollectionWarningTitle": {"message": "Missing Manage Collection Permissions"}, "grantManageCollectionWarning": {"message": "Grant Manage collection permissions to allow full collection management including deletion of collection."}, "grantCollectionAccess": {"message": "Grant groups or members access to this collection."}, "grantCollectionAccessMembersOnly": {"message": "Grant members access to this collection."}, "adminCollectionAccess": {"message": "Administrators can access and manage collections."}, "serviceAccountAccessUpdated": {"message": "Service account access updated"}, "commonImportFormats": {"message": "Common formats", "description": "Label indicating the most common import formats"}, "maintainYourSubscription": {"message": "To maintain your subscription for $ORG$, ", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'To maintain your subscription for $ORG$, add a payment method.'", "placeholders": {"org": {"content": "$1", "example": "Example Inc."}}}, "addAPaymentMethod": {"message": "add a payment method", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'To maintain your subscription for $ORG$, add a payment method'"}, "organizationInformation": {"message": "Organization information"}, "confirmationDetails": {"message": "Confirmation details"}, "smFreeTrialThankYou": {"message": "Thank you for signing up for Bitwarden Secrets Manager!"}, "smFreeTrialConfirmationEmail": {"message": "We've sent a confirmation email to your email at "}, "sorryToSeeYouGo": {"message": "Sorry to see you go! Help improve Bitwarden by sharing why you're canceling.", "description": "A message shown to users as part of an offboarding survey asking them to provide more information on their subscription cancelation."}, "selectCancellationReason": {"message": "Select a reason for canceling", "description": "Used as a form field label for a select input on the offboarding survey."}, "anyOtherFeedback": {"message": "Is there any other feedback you'd like to share?", "description": "Used as a form field label for a textarea input on the offboarding survey."}, "missingFeatures": {"message": "Missing features", "description": "An option for the offboarding survey shown when a user cancels their subscription."}, "movingToAnotherTool": {"message": "Moving to another tool", "description": "An option for the offboarding survey shown when a user cancels their subscription."}, "tooDifficultToUse": {"message": "Too difficult to use", "description": "An option for the offboarding survey shown when a user cancels their subscription."}, "notUsingEnough": {"message": "Not using enough", "description": "An option for the offboarding survey shown when a user cancels their subscription."}, "tooExpensive": {"message": "Too expensive", "description": "An option for the offboarding survey shown when a user cancels their subscription."}, "freeForOneYear": {"message": "Free for 1 year"}, "newWebApp": {"message": "Welcome to the new and improved web app. Learn more about what’s changed."}, "releaseBlog": {"message": "Read release blog"}, "adminConsole": {"message": "<PERSON><PERSON>"}, "providerPortal": {"message": "Provider Portal"}, "success": {"message": "Success"}, "restrictedGroupAccess": {"message": "You cannot add yourself to groups."}, "cannotAddYourselfToCollections": {"message": "You cannot add yourself to collections."}, "assign": {"message": "Assign"}, "assignToCollections": {"message": "Assign to collections"}, "assignToTheseCollections": {"message": "Assign to these collections"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Only organization members with access to these collections will be able to see the item."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Only organization members with access to these collections will be able to see the items."}, "selectCollectionsToAssign": {"message": "Select collections to assign"}, "noCollectionsAssigned": {"message": "No collections have been assigned"}, "successfullyAssignedCollections": {"message": "Successfully assigned collections"}, "bulkCollectionAssignmentWarning": {"message": "You have selected $TOTAL_COUNT$ items. You cannot update $READONLY_COUNT$ of the items because you do not have edit permissions.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2", "example": "3"}}}, "addField": {"message": "Add field"}, "editField": {"message": "Edit field"}, "items": {"message": "Items"}, "assignedSeats": {"message": "Assigned seats"}, "assigned": {"message": "Assigned"}, "used": {"message": "Used"}, "remaining": {"message": "Remaining"}, "unlinkOrganization": {"message": "Unlink organization"}, "manageSeats": {"message": "MANAGE SEATS"}, "manageSeatsDescription": {"message": "Adjustments to seats will be reflected in the next billing cycle."}, "unassignedSeatsDescription": {"message": "Unassigned seats"}, "purchaseSeatDescription": {"message": "Additional seats purchased"}, "assignedSeatCannotUpdate": {"message": "Assigned Seats can not be updated. Please contact your organization owner for assistance."}, "subscriptionUpdateFailed": {"message": "Subscription update failed"}, "trial": {"message": "Trial", "description": "A subscription status label."}, "pastDue": {"message": "Past due", "description": "A subscription status label"}, "subscriptionExpired": {"message": "Subscription expired", "description": "The date header used when a subscription is past due."}, "pastDueWarningForChargeAutomatically": {"message": "You have a grace period of $DAYS$ days from your subscription expiration date to maintain your subscription. Please resolve the past due invoices by $SUSPENSION_DATE$.", "placeholders": {"days": {"content": "$1", "example": "11"}, "suspension_date": {"content": "$2", "example": "01/10/2024"}}, "description": "A warning shown to the user when their subscription is past due and they are charged automatically."}, "pastDueWarningForSendInvoice": {"message": "You have a grace period of $DAYS$ days from the date your first unpaid invoice is due to maintain your subscription. Please resolve the past due invoices by $SUSPENSION_DATE$.", "placeholders": {"days": {"content": "$1", "example": "11"}, "suspension_date": {"content": "$2", "example": "01/10/2024"}}, "description": "A warning shown to the user when their subscription is past due and they pay via invoice."}, "unpaidInvoice": {"message": "Unpaid invoice", "description": "The header of a warning box shown to a user whose subscription is unpaid."}, "toReactivateYourSubscription": {"message": "To reactivate your subscription, please resolve the past due invoices.", "description": "The body of a warning box shown to a user whose subscription is unpaid."}, "cancellationDate": {"message": "Cancellation date", "description": "The date header used when a subscription is cancelled."}, "machineAccountsCannotCreate": {"message": "Machine accounts cannot be created in suspended organizations. Please contact your organization owner for assistance."}, "machineAccount": {"message": "Machine account", "description": "A machine user which can be used to automate processes and access secrets in the system."}, "machineAccounts": {"message": "Machine accounts", "description": "The title for the section that deals with machine accounts."}, "newMachineAccount": {"message": "New machine account", "description": "Title for creating a new machine account."}, "machineAccountsNoItemsMessage": {"message": "Create a new machine account to get started automating secret access.", "description": "Message to encourage the user to start creating machine accounts."}, "machineAccountsNoItemsTitle": {"message": "Nothing to show yet", "description": "Title to indicate that there are no machine accounts to display."}, "deleteMachineAccounts": {"message": "Delete machine accounts", "description": "Title for the action to delete one or multiple machine accounts."}, "deleteMachineAccount": {"message": "Delete machine account", "description": "Title for the action to delete a single machine account."}, "viewMachineAccount": {"message": "View machine account", "description": "Action to view the details of a machine account."}, "deleteMachineAccountDialogMessage": {"message": "Deleting machine account $MACHINE_ACCOUNT$ is permanent and irreversible.", "placeholders": {"machine_account": {"content": "$1", "example": "Machine account name"}}}, "deleteMachineAccountsDialogMessage": {"message": "Deleting machine accounts is permanent and irreversible."}, "deleteMachineAccountsConfirmMessage": {"message": "Delete $COUNT$ machine accounts", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "deleteMachineAccountToast": {"message": "Machine account deleted"}, "deleteMachineAccountsToast": {"message": "Machine accounts deleted"}, "searchMachineAccounts": {"message": "Search machine accounts", "description": "Placeholder text for searching machine accounts."}, "editMachineAccount": {"message": "Edit machine account", "description": "Title for editing a machine account."}, "machineAccountName": {"message": "Machine account name", "description": "Label for the name of a machine account"}, "machineAccountCreated": {"message": "Machine account created", "description": "Notifies that a new machine account has been created"}, "machineAccountUpdated": {"message": "Machine account updated", "description": "Notifies that a machine account has been updated"}, "projectMachineAccountsDescription": {"message": "Grant machine accounts access to this project."}, "projectMachineAccountsSelectHint": {"message": "Type or select machine accounts"}, "projectEmptyMachineAccountAccessPolicies": {"message": "Add machine accounts to grant access"}, "machineAccountPeopleDescription": {"message": "Grant groups or people access to this machine account."}, "machineAccountProjectsDescription": {"message": "Assign projects to this machine account. "}, "createMachineAccount": {"message": "Create a machine account"}, "maPeopleWarningMessage": {"message": "Removing people from a machine account does not remove the access tokens they created. For security best practice, it is recommended to revoke access tokens created by people removed from a machine account."}, "smAccessRemovalWarningMaTitle": {"message": "Remove access to this machine account"}, "smAccessRemovalWarningMaMessage": {"message": "This action will remove your access to the machine account."}, "machineAccountsIncluded": {"message": "$COUNT$ machine accounts included", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "additionalMachineAccountCost": {"message": "$COST$ per month for additional machine accounts", "placeholders": {"cost": {"content": "$1", "example": "$0.50"}}}, "additionalMachineAccounts": {"message": "Additional machine accounts"}, "includedMachineAccounts": {"message": "Your plan comes with $COUNT$ machine accounts.", "placeholders": {"count": {"content": "$1", "example": "50"}}}, "addAdditionalMachineAccounts": {"message": "You can add additional machine accounts for $COST$ per month.", "placeholders": {"cost": {"content": "$1", "example": "$0.50"}}}, "limitMachineAccounts": {"message": "Limit machine accounts (optional)"}, "limitMachineAccountsDesc": {"message": "Set a limit for your machine accounts. Once this limit is reached, you will not be able to create new machine accounts."}, "machineAccountLimit": {"message": "Machine account limit (optional)"}, "maxMachineAccountCost": {"message": "Max potential machine account cost"}, "machineAccountAccessUpdated": {"message": "Machine account access updated"}, "restrictedGroupAccessDesc": {"message": "You cannot add yourself to a group."}, "deleteProvider": {"message": "Delete provider"}, "deleteProviderConfirmation": {"message": "Deleting a provider is permanent and irreversible. Enter your master password to confirm the deletion of the provider and all associated data."}, "deleteProviderName": {"message": "Cannot delete $ID$", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "deleteProviderWarningDescription": {"message": "You must unlink all clients before you can delete $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "providerDeleted": {"message": "Provider deleted"}, "providerDeletedDesc": {"message": "The Provider and all associated data has been deleted."}, "deleteProviderRecoverConfirmDesc": {"message": "You have requested to delete this Provider. Use the button below to confirm."}, "deleteProviderWarning": {"message": "Deleting your provider is permanent. It cannot be undone."}, "errorAssigningTargetCollection": {"message": "Error assigning target collection."}, "errorAssigningTargetFolder": {"message": "Error assigning target folder."}, "integrationsAndSdks": {"message": "Integrations & SDKs", "description": "The title for the section that deals with integrations and SDKs."}, "integrations": {"message": "Integrations"}, "integrationsDesc": {"message": "Automatically sync secrets from Bitwarden Secrets Manager to a third-party service."}, "sdks": {"message": "SDKs"}, "sdksDesc": {"message": "Use Bitwarden Secrets Manager SDK in the following programming languages to build your own applications."}, "ssoDescStart": {"message": "Configure", "description": "This represents the beginning of a sentence, broken up to include links. The full sentence will be 'Configure single sign-on for Bitwarden using the implementation guide for your Identity Provider."}, "ssoDescEnd": {"message": "for Bitwarden using the implementation guide for your Identity Provider.", "description": "This represents the end of a sentence, broken up to include links. The full sentence will be 'Configure single sign-on for Bitwarden using the implementation guide for your Identity Provider."}, "userProvisioning": {"message": "User provisioning"}, "scimIntegration": {"message": "SCIM"}, "scimIntegrationDescStart": {"message": "Configure ", "description": "This represents the beginning of a sentence, broken up to include links. The full sentence will be 'Configure SCIM (System for Cross-domain Identity Management) to automatically provision users and groups to Bitwarden using the implementation guide for your Identity Provider"}, "scimIntegrationDescEnd": {"message": "(System for Cross-domain Identity Management) to automatically provision users and groups to Bitwarden using the implementation guide for your Identity Provider.", "description": "This represents the end of a sentence, broken up to include links. The full sentence will be 'Configure SCIM (System for Cross-domain Identity Management) to automatically provision users and groups to Bitwarden using the implementation guide for your Identity Provider"}, "bwdc": {"message": "Bitwarden Directory Connector"}, "bwdcDesc": {"message": "Configure Bitwarden Directory Connector to automatically provision users and groups using the implementation guide for your Identity Provider."}, "eventManagement": {"message": "Event management"}, "eventManagementDesc": {"message": "Integrate Bitwarden event logs with your SIEM (system information and event management) system by using the implementation guide for your platform."}, "deviceManagement": {"message": "Device management"}, "deviceManagementDesc": {"message": "Configure device management for Bitwarden using the implementation guide for your platform."}, "deviceIdMissing": {"message": "Device ID is missing"}, "deviceTypeMissing": {"message": "Device type is missing"}, "deviceCreationDateMissing": {"message": "Device creation date is missing"}, "desktopRequired": {"message": "Desktop required"}, "reopenLinkOnDesktop": {"message": "Reopen this link from your email on a desktop."}, "integrationCardTooltip": {"message": "Launch $INTEGRATION$ implementation guide.", "placeholders": {"integration": {"content": "$1", "example": "Google"}}}, "smIntegrationTooltip": {"message": "Set up $INTEGRATION$.", "placeholders": {"integration": {"content": "$1", "example": "Google"}}}, "smSdkTooltip": {"message": "View $SDK$ repository", "placeholders": {"sdk": {"content": "$1", "example": "Rust"}}}, "integrationCardAriaLabel": {"message": "open $INTEGRATION$ implementation guide in a new tab.", "placeholders": {"integration": {"content": "$1", "example": "google"}}}, "smSdkAriaLabel": {"message": "view $SDK$ repository in a new tab.", "placeholders": {"sdk": {"content": "$1", "example": "rust"}}}, "smIntegrationCardAriaLabel": {"message": "set up $INTEGRATION$ implementation guide in a new tab.", "placeholders": {"integration": {"content": "$1", "example": "google"}}}, "createNewClientToManageAsProvider": {"message": "Create a new client organization to manage as a Provider. Additional seats will be reflected in the next billing cycle."}, "selectAPlan": {"message": "Select a plan"}, "thirtyFivePercentDiscount": {"message": "35% Discount"}, "monthPerMember": {"message": "month per member"}, "monthPerMemberBilledAnnually": {"message": "month per member billed annually"}, "seats": {"message": "Seats"}, "addOrganization": {"message": "Add organization"}, "createdNewClient": {"message": "Successfully created new client"}, "noAccess": {"message": "No access"}, "collectionAdminConsoleManaged": {"message": "This collection is only accessible from the admin console"}, "organizationOptionsMenu": {"message": "Toggle Organization Menu"}, "vaultItemSelect": {"message": "Select vault item"}, "collectionItemSelect": {"message": "Select collection item"}, "manageBillingFromProviderPortalMessage": {"message": "Manage billing from the Provider Portal"}, "continueSettingUp": {"message": "Continue setting up Bitwarden"}, "continueSettingUpFreeTrial": {"message": "Continue setting up your free trial of Bitwarden"}, "continueSettingUpPasswordManager": {"message": "Continue setting up Bitwarden Password Manager"}, "continueSettingUpFreeTrialPasswordManager": {"message": "Continue setting up your free trial of Bitwarden Password Manager"}, "continueSettingUpSecretsManager": {"message": "Continue setting up Bitwarden Secrets Manager"}, "continueSettingUpFreeTrialSecretsManager": {"message": "Continue setting up your free trial of Bitwarden Secrets Manager"}, "enterTeamsOrgInfo": {"message": "Enter your Teams organization information"}, "enterFamiliesOrgInfo": {"message": "Enter your Families organization information"}, "enterEnterpriseOrgInfo": {"message": "Enter your Enterprise organization information"}, "viewItemsIn": {"message": "View items in $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Back to $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "back": {"message": "Back", "description": "Button text to navigate back"}, "removeItem": {"message": "Remove $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "viewInfo": {"message": "View info"}, "viewAccess": {"message": "View access"}, "noCollectionsSelected": {"message": "You have not selected any collections."}, "updateName": {"message": "Update name"}, "updatedOrganizationName": {"message": "Updated organization name"}, "providerPlan": {"message": "Managed Service Provider"}, "managedServiceProvider": {"message": "Managed service provider"}, "multiOrganizationEnterprise": {"message": "Multi-organization enterprise"}, "orgSeats": {"message": "Organization Seats"}, "providerDiscount": {"message": "$AMOUNT$% Discount", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "lowKDFIterationsBanner": {"message": "Low KDF iterations. Increase your iterations to improve the security of your account."}, "changeKDFSettings": {"message": "Change KDF settings"}, "secureYourInfrastructure": {"message": "Secure your infrastructure"}, "protectYourFamilyOrBusiness": {"message": "Protect your family or business"}, "upgradeOrganizationCloseSecurityGaps": {"message": "Close security gaps with monitoring reports"}, "upgradeOrganizationCloseSecurityGapsDesc": {"message": "Stay ahead of security vulnerabilities by upgrading to a paid plan for enhanced monitoring."}, "approveAllRequests": {"message": "Approve all requests"}, "allLoginRequestsApproved": {"message": "All login requests approved"}, "payPal": {"message": "PayPal"}, "bitcoin": {"message": "Bitcoin"}, "updatedTaxInformation": {"message": "Updated tax information"}, "billingInvalidTaxIdError": {"message": "Invalid tax ID, if you believe this is an error please contact support."}, "billingTaxIdTypeInferenceError": {"message": "We were unable to validate your tax ID, if you believe this is an error please contact support."}, "billingPreviewInvalidTaxIdError": {"message": "Invalid tax ID, if you believe this is an error please contact support."}, "billingPreviewInvoiceError": {"message": "An error occurred while previewing the invoice. Please try again later."}, "unverified": {"message": "Unverified"}, "verified": {"message": "Verified"}, "viewSecret": {"message": "View secret"}, "noClients": {"message": "There are no clients to list"}, "providerBillingEmailHint": {"message": "This email address will receive all invoices pertaining to this provider", "description": "A hint that shows up on the Provider setup page to inform the admin the billing email will receive the provider's invoices."}, "upgradeOrganizationEnterprise": {"message": "Identify security risks by auditing member access"}, "onlyAvailableForEnterpriseOrganization": {"message": "Quickly view member access across the organization by upgrading to an Enterprise plan."}, "date": {"message": "Date"}, "exportClientReport": {"message": "Export client report"}, "memberAccessReport": {"message": "Member access"}, "memberAccessReportDesc": {"message": "Ensure members have access to the right credentials and their accounts are secure. Use this report to obtain a CSV of member access and account configurations."}, "memberAccessReportPageDesc": {"message": "Audit organization member access across groups, collections, and collection items. The CSV export provides a detailed breakdown per member, including information on collection permissions and account configurations."}, "memberAccessReportNoCollection": {"message": "(No Collection)"}, "memberAccessReportNoCollectionPermission": {"message": "(No Collection Permission)"}, "memberAccessReportNoGroup": {"message": "(No Group)"}, "memberAccessReportTwoFactorEnabledTrue": {"message": "On"}, "memberAccessReportTwoFactorEnabledFalse": {"message": "Off"}, "memberAccessReportAuthenticationEnabledTrue": {"message": "On"}, "memberAccessReportAuthenticationEnabledFalse": {"message": "Off"}, "higherKDFIterations": {"message": "Higher KDF iterations can help protect your master password from being brute forced by an attacker."}, "incrementsOf100,000": {"message": "increments of 100,000"}, "smallIncrements": {"message": "small increments"}, "kdfIterationRecommends": {"message": "We recommend 600,000 or more"}, "kdfToHighWarningIncreaseInIncrements": {"message": "For older devices, setting your KDF too high may lead to performance issues. Increase the value in $VALUE$ and test your devices.", "placeholders": {"value": {"content": "$1", "example": "increments of 100,000"}}}, "providerReinstate": {"message": " Contact Customer Support to reinstate your subscription."}, "secretPeopleDescription": {"message": "Grant groups or people access to this secret. Permissions set for people will override permissions set by groups."}, "secretPeopleEmptyMessage": {"message": "Add people or groups to share access to this secret"}, "secretMachineAccountsDescription": {"message": "<PERSON> machine accounts access to this secret."}, "secretMachineAccountsEmptyMessage": {"message": "Add machine accounts to grant access to this secret"}, "smAccessRemovalWarningSecretTitle": {"message": "Remove access to this secret"}, "smAccessRemovalSecretMessage": {"message": "This action will remove your access to this secret."}, "invoice": {"message": "Invoice"}, "unassignedSeatsAvailable": {"message": "You have $SEATS$ unassigned seats available.", "placeholders": {"seats": {"content": "$1", "example": "10"}}, "description": "A message showing how many unassigned seats are available for a provider."}, "contactYourProviderForAdditionalSeats": {"message": "Contact your provider admin to purchase additional seats."}, "open": {"message": "Open", "description": "The status of an invoice."}, "uncollectible": {"message": "Uncollectible", "description": "The status of an invoice."}, "clientDetails": {"message": "Client details"}, "downloadCSV": {"message": "Download CSV"}, "monthlySubscriptionUserSeatsMessage": {"message": "Adjustments to your subscription will result in prorated charges to your billing totals on your next billing period. "}, "annualSubscriptionUserSeatsMessage": {"message": "Adjustments to your subscription will result in prorated charges on a monthly billing cycle. "}, "billingHistoryDescription": {"message": "Download a CSV to obtain client details for each billing date. Prorated charges are not included in the CSV and may vary from the linked invoice. For the most accurate billing details, refer to your monthly invoices.", "description": "A paragraph on the Billing History page of the Provider Portal letting users know they can download a CSV report for their invoices that does not include prorations."}, "noInvoicesToList": {"message": "There are no invoices to list", "description": "A paragraph on the Billing History page of the Provider Portal letting users know they can download a CSV report for their invoices that does not include prorations."}, "providerClientVaultPrivacyNotification": {"message": "Notice: Later this month, client vault privacy will be improved and provider members will no longer have direct access to client vault items. For questions,", "description": "This will be displayed as part of a larger sentence. The whole sentence reads: 'Notice: Later this month, client vault privacy will be improved and provider members will no longer have direct access to client vault items. For questions, please contact Bitwarden support'."}, "contactBitwardenSupport": {"message": "contact Bitwarden support.", "description": "This will be displayed as part of a larger sentence. The whole sentence reads: 'Notice: Later this month, client vault privacy will be improved and provider members will no longer have direct access to client vault items. For questions, please contact Bitwarden support'. 'Bitwarden' should not be translated"}, "sponsored": {"message": "Sponsored"}, "licenseAndBillingManagementDesc": {"message": "After making updates in the Bitwarden cloud server, upload your license file to apply the most recent changes."}, "addToFolder": {"message": "Add to folder"}, "selectFolder": {"message": "Select folder"}, "personalItemTransferWarningSingular": {"message": "1 item will be permanently transferred to the selected organization. You will no longer own this item."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to the selected organization. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 item will be permanently transferred to $ORG$. You will no longer own this item.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to $ORG$. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "data": {"message": "Data"}, "purchasedSeatsRemoved": {"message": "purchased seats removed"}, "environmentVariables": {"message": "Environment variables"}, "organizationId": {"message": "Organization ID"}, "projectIds": {"message": "Project IDs"}, "projectId": {"message": "Project ID"}, "projectsAccessedByMachineAccount": {"message": "The following projects can be accessed by this machine account."}, "config": {"message": "Config"}, "learnMoreAboutEmergencyAccess": {"message": "Learn more about emergency access"}, "learnMoreAboutMatchDetection": {"message": "Learn more about match detection"}, "learnMoreAboutMasterPasswordReprompt": {"message": "Learn more about master password re-prompt"}, "learnMoreAboutSearchingYourVault": {"message": "Learn more about searching your vault"}, "learnMoreAboutYourAccountFingerprintPhrase": {"message": "Learn about your account fingerprint phrase"}, "impactOfRotatingYourEncryptionKey": {"message": "Impact of rotating your encryption key"}, "learnMoreAboutEncryptionAlgorithms": {"message": "Learn more about encryption algorithms"}, "learnMoreAboutKDFIterations": {"message": "Learn more about KDF iterations"}, "learnMoreAboutLocalization": {"message": "Learn more about localization"}, "learnMoreAboutWebsiteIcons": {"message": "Learn more about using website icons"}, "learnMoreAboutUserAccess": {"message": "Learn more about user access"}, "learnMoreAboutMemberRoles": {"message": "Learn more about member roles and permissions"}, "whatIsACvvNumber": {"message": "What is a CVV number?"}, "learnMoreAboutApi": {"message": "Learn more about Bitwarden's API"}, "fileSend": {"message": "File Send"}, "fileSends": {"message": "File Sends"}, "textSend": {"message": "Text Send"}, "textSends": {"message": "Text Sends"}, "includesXMembers": {"message": "for $COUNT$ member", "placeholders": {"count": {"content": "$1", "example": "5"}}}, "costPerMember": {"message": "$COST$", "placeholders": {"cost": {"content": "$1", "example": "$3"}}}, "optionalOnPremHosting": {"message": "Optional on-premises hosting"}, "upgradeFreeOrganization": {"message": "Upgrade your $NAME$ organization ", "placeholders": {"name": {"content": "$1", "example": "Teams"}}}, "includeSsoAuthenticationMessage": {"message": "SSO Authentication"}, "familiesPlanInvLimitReachedManageBilling": {"message": "Families organizations may have up to $SEATCOUNT$ members. Upgrade to a paid plan to invite more members.", "placeholders": {"seatcount": {"content": "$1", "example": "6"}}}, "familiesPlanInvLimitReachedNoManageBilling": {"message": "Families organizations may have up to $SEATCOUNT$ members. Contact your organization owner to upgrade.", "placeholders": {"seatcount": {"content": "$1", "example": "6"}}}, "upgradePlans": {"message": "Upgrade your plan to invite members and experience powerful security features."}, "upgradeDiscount": {"message": "Save $AMOUNT$%", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "enterprisePlanUpgradeMessage": {"message": "Advanced capabilities for larger organizations"}, "teamsPlanUpgradeMessage": {"message": "Resilient protection for growing teams"}, "teamsInviteMessage": {"message": "Invite unlimited members"}, "accessToCreateGroups": {"message": "Access to create groups"}, "syncGroupsAndUsersFromDirectory": {"message": "Sync groups and users from a directory"}, "familyPlanUpgradeMessage": {"message": "Secure your family logins"}, "accessToPremiumFeatures": {"message": "Access to Premium features"}, "additionalStorageGbMessage": {"message": "GB additional storage"}, "sshKeyAlgorithm": {"message": "Key algorithm"}, "sshPrivateKey": {"message": "Private key"}, "sshPublicKey": {"message": "Public key"}, "sshFingerprint": {"message": "Fingerprint"}, "sshKeyFingerprint": {"message": "Fingerprint"}, "sshKeyPrivateKey": {"message": "Private key"}, "sshKeyPublicKey": {"message": "Public key"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "premiumAccounts": {"message": "6 premium accounts"}, "unlimitedSharing": {"message": "Unlimited sharing"}, "unlimitedCollections": {"message": "Unlimited collections"}, "secureDataSharing": {"message": "Secure data sharing"}, "eventLogMonitoring": {"message": "Event log monitoring"}, "directoryIntegration": {"message": "Directory integration"}, "passwordLessSso": {"message": "Passwordless SSO"}, "accountRecovery": {"message": "Account recovery"}, "customRoles": {"message": "Custom roles"}, "unlimitedSecretsStorage": {"message": "Unlimited secrets storage"}, "unlimitedUsers": {"message": "Unlimited users"}, "UpTo50MachineAccounts": {"message": "Up to 50 machine accounts"}, "UpTo20MachineAccounts": {"message": "Up to 20 machine accounts"}, "current": {"message": "Current"}, "secretsManagerSubscriptionInfo": {"message": "Your Secrets Manager subscription will upgrade based on the plan selected"}, "bitwardenPasswordManager": {"message": "Bitwarden Password Manager"}, "secretsManagerComplimentaryPasswordManager": {"message": "Your complimentary one year Password Manager subscription will upgrade to the selected plan. You will not be charged until the complimentary period is over."}, "fileSavedToDevice": {"message": "File saved to device. Manage from your device downloads."}, "publicApi": {"message": "Public API", "description": "The text, 'API', is an acronym and should not be translated."}, "showCharacterCount": {"message": "Show character count"}, "hideCharacterCount": {"message": "Hide character count"}, "editAccess": {"message": "Edit access"}, "textHelpText": {"message": "Use text fields for data like security questions"}, "hiddenHelpText": {"message": "Use hidden fields for sensitive data like a password"}, "checkBoxHelpText": {"message": "Use checkboxes if you'd like to autofill a form's checkbox, like a remember email"}, "linkedHelpText": {"message": "Use a linked field when you are experiencing autofill issues for a specific website."}, "linkedLabelHelpText": {"message": "Enter the the field's html id, name, aria-label, or placeholder."}, "uppercaseDescription": {"message": "Include uppercase characters", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Include lowercase characters", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "Include numbers", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "Include special characters", "description": "Full description for the password generator special characters checkbox"}, "addAttachment": {"message": "Add attachment"}, "maxFileSizeSansPunctuation": {"message": "Maximum file size is 500 MB"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Are you sure you want to permanently delete this attachment?"}, "manageSubscriptionFromThe": {"message": "Manage subscription from the", "description": "This represents the beginning of a sentence. The full sentence will be 'Manage subscription from the Provider Portal', but 'Provider Portal' will be a link and thus cannot be included in the translation file."}, "toHostBitwardenOnYourOwnServer": {"message": "To host Bitwarden on your own server, you will need to upload your license file. To support Free Families plans and advanced billing capabilities for your self-hosted organization, you will need to set up automatic sync in your self-hosted organization."}, "selfHostingTitleProper": {"message": "Self-Hosting"}, "claim-domain-single-org-warning": {"message": "Claiming a domain will turn on the single organization policy."}, "single-org-revoked-user-warning": {"message": "Non-compliant members will be revoked. Administrators can restore members once they leave all other organizations."}, "deleteOrganizationUser": {"message": "Delete $NAME$", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}, "description": "Title for the delete organization user dialog"}}, "deleteOrganizationUserWarningDesc": {"message": "This will permanently delete all items owned by $NAME$. Collection items are not impacted.", "description": "Warning description for the delete organization user dialog", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}}}, "deleteManyOrganizationUsersWarningDesc": {"message": "This will permanently delete all items owned by the following members. Collection items are not impacted.", "description": "Warning description for the bulk delete organization users dialog"}, "organizationUserDeleted": {"message": "Deleted $NAME$", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}}}, "organizationUserDeletedDesc": {"message": "The user was removed from the organization and all associated user data has been deleted."}, "deletedUserId": {"message": "Deleted user $ID$ - an owner / admin deleted the user account", "placeholders": {"id": {"content": "$1", "example": "First 8 Character of a GUID"}}}, "userLeftOrganization": {"message": "User $ID$ left organization", "placeholders": {"id": {"content": "$1", "example": "First 8 Character of a GUID"}}}, "suspendedOrganizationTitle": {"message": "The $ORGANIZATION$ is suspended", "placeholders": {"organization": {"content": "$1", "example": "Acme c"}}}, "suspendedUserOrgMessage": {"message": "Contact your organization owner for assistance."}, "suspendedOwnerOrgMessage": {"message": "To regain access to your organization, add a payment method."}, "deleteMembers": {"message": "Delete members"}, "noSelectedMembersApplicable": {"message": "This action is not applicable to any of the selected members."}, "deletedSuccessfully": {"message": "Deleted successfully"}, "freeFamiliesSponsorship": {"message": "Remove Free Bitwarden Families sponsorship"}, "freeFamiliesSponsorshipPolicyDesc": {"message": "Do not allow members to redeem a Families plan through this organization."}, "verifyBankAccountWithStatementDescriptorWarning": {"message": "Payment with a bank account is only available to customers in the United States. You will be required to verify your bank account. We will make a micro-deposit within the next 1-2 business days. Enter the statement descriptor code from this deposit on the organization's billing page to verify the bank account. Failure to verify the bank account will result in a missed payment and your subscription being suspended."}, "verifyBankAccountWithStatementDescriptorInstructions": {"message": "We have made a micro-deposit to your bank account (this may take 1-2 business days). Enter the six-digit code starting with 'SM' found on the deposit description. Failure to verify the bank account will result in a missed payment and your subscription being suspended."}, "descriptorCode": {"message": "Descriptor code"}, "cannotRemoveViewOnlyCollections": {"message": "You cannot remove collections with View only permissions: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "removeMembers": {"message": "Remove members"}, "devices": {"message": "Devices"}, "deviceListDescription": {"message": "Your account was logged in to each of the devices below. If you do not recognize a device, remove it now."}, "deviceListDescriptionTemp": {"message": "Your account was logged in to each of the devices below."}, "claimedDomains": {"message": "Claimed domains"}, "claimDomain": {"message": "Claim domain"}, "reclaimDomain": {"message": "Reclaim domain"}, "claimDomainNameInputHint": {"message": "Example: mydomain.com. Subdomains require separate entries to be claimed."}, "automaticClaimedDomains": {"message": "Automatic Claimed Domains"}, "automaticDomainClaimProcess": {"message": "Bitwarden will attempt to claim the domain 3 times during the first 72 hours. If the domain can’t be claimed, check the DNS record in your host and manually claim. The domain will be removed from your organization in 7 days if it is not claimed."}, "domainNotClaimed": {"message": "$DOMAIN$ not claimed. Check your DNS records.", "placeholders": {"DOMAIN": {"content": "$1", "example": "bitwarden.com"}}}, "domainStatusClaimed": {"message": "Claimed"}, "domainStatusUnderVerification": {"message": "Under verification"}, "claimedDomainsDesc": {"message": "Claim a domain to own all member accounts whose email address matches the domain. Members will be able to skip the SSO identifier when logging in. Administrators will also be able to delete member accounts."}, "invalidDomainNameClaimMessage": {"message": "Input is not a valid format. Format: mydomain.com. Subdomains require separate entries to be claimed."}, "domainClaimedEvent": {"message": "$DOMAIN$ claimed", "placeholders": {"DOMAIN": {"content": "$1", "example": "bitwarden.com"}}}, "domainNotClaimedEvent": {"message": "$DOMAIN$ not claimed", "placeholders": {"DOMAIN": {"content": "$1", "example": "bitwarden.com"}}}, "updatedRevokeSponsorshipConfirmationForSentSponsorship": {"message": "If you remove $EMAIL$, the sponsorship for this Family plan cannot be redeemed. Are you sure you want to continue?", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "updatedRevokeSponsorshipConfirmationForAcceptedSponsorship": {"message": "If you remove $EMAIL$, the sponsorship for this Family plan will end and the saved payment method will be charged $40 + applicable tax on $DATE$. You will not be able to redeem a new sponsorship until $DATE$. Are you sure you want to continue?", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "date": {"content": "$2", "example": "12/10/2024"}}}, "domainClaimed": {"message": "Domain claimed"}, "organizationNameMaxLength": {"message": "Organization name cannot exceed 50 characters."}, "rotationCompletedTitle": {"message": "Key rotation successful"}, "rotationCompletedDesc": {"message": "Your master password and encryption keys have been updated. Your other devices have been logged out."}, "trustUserEmergencyAccess": {"message": "Trust and confirm user"}, "trustOrganization": {"message": "Trust organization"}, "trust": {"message": "Trust"}, "doNotTrust": {"message": "Do not trust"}, "organizationNotTrusted": {"message": "Organization is not trusted"}, "emergencyAccessTrustWarning": {"message": "For the security of your account, only confirm if you have granted emergency access to this user and their fingerprint matches what is displayed in their account"}, "orgTrustWarning": {"message": "For the security of your account, only proceed if you are a member of this organization, have account recovery enabled, and the fingerprint displayed below matches the organization's fingerprint."}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "Trust user"}, "sshKeyWrongPassword": {"message": "The password you entered is incorrect."}, "importSshKey": {"message": "Import"}, "confirmSshKeyPassword": {"message": "Confirm password"}, "enterSshKeyPasswordDesc": {"message": "Enter the password for the SSH key."}, "enterSshKeyPassword": {"message": "Enter password"}, "invalidSshKey": {"message": "The SSH key is invalid"}, "sshKeyTypeUnsupported": {"message": "The SSH key type is not supported"}, "importSshKeyFromClipboard": {"message": "Import key from clipboard"}, "sshKeyImported": {"message": "SSH key imported successfully"}, "copySSHPrivateKey": {"message": "Copy private key"}, "openingExtension": {"message": "Opening the Bitwarden browser extension"}, "somethingWentWrong": {"message": "Something went wrong..."}, "openingExtensionError": {"message": "We had trouble opening the Bitwarden browser extension. Click the button to open it now."}, "openExtension": {"message": "Open extension"}, "doNotHaveExtension": {"message": "Don't have the Bitwarden browser extension?"}, "installExtension": {"message": "Install extension"}, "openedExtension": {"message": "Opened the browser extension"}, "openedExtensionViewAtRiskPasswords": {"message": "Successfully opened the Bitwarden browser extension. You can now review your at-risk passwords."}, "openExtensionManuallyPart1": {"message": "We had trouble opening the Bitwarden browser extension. Open the Bitwarden icon", "description": "This will be used as part of a larger sentence, broken up to include the Bitwarden icon. The full sentence will read 'We had trouble opening the Bitwarden browser extension. Open the Bitwarden icon [Bitwarden Icon] from the toolbar.'"}, "openExtensionManuallyPart2": {"message": "from the toolbar.", "description": "This will be used as part of a larger sentence, broken up to include the Bitwarden icon. The full sentence will read 'We had trouble opening the Bitwarden browser extension. Open the Bitwarden icon [Bitwarden Icon] from the toolbar.'"}, "resellerRenewalWarningMsg": {"message": "Your subscription will renew soon. To ensure uninterrupted service, contact $RESELLER$ to confirm your renewal before $RENEWAL_DATE$.", "placeholders": {"reseller": {"content": "$1", "example": "Reseller Name"}, "renewal_date": {"content": "$2", "example": "01/01/2024"}}}, "resellerOpenInvoiceWarningMgs": {"message": "An invoice for your subscription was issued on $ISSUED_DATE$. To ensure uninterrupted service, contact $RESELLER$ to confirm your renewal before $DUE_DATE$.", "placeholders": {"reseller": {"content": "$1", "example": "Reseller Name"}, "issued_date": {"content": "$2", "example": "01/01/2024"}, "due_date": {"content": "$3", "example": "01/15/2024"}}}, "resellerPastDueWarningMsg": {"message": "The invoice for your subscription has not been paid. To ensure uninterrupted service, contact $RESELLER$ to confirm your renewal before $GRACE_PERIOD_END$.", "placeholders": {"reseller": {"content": "$1", "example": "Reseller Name"}, "grace_period_end": {"content": "$2", "example": "02/14/2024"}}}, "restartOrganizationSubscription": {"message": "Organization subscription restarted"}, "restartSubscription": {"message": "Restart your subscription"}, "suspendedManagedOrgMessage": {"message": "Contact $PROVIDER$ for assistance.", "placeholders": {"provider": {"content": "$1", "example": "Acme c"}}}, "accountDeprovisioningNotification": {"message": "Administrators now have the ability to delete member accounts that belong to a claimed domain."}, "deleteManagedUserWarningDesc": {"message": "This action will delete the member account including all items in their vault. This replaces the previous Remove action."}, "deleteManagedUserWarning": {"message": "Delete is a new action!"}, "seatsRemaining": {"message": "You have $REMAINING$ seats remaining out of $TOTAL$ seats assigned to this organization. Contact your provider to manage your subscription.", "placeholders": {"remaining": {"content": "$1", "example": "5"}, "total": {"content": "$2", "example": "10"}}}, "existingOrganization": {"message": "Existing organization"}, "selectOrganizationProviderPortal": {"message": "Select an organization to add to your Provider Portal."}, "noOrganizations": {"message": "There are no organizations to list"}, "yourProviderSubscriptionCredit": {"message": "Your provider subscription will receive a credit for any remaining time in the organization's subscription."}, "doYouWantToAddThisOrg": {"message": "Do you want to add this organization to $PROVIDER$?", "placeholders": {"provider": {"content": "$1", "example": "Cool MSP"}}}, "addedExistingOrganization": {"message": "Added existing organization"}, "assignedExceedsAvailable": {"message": "Assigned seats exceed available seats."}, "userkeyRotationDisclaimerEmergencyAccessText": {"message": "Fingerprint phrase for $NUM_USERS$ contacts for which you have enabled emergency access.", "placeholders": {"num_users": {"content": "$1", "example": "5"}}}, "userkeyRotationDisclaimerAccountRecoveryOrgsText": {"message": "Fingerprint phrase for the organization $ORG_NAME$ for which you have enabled account recovery.", "placeholders": {"org_name": {"content": "$1", "example": "My org"}}}, "userkeyRotationDisclaimerDescription": {"message": "Rotating your encryption keys will require you to trust keys of any organizations that can recover your account, and any contacts that you have enabled emergency access for. To continue, make sure you can verify the following:"}, "userkeyRotationDisclaimerTitle": {"message": "Untrusted encryption keys"}, "changeAtRiskPassword": {"message": "Change at-risk password"}, "removeUnlockWithPinPolicyTitle": {"message": "Remove Unlock with PIN"}, "removeUnlockWithPinPolicyDesc": {"message": "Do not allow members to unlock their account with a PIN."}, "upgradeForFullEventsMessage": {"message": "Event logs are not stored for your organization. Upgrade to a Teams or Enterprise plan to get full access to organization event logs."}, "upgradeEventLogTitleMessage": {"message": "Upgrade to see event logs from your organization."}, "upgradeEventLogMessage": {"message": "These events are examples only and do not reflect real events within your Bitwarden organization."}, "cannotCreateCollection": {"message": "Free organizations may have up to 2 collections. Upgrade to a paid plan to add more collections."}, "businessUnit": {"message": "Business Unit"}, "businessUnits": {"message": "Business Units"}, "newBusinessUnit": {"message": "New business unit"}, "sendsTitleNoItems": {"message": "Send sensitive information safely", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsBodyNoItems": {"message": "Share files and data securely with anyone, on any platform. Your information will remain end-to-end encrypted while limiting exposure.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "generatorNudgeTitle": {"message": "Quickly create passwords"}, "generatorNudgeBodyOne": {"message": "Easily create strong and unique passwords by clicking on", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyTwo": {"message": "to help you keep your logins secure.", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyAria": {"message": "Easily create strong and unique passwords by clicking on the Generate password button to help you keep your logins secure.", "description": "Aria label for the body content of the generator nudge"}, "newLoginNudgeTitle": {"message": "Save time with autofill"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Website", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "so this login appears as an autofill suggestion.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Seamless online checkout"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Simplify creating accounts"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "Keep your sensitive data safe"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "restart": {"message": "<PERSON><PERSON>"}, "verifyProviderBankAccountWithStatementDescriptorWarning": {"message": "Payment with a bank account is only available to customers in the United States. You will be required to verify your bank account. We will make a micro-deposit within the next 1-2 business days. Enter the statement descriptor code from this deposit on the provider's subscription page to verify the bank account. Failure to verify the bank account will result in a missed payment and your subscription being suspended."}, "clickPayWithPayPal": {"message": "Please click the Pay with PayPal button to add your payment method."}, "revokeActiveSponsorshipConfirmation": {"message": "If you remove $EMAIL$, the sponsorship for this Family plan will end. A seat within your organization will become available for members or sponsorships after the sponsored organization renewal date on $DATE$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "date": {"content": "$2", "example": "12/31/2024"}}}, "billingAddressRequiredToAddCredit": {"message": "Billing address required to add credit.", "description": "Error message shown when trying to add credit to a trialing organization without a billing address."}}