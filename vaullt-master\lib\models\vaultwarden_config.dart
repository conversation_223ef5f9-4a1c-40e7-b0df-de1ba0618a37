/// Configuration model for Vaultwarden settings
class VaultwardenConfig {
  final bool signupsAllowed;
  final String dataPath;
  final int port;
  final String domain;
  final bool isRunning;
  final bool localhostOnly;

  const VaultwardenConfig({
    required this.signupsAllowed,
    required this.dataPath,
    required this.port,
    this.domain = '',
    this.isRunning = false,
    this.localhostOnly = true,
  });

  VaultwardenConfig copyWith({
    bool? signupsAllowed,
    String? dataPath,
    int? port,
    String? domain,
    bool? isRunning,
    bool? localhostOnly,
  }) {
    return VaultwardenConfig(
      signupsAllowed: signupsAllowed ?? this.signupsAllowed,
      dataPath: dataPath ?? this.dataPath,
      port: port ?? this.port,
      domain: domain ?? this.domain,
      isRunning: isRunning ?? this.isRunning,
      localhostOnly: localhostOnly ?? this.localhostOnly,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'signupsAllowed': signupsAllowed,
      'dataPath': dataPath,
      'port': port,
      'domain': domain,
      'isRunning': isRunning,
      'localhostOnly': localhostOnly,
    };
  }

  factory VaultwardenConfig.fromMap(Map<String, dynamic> map) {
    return VaultwardenConfig(
      signupsAllowed: map['signupsAllowed'] ?? false,
      dataPath: map['dataPath'] ?? '',
      port: map['port'] ?? 11001,
      domain: map['domain'] ?? '',
      isRunning: map['isRunning'] ?? false,
      localhostOnly: map['localhostOnly'] ?? true,
    );
  }

  @override
  String toString() {
    return 'VaultwardenConfig(signupsAllowed: $signupsAllowed, dataPath: $dataPath, port: $port, domain: $domain, isRunning: $isRunning, localhostOnly: $localhostOnly)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is VaultwardenConfig &&
        other.signupsAllowed == signupsAllowed &&
        other.dataPath == dataPath &&
        other.port == port &&
        other.domain == domain &&
        other.isRunning == isRunning &&
        other.localhostOnly == localhostOnly;
  }

  @override
  int get hashCode {
    return signupsAllowed.hashCode ^
        dataPath.hashCode ^
        port.hashCode ^
        domain.hashCode ^
        isRunning.hashCode ^
        localhostOnly.hashCode;
  }
}
