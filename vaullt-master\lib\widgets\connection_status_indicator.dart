import 'package:flutter/material.dart';
import '../providers/vaultwarden_provider.dart';

class ConnectionStatusIndicator extends StatelessWidget {
  final VaultwardenProvider provider;

  const ConnectionStatusIndicator({
    super.key,
    required this.provider,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getStatusColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getStatusColor(),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: _getStatusColor(),
            ),
          ),
          const SizedBox(width: 6),
          Text(
            _getStatusText(),
            style: TextStyle(
              color: _getStatusColor(),
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    if (provider.isLoading) return Colors.orange;
    return provider.isRunning ? Colors.green : Colors.red;
  }

  String _getStatusText() {
    if (provider.isLoading) return 'Loading...';
    return provider.isRunning ? 'Online' : 'Offline';
  }
}
