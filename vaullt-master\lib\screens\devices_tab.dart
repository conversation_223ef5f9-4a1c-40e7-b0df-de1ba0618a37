import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/vaultwarden_provider.dart';
import '../widgets/device_linking_section.dart';

/// Tab for device management and linking functionality
class DevicesTab extends StatelessWidget {
  const DevicesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Device Management'),
        automaticallyImplyLeading: false,
      ),
      body: Consumer<VaultwardenProvider>(
        builder: (context, provider, child) {
          if (!provider.isInitialized) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Initializing services...'),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Text(
                  'Connect Mobile Devices',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Share your Vaultwarden instance with mobile devices on your network.',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 24),

                // Device Linking Section
                const DeviceLinkingSection(),
                const SizedBox(height: 24),

                // Instructions Card
                _buildInstructionsCard(context),
                const SizedBox(height: 24),

                // Network Information Card
                _buildNetworkInfoCard(context, provider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInstructionsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'How to Connect',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInstructionStep(
              context,
              '1',
              'Start Vaultwarden',
              'Make sure your Vaultwarden container is running.',
            ),
            const SizedBox(height: 12),
            _buildInstructionStep(
              context,
              '2',
              'Generate QR Code',
              'Click "Generate QR Code" to create connection credentials.',
            ),
            const SizedBox(height: 12),
            _buildInstructionStep(
              context,
              '3',
              'Scan with Mobile App',
              'Use the VaAulLT mobile app to scan the QR code.',
            ),
            const SizedBox(height: 12),
            _buildInstructionStep(
              context,
              '4',
              'Auto-Connect',
              'The mobile app will automatically discover and connect to your service.',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionStep(
    BuildContext context,
    String number,
    String title,
    String description,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              number,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNetworkInfoCard(BuildContext context, VaultwardenProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.network_check,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Network Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              context,
              'Service Status',
              provider.isRunning ? 'Running' : 'Stopped',
              provider.isRunning ? Colors.green : Colors.red,
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              context,
              'Port',
              provider.config.port.toString(),
              null,
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              context,
              'Network Access',
              provider.config.localhostOnly ? 'Localhost Only' : 'LAN Access',
              provider.config.localhostOnly ? Colors.orange : Colors.green,
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              context,
              'mDNS Discovery',
              provider.isMdnsAdvertising ? 'Active' : 'Inactive',
              provider.isMdnsAdvertising ? Colors.green : Colors.grey,
            ),
            if (provider.localDomainName != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow(
                context,
                'Local Domain',
                provider.localDomainName!,
                null,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    Color? valueColor,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: valueColor ?? Theme.of(context).textTheme.bodyMedium?.color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
