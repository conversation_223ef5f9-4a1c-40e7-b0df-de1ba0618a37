import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:pointycastle/export.dart';

/// Service for SSH key generation and management
class SshService {
  static const String _keyFileName = 'vaullt_ssh_key';
  static const String _pubKeyFileName = 'vaullt_ssh_key.pub';
  
  String? _privateKeyPath;
  String? _publicKeyPath;
  String? _publicKeyContent;
  
  // Getters
  String? get publicKeyContent => _publicKeyContent;
  String? get privateKeyPath => _privateKeyPath;
  String? get publicKeyPath => _publicKeyPath;
  
  /// Initialize the SSH service and load or generate keys
  Future<void> initialize() async {
    try {
      final appDir = await _getAppDirectory();
      _privateKeyPath = path.join(appDir, _keyFileName);
      _publicKeyPath = path.join(appDir, _pubKeyFileName);
      
      // Check if keys already exist
      if (await _keysExist()) {
        await _loadPublicKey();
        debugPrint('SSH keys loaded from existing files');
      } else {
        await _generateKeyPair();
        debugPrint('New SSH key pair generated');
      }
    } catch (e) {
      debugPrint('Error initializing SSH service: $e');
      rethrow;
    }
  }
  
  /// Generate a new SSH key pair
  Future<void> generateNewKeyPair() async {
    try {
      // Delete existing keys if they exist
      await _deleteExistingKeys();
      
      // Generate new key pair
      await _generateKeyPair();
      debugPrint('New SSH key pair generated and saved');
    } catch (e) {
      debugPrint('Error generating new SSH key pair: $e');
      rethrow;
    }
  }
  
  /// Check if SSH keys exist
  Future<bool> _keysExist() async {
    final privateKeyFile = File(_privateKeyPath!);
    final publicKeyFile = File(_publicKeyPath!);
    
    return await privateKeyFile.exists() && await publicKeyFile.exists();
  }
  
  /// Load the public key content from file
  Future<void> _loadPublicKey() async {
    try {
      final publicKeyFile = File(_publicKeyPath!);
      _publicKeyContent = await publicKeyFile.readAsString();
    } catch (e) {
      debugPrint('Error loading public key: $e');
      rethrow;
    }
  }
  
  /// Generate a new RSA key pair
  Future<void> _generateKeyPair() async {
    try {
      // Generate RSA key pair
      final keyGen = RSAKeyGenerator();
      final secureRandom = FortunaRandom();
      
      // Seed the random number generator
      final seedSource = Uint8List(32);
      for (int i = 0; i < 32; i++) {
        seedSource[i] = DateTime.now().millisecondsSinceEpoch & 0xFF;
      }
      secureRandom.seed(KeyParameter(seedSource));
      
      final params = RSAKeyGeneratorParameters(
        BigInt.parse('65537'), // Standard public exponent
        2048, // Key size in bits
        64, // Certainty for prime generation
      );
      
      keyGen.init(ParametersWithRandom(params, secureRandom));
      final keyPair = keyGen.generateKeyPair();
      
      final privateKey = keyPair.privateKey as RSAPrivateKey;
      final publicKey = keyPair.publicKey as RSAPublicKey;
      
      // Convert to PEM format
      final privateKeyPem = _encodePrivateKeyToPem(privateKey);
      final publicKeyOpenSSH = _encodePublicKeyToOpenSSH(publicKey);
      
      // Save keys to files
      await _saveKeyToFile(_privateKeyPath!, privateKeyPem);
      await _saveKeyToFile(_publicKeyPath!, publicKeyOpenSSH);
      
      _publicKeyContent = publicKeyOpenSSH;
      
    } catch (e) {
      debugPrint('Error generating RSA key pair: $e');
      rethrow;
    }
  }
  
  /// Encode private key to PEM format (simplified version)
  String _encodePrivateKeyToPem(RSAPrivateKey privateKey) {
    // For now, we'll create a simple representation
    // In a production environment, you'd want proper ASN.1 encoding
    final keyData = {
      'n': privateKey.modulus.toString(),
      'e': privateKey.exponent.toString(),
      'd': privateKey.privateExponent.toString(),
      'p': privateKey.p.toString(),
      'q': privateKey.q.toString(),
    };

    final jsonString = json.encode(keyData);
    final base64String = base64.encode(utf8.encode(jsonString));

    return '-----BEGIN PRIVATE KEY-----\n${_formatBase64(base64String)}\n-----END PRIVATE KEY-----\n';
  }
  
  /// Encode public key to OpenSSH format
  String _encodePublicKeyToOpenSSH(RSAPublicKey publicKey) {
    final keyType = 'ssh-rsa';
    final keyTypeBytes = utf8.encode(keyType);
    
    // Encode the public key components
    final eBytes = _encodeMpint(publicKey.exponent!);
    final nBytes = _encodeMpint(publicKey.modulus!);
    
    // Build the key data
    final keyData = <int>[];
    keyData.addAll(_encodeString(keyTypeBytes));
    keyData.addAll(eBytes);
    keyData.addAll(nBytes);
    
    final base64Key = base64.encode(keyData);
    return '$keyType $base64Key vaullt@desktop\n';
  }
  

  
  /// Encode a big integer as mpint for SSH format
  List<int> _encodeMpint(BigInt value) {
    final bytes = _bigIntToBytes(value);
    return _encodeString(bytes);
  }
  
  /// Encode a string/bytes with length prefix for SSH format
  List<int> _encodeString(List<int> data) {
    final length = data.length;
    return [
      (length >> 24) & 0xFF,
      (length >> 16) & 0xFF,
      (length >> 8) & 0xFF,
      length & 0xFF,
      ...data,
    ];
  }
  
  /// Convert BigInt to bytes
  List<int> _bigIntToBytes(BigInt value) {
    final bytes = <int>[];
    var temp = value;
    
    if (temp == BigInt.zero) {
      return [0];
    }
    
    while (temp > BigInt.zero) {
      bytes.insert(0, (temp & BigInt.from(0xFF)).toInt());
      temp = temp >> 8;
    }
    
    // Add leading zero if the first bit is set (to ensure positive interpretation)
    if (bytes.isNotEmpty && bytes[0] & 0x80 != 0) {
      bytes.insert(0, 0);
    }
    
    return bytes;
  }
  
  /// Format base64 string with line breaks
  String _formatBase64(String base64String) {
    const lineLength = 64;
    final buffer = StringBuffer();
    
    for (int i = 0; i < base64String.length; i += lineLength) {
      final end = (i + lineLength < base64String.length) ? i + lineLength : base64String.length;
      buffer.writeln(base64String.substring(i, end));
    }
    
    return buffer.toString().trim();
  }
  
  /// Save key content to file
  Future<void> _saveKeyToFile(String filePath, String content) async {
    final file = File(filePath);
    await file.parent.create(recursive: true);
    await file.writeAsString(content);
    
    // Set appropriate permissions on Unix-like systems
    if (!Platform.isWindows) {
      await Process.run('chmod', ['600', filePath]);
    }
  }
  
  /// Delete existing key files
  Future<void> _deleteExistingKeys() async {
    try {
      final privateKeyFile = File(_privateKeyPath!);
      final publicKeyFile = File(_publicKeyPath!);
      
      if (await privateKeyFile.exists()) {
        await privateKeyFile.delete();
      }
      
      if (await publicKeyFile.exists()) {
        await publicKeyFile.delete();
      }
    } catch (e) {
      debugPrint('Error deleting existing keys: $e');
    }
  }
  
  /// Get application directory for storing keys
  Future<String> _getAppDirectory() async {
    if (Platform.isWindows) {
      final appData = Platform.environment['APPDATA'];
      if (appData != null) {
        return path.join(appData, 'VaAulLT');
      }
    } else if (Platform.isLinux || Platform.isMacOS) {
      final home = Platform.environment['HOME'];
      if (home != null) {
        return path.join(home, '.vaullt');
      }
    }
    
    // Fallback to current directory
    return path.join(Directory.current.path, '.vaullt');
  }
}
