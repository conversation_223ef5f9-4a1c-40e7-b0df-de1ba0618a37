^C:\USERS\<USER>\DOCUMENTS\VAAULLT\VAULLT-MASTER\BUILD\WINDOWS\X64\CMAKEFILES\1B7FC38FF7BCDF2E44CA46618854DA98\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\Users\<USER>\Documents\tools\flutter PROJECT_DIR=C:\Users\<USER>\Documents\VaAulLT\vaullt-master FLUTTER_ROOT=C:\Users\<USER>\Documents\tools\flutter FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\Documents\VaAulLT\vaullt-master\windows\flutter\ephemeral PROJECT_DIR=C:\Users\<USER>\Documents\VaAulLT\vaullt-master FLUTTER_TARGET=C:\Users\<USER>\Documents\VaAulLT\vaullt-master\lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzUuMg==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049MDVkYjk2ODkwOA==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049YThiZmRmYzM5NA==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My45LjA= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=C:\Users\<USER>\Documents\VaAulLT\vaullt-master\.dart_tool\package_config.json C:/Users/<USER>/Documents/tools/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOCUMENTS\VAAULLT\VAULLT-MASTER\BUILD\WINDOWS\X64\CMAKEFILES\A0CC40DFC0517522E069D0170AB8CAFC\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOCUMENTS\VAAULLT\VAULLT-MASTER\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/VaAulLT/vaullt-master/windows -BC:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64 --check-stamp-file C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
