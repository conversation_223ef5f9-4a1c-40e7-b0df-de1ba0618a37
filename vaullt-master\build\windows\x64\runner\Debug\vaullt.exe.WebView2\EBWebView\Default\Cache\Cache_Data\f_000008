{"allApplications": {"message": "Toutes les applications"}, "appLogoLabel": {"message": "Logo de Bitwarden"}, "criticalApplications": {"message": "Applications critiques"}, "noCriticalAppsAtRisk": {"message": "Aucune application critique à risques"}, "accessIntelligence": {"message": "Accéder à Intelligence"}, "riskInsights": {"message": "Aperçus des Risques"}, "passwordRisk": {"message": "Risque du mot de passe"}, "reviewAtRiskPasswords": {"message": "Examinez les mots de passe à risque (faibles, exposés ou réutilisés) à travers les applications. Sélectionnez vos applications les plus critiques pour prioriser les actions de sécurité pour que vos utilisateurs s'occupent des mots de passe à risque."}, "dataLastUpdated": {"message": "Dernière mise à jour des données : $DATE$", "placeholders": {"date": {"content": "$1", "example": "2021-01-01"}}}, "notifiedMembers": {"message": "Membres notifiés"}, "revokeMembers": {"message": "Révoquer des membres"}, "restoreMembers": {"message": "Restaurer des membres"}, "cannotRestoreAccessError": {"message": "Impossible de restaurer l'accès à l'organisation"}, "allApplicationsWithCount": {"message": "Toutes les applications ($COUNT$)", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "createNewLoginItem": {"message": "Créer un nouvel élément de connexion"}, "criticalApplicationsWithCount": {"message": "Applications critiques ($COUNT$)", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "notifiedMembersWithCount": {"message": "Membres notifiés ($COUNT$)", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "noAppsInOrgTitle": {"message": "Aucune application trouvée dans $ORG NAME$", "placeholders": {"org name": {"content": "$1", "example": "Company Name"}}}, "noAppsInOrgDescription": {"message": "Au fur et à mesure que les utilisateurs enregistrent des identifiants, les applications apparaissent ici, montrant les mots de passe à risque. Marquez les applications critiques et avertissez les utilisateurs de mettre à jour les mots de passe."}, "noCriticalAppsTitle": {"message": "Vous n'avez marqué aucune application comme Critique"}, "noCriticalAppsDescription": {"message": "Sélectionnez vos applications les plus critiques pour découvrir les mots de passe à risque et avertissez les utilisateurs de modifier ces mots de passe."}, "markCriticalApps": {"message": "Marquer les applications critiques"}, "markAppAsCritical": {"message": "Marquer l'application comme critique"}, "applicationsMarkedAsCriticalSuccess": {"message": "Applications marquées comme critiques"}, "application": {"message": "Application"}, "atRiskPasswords": {"message": "Mots de passes à risque"}, "requestPasswordChange": {"message": "Demander un changement de mot de passe"}, "totalPasswords": {"message": "Total des mots de passe"}, "searchApps": {"message": "Rechercher des applications"}, "atRiskMembers": {"message": "Membres à risque"}, "atRiskMembersWithCount": {"message": "Membres à risque ($COUNT$)", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "atRiskApplicationsWithCount": {"message": "Applications à risque ($COUNT$)", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "atRiskMembersDescription": {"message": "Ces membres se connectent à des applications avec des mots de passe faibles, exposés ou réutilisés."}, "atRiskMembersDescriptionNone": {"message": "Aucun membre ne se connecte dans des applications avec des mots de passe faibles, exposés ou réutilisés."}, "atRiskApplicationsDescription": {"message": "Ces applications ont des mots de passe faibles, exposés ou réutilisés."}, "atRiskApplicationsDescriptionNone": {"message": "Il n'y a aucune application avec des mots de passe faibles, exposés ou réutilisés."}, "atRiskMembersDescriptionWithApp": {"message": "Ces membres se connectent à $APPNAME$ avec des mots de passe faibles, exposés ou réutilisés.", "placeholders": {"appname": {"content": "$1", "example": "Salesforce"}}}, "atRiskMembersDescriptionWithAppNone": {"message": "Il n'y a pas de membre à risque pour $APPNAME$.", "placeholders": {"appname": {"content": "$1", "example": "Salesforce"}}}, "totalMembers": {"message": "Total des membres"}, "atRiskApplications": {"message": "Applications à risque"}, "totalApplications": {"message": "Total des applications"}, "unmarkAsCriticalApp": {"message": "Ne plus marquer comme application critique"}, "criticalApplicationSuccessfullyUnmarked": {"message": "Marquage d'application critique retiré avec succès"}, "whatTypeOfItem": {"message": "Quel type d'élément est-ce ?"}, "name": {"message": "Nom"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "Nouvelle URI"}, "username": {"message": "Nom d'utilisateur"}, "password": {"message": "Mot de passe"}, "newPassword": {"message": "Nouveau mot de passe"}, "passphrase": {"message": "Phrase de passe"}, "notes": {"message": "Notes"}, "privateNote": {"message": "Note privée"}, "note": {"message": "Note"}, "customFields": {"message": "<PERSON><PERSON>"}, "cardholderName": {"message": "Nom du titulaire de la carte"}, "loginCredentials": {"message": "Identifiants de connexion"}, "personalDetails": {"message": "Détails personnels"}, "identification": {"message": "Identification"}, "contactInfo": {"message": "Coordonnées"}, "cardDetails": {"message": "Détails de la carte de paiement"}, "cardBrandDetails": {"message": "Détails de la carte $BRAND$", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "itemHistory": {"message": "Historique des éléments"}, "authenticatorKey": {"message": "Clé d'authentification"}, "autofillOptions": {"message": "Options de saisie automatique"}, "websiteUri": {"message": "Site web (URI)"}, "websiteUriCount": {"message": "Site web (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Site web ajouté"}, "addWebsite": {"message": "Ajouter le site web"}, "deleteWebsite": {"message": "Supprimer le site web"}, "defaultLabel": {"message": "Par défaut ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Afficher la détection de correspondance $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Cacher la détection de correspondance $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Remplissage automatique lors du chargement de la page ?"}, "number": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "brand": {"message": "Réseau de paiement"}, "expiration": {"message": "Expiration"}, "securityCode": {"message": "Cryptogramme visuel (CVV)"}, "securityCodeSlashCVV": {"message": "Code de sécurité / CVV"}, "identityName": {"message": "Identité"}, "company": {"message": "Société"}, "ssn": {"message": "Numéro de sécurité sociale"}, "passportNumber": {"message": "<PERSON>um<PERSON><PERSON>"}, "licenseNumber": {"message": "Numéro de permis"}, "email": {"message": "<PERSON><PERSON><PERSON>"}, "phone": {"message": "Téléphone"}, "january": {"message": "<PERSON><PERSON>"}, "february": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "march": {"message": "Mars"}, "april": {"message": "Avril"}, "may": {"message": "<PERSON>"}, "june": {"message": "Juin"}, "july": {"message": "<PERSON><PERSON><PERSON>"}, "august": {"message": "Août"}, "september": {"message": "Septembre"}, "october": {"message": "Octobre"}, "november": {"message": "Novembre"}, "december": {"message": "Décembre"}, "title": {"message": "Titre"}, "mr": {"message": "<PERSON>."}, "mrs": {"message": "Mme"}, "ms": {"message": "<PERSON><PERSON>"}, "mx": {"message": "Mx"}, "dr": {"message": "Dr"}, "cardExpiredTitle": {"message": "Carte expirée"}, "cardExpiredMessage": {"message": "Si vous l'avez renouvelée, mettez à jour les informations de la carte"}, "expirationMonth": {"message": "Mois d'expiration"}, "expirationYear": {"message": "Année d'expiration"}, "authenticatorKeyTotp": {"message": "Clé d'authentification (TOTP)"}, "totpHelperTitle": {"message": "Rendre la vérification en deux étapes transparente"}, "totpHelper": {"message": "Bitwarden peut stocker et remplir des codes de vérification en 2 étapes. Copiez et collez la clé dans ce champ."}, "totpHelperWithCapture": {"message": "Bitwarden peut stocker et remplir des codes de vérification en 2 étapes. Sélectionnez l'icône caméra pour prendre une capture d'écran du code QR de l'authentificateur de ce site Web, ou copiez et collez la clé dans ce champ."}, "learnMoreAboutAuthenticators": {"message": "En savoir plus sur les authentificateurs"}, "folder": {"message": "Dossier"}, "value": {"message": "<PERSON><PERSON>"}, "cfTypeText": {"message": "Texte"}, "cfTypeHidden": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "cfTypeBoolean": {"message": "Booléen"}, "cfTypeCheckbox": {"message": "Case à cocher"}, "cfTypeLinked": {"message": "<PERSON><PERSON>", "description": "This describes a field that is 'linked' (related) to another field."}, "fieldType": {"message": "Type de champ"}, "fieldLabel": {"message": "Étiquette du champ"}, "remove": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unassigned": {"message": "Non attribué"}, "noneFolder": {"message": "Aucun dossier", "description": "This is the folder for uncategorized items"}, "selfOwnershipLabel": {"message": "Vous", "description": "Used as a label to indicate that the user is the owner of an item."}, "addFolder": {"message": "Ajouter un dossier"}, "editFolder": {"message": "Modifier le dossier"}, "editWithName": {"message": "Modifier $ITEM$: $NAME$", "placeholders": {"item": {"content": "$1", "example": "login"}, "name": {"content": "$2", "example": "Social"}}}, "newFolder": {"message": "Nouveau dossier"}, "folderName": {"message": "Nom de dossier"}, "folderHintText": {"message": "C<PERSON>ez un sous-dossier en ajoutant le nom du dossier parent suivi d'un \"/\". Par exemple : Social/Forums"}, "deleteFolderPermanently": {"message": "Êtes-vous sûr de vouloir supprimer définitivement ce dossier ?"}, "baseDomain": {"message": "Domaine de base", "description": "Domain name. Example: website.com"}, "domainName": {"message": "Nom de domaine", "description": "Domain name. Example: website.com"}, "host": {"message": "<PERSON><PERSON><PERSON>", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "Exact"}, "startsWith": {"message": "Commence par"}, "regEx": {"message": "Expression régulière", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "Détection de correspondance", "description": "URI match detection for auto-fill."}, "defaultMatchDetection": {"message": "Détection de correspondance par défaut", "description": "Default URI match detection for auto-fill."}, "never": {"message": "<PERSON><PERSON>"}, "toggleVisibility": {"message": "Afficher/Masquer"}, "toggleCollapse": {"message": "Déplier / replier", "description": "Toggling an expand/collapse state."}, "checkPassword": {"message": "Vérifier si le mot de passe a été exposé."}, "passwordExposed": {"message": "Ce mot de passe a été exposé $VALUE$ fois dans des fuites de données. Vous devriez le changer.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "Ce mot de passe n'a été trouvé dans aucune fuite de données connue. Il semble sécurisé."}, "save": {"message": "Enregistrer"}, "cancel": {"message": "Annuler"}, "canceled": {"message": "<PERSON><PERSON><PERSON>"}, "close": {"message": "<PERSON><PERSON><PERSON>"}, "delete": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "favorite": {"message": "<PERSON><PERSON><PERSON>"}, "unfavorite": {"message": "Retirer des favoris"}, "edit": {"message": "Modifier"}, "searchCollection": {"message": "Rechercher dans la collection"}, "searchFolder": {"message": "Rechercher dans un dossier"}, "searchFavorites": {"message": "Rechercher dans les favoris"}, "searchLogin": {"message": "Rechercher dans les identifiants", "description": "Search Login type"}, "searchCard": {"message": "Rechercher dans les cartes", "description": "Search Card type"}, "searchIdentity": {"message": "Rechercher dans les identités", "description": "Search Identity type"}, "searchSecureNote": {"message": "Rechercher dans les notes sécurisées", "description": "Search Secure Note type"}, "searchVault": {"message": "Rechercher dans le coffre"}, "searchMyVault": {"message": "Rechercher dans mon coffre"}, "searchOrganization": {"message": "Rechercher une organisation"}, "searchMembers": {"message": "Rechercher dans les membres"}, "searchGroups": {"message": "Rechercher des groupes"}, "allItems": {"message": "Tous les éléments"}, "favorites": {"message": "<PERSON><PERSON><PERSON>"}, "types": {"message": "Types"}, "typeLogin": {"message": "Identifiant"}, "typeCard": {"message": "Carte de paiement"}, "typeIdentity": {"message": "Identité"}, "typeSecureNote": {"message": "Note sécurisée"}, "typeSshKey": {"message": "Clé SSH"}, "typeLoginPlural": {"message": "Identifiants"}, "typeCardPlural": {"message": "<PERSON><PERSON>"}, "typeIdentityPlural": {"message": "Identités"}, "typeSecureNotePlural": {"message": "Notes sécurisées"}, "folders": {"message": "Dossiers"}, "collections": {"message": "Collections"}, "firstName": {"message": "Prénom"}, "middleName": {"message": "Deuxième prénom"}, "lastName": {"message": "Nom de famille"}, "fullName": {"message": "Nom et prénom"}, "address": {"message": "<PERSON><PERSON><PERSON>"}, "address1": {"message": "Adresse 1"}, "address2": {"message": "Adresse 2"}, "address3": {"message": "Adresse 3"}, "cityTown": {"message": "Ville"}, "stateProvince": {"message": "État / Région"}, "zipPostalCode": {"message": "Code postal"}, "country": {"message": "Pays"}, "shared": {"message": "Partagé"}, "attachments": {"message": "Pièces jointes"}, "select": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "newItem": {"message": "Nouvel élément"}, "addItem": {"message": "Ajouter un élément"}, "editItem": {"message": "Éditer l'élément"}, "viewItem": {"message": "Afficher l'élément"}, "newItemHeader": {"message": "Nouveau $TYPE$", "placeholders": {"type": {"content": "$1", "example": "login"}}}, "editItemHeader": {"message": "Modifier $TYPE$", "placeholders": {"type": {"content": "$1", "example": "login"}}}, "viewItemType": {"message": "Voir $ITEMTYPE$", "placeholders": {"itemtype": {"content": "$1", "example": "login"}}}, "new": {"message": "Nouveau", "description": "for adding new items"}, "item": {"message": "É<PERSON>ment"}, "itemDetails": {"message": "Détails de l'élément"}, "itemName": {"message": "Nom de l’élément"}, "ex": {"message": "ex.", "description": "Short abbreviation for 'example'."}, "other": {"message": "<PERSON><PERSON>"}, "share": {"message": "Partager"}, "moveToOrganization": {"message": "Déplacer vers l'organisation"}, "valueCopied": {"message": "$VALUE$ copié", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "copySuccessful": {"message": "<PERSON><PERSON>"}, "copyValue": {"message": "<PERSON><PERSON><PERSON> la valeur", "description": "Copy value to clipboard"}, "copyPassword": {"message": "<PERSON><PERSON><PERSON> le mot de passe", "description": "Copy password to clipboard"}, "copyPassphrase": {"message": "Copier la phrase de passe", "description": "Copy passphrase to clipboard"}, "passwordCopied": {"message": "Mot de passe copié"}, "copyUsername": {"message": "<PERSON><PERSON><PERSON> le nom d'utilisateur", "description": "Copy username to clipboard"}, "copyNumber": {"message": "<PERSON><PERSON><PERSON> le numéro", "description": "Copy credit card number"}, "copySecurityCode": {"message": "Copier le code de sécurité", "description": "Copy credit card security code (CVV)"}, "copyUri": {"message": "Copier l'URI", "description": "Copy URI to clipboard"}, "copyCustomField": {"message": "Copier $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Copier le site web"}, "copyNotes": {"message": "<PERSON><PERSON>r les notes"}, "copyAddress": {"message": "Co<PERSON>r l'adresse"}, "copyPhone": {"message": "<PERSON><PERSON><PERSON> le téléphone"}, "copyEmail": {"message": "<PERSON><PERSON><PERSON> le co<PERSON>riel"}, "copyCompany": {"message": "Copier l'entreprise"}, "copySSN": {"message": "<PERSON><PERSON><PERSON> le numéro de Sécurité Sociale"}, "copyPassportNumber": {"message": "<PERSON><PERSON><PERSON> le numéro de passeport"}, "copyLicenseNumber": {"message": "<PERSON><PERSON><PERSON> le numéro de permis de conduire"}, "copyName": {"message": "<PERSON><PERSON><PERSON> le nom"}, "me": {"message": "<PERSON><PERSON>"}, "myVault": {"message": "Mon coffre"}, "allVaults": {"message": "Tous les coffres"}, "vault": {"message": "<PERSON><PERSON><PERSON>"}, "vaults": {"message": "<PERSON><PERSON><PERSON>"}, "vaultItems": {"message": "Éléments du coffre"}, "filter": {"message": "Filtre"}, "deleteSelected": {"message": "Supprimer la sélection"}, "moveSelected": {"message": "Déplacer la sélection"}, "selectAll": {"message": "<PERSON><PERSON>"}, "unselectAll": {"message": "<PERSON><PERSON>"}, "launch": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "newAttachment": {"message": "Ajouter une nouvelle pièce jointe"}, "deletedAttachment": {"message": "Pièce jointe supprimée"}, "deleteAttachmentConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer cette pièce jointe ?"}, "attachmentSaved": {"message": "La pièce jointe a été enregistrée."}, "file": {"message": "<PERSON><PERSON><PERSON>"}, "selectFile": {"message": "Sélectionnez un fichier."}, "maxFileSize": {"message": "La taille maximale du fichier est de 500 Mo."}, "addedItem": {"message": "<PERSON><PERSON><PERSON> a<PERSON>"}, "editedItem": {"message": "Élément modifié"}, "movedItemToOrg": {"message": "$ITEMNAME$ a été déplacé vers $ORGNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "itemsMovedToOrg": {"message": "Éléments déplacés vers $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Élément déplacé vers $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "deleteItem": {"message": "Supprimer l'élément"}, "deleteFolder": {"message": "Supp<PERSON>er le dossier"}, "deleteAttachment": {"message": "Supprimer la pièce jointe"}, "deleteItemConfirmation": {"message": "Êtes-vous sûr de vouloir déplacer cet élément vers la corbeille ?"}, "deletedItem": {"message": "L'élément a été envoyé dans la corbeille"}, "deletedItems": {"message": "Les éléments ont été envoyés dans la corbeille"}, "movedItems": {"message": "Éléments déplacés"}, "overwritePasswordConfirmation": {"message": "Êtes-vous sûr de vouloir écraser le mot de passe actuel ?"}, "editedFolder": {"message": "Do<PERSON>r enregis<PERSON><PERSON>"}, "addedFolder": {"message": "<PERSON><PERSON><PERSON>"}, "deleteFolderConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer ce dossier ?"}, "deletedFolder": {"message": "Dossier supprimé"}, "editInfo": {"message": "Éditer les informations"}, "access": {"message": "Accès"}, "accessLevel": {"message": "Niveau d'accès"}, "accessing": {"message": "Accès en cours"}, "loggedOut": {"message": "Déconnecté"}, "loggedOutDesc": {"message": "Vous avez été déconnecté de votre compte."}, "loginExpired": {"message": "Votre session a expiré."}, "restartRegistration": {"message": "Redémarrer l'inscription"}, "expiredLink": {"message": "<PERSON>n expiré"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Veuillez redémarrer votre inscription ou essayez de vous connecter."}, "youMayAlreadyHaveAnAccount": {"message": "Vous avez peut-être déjà un compte"}, "logOutConfirmation": {"message": "Êtes-vous sûr de vouloir vous déconnecter ?"}, "logOut": {"message": "Se déconnecter"}, "ok": {"message": "Ok"}, "yes": {"message": "O<PERSON>"}, "no": {"message": "Non"}, "location": {"message": "Localisation"}, "loginOrCreateNewAccount": {"message": "Connectez-vous ou créez un nouveau compte pour accéder à votre coffre sécurisé."}, "loginWithDevice": {"message": "Se connecter avec l'appareil"}, "loginWithDeviceEnabledNote": {"message": "La connexion avec l'appareil doit être configurée dans les paramètres de l'application Bitwarden. Besoin d'une autre option ?"}, "needAnotherOptionV1": {"message": "Besoin d'une autre option ?"}, "loginWithMasterPassword": {"message": "Se connecter avec le mot de passe principal"}, "readingPasskeyLoading": {"message": "Lecture de la <PERSON>..."}, "readingPasskeyLoadingInfo": {"message": "Gardez cette fenêtre ouverte et suivez les instructions de votre navigateur."}, "useADifferentLogInMethod": {"message": "Utiliser une méthode de connexion différente"}, "logInWithPasskey": {"message": "Se connecter avec une clé d'accès"}, "useSingleSignOn": {"message": "Utiliser l'authentification unique"}, "welcomeBack": {"message": "Content de vous revoir"}, "invalidPasskeyPleaseTryAgain": {"message": "Passkey invalide. Veuillez réessayer de nouveau."}, "twoFactorForPasskeysNotSupportedOnClientUpdateToLogIn": {"message": "La 2FA pour les Passkey n'est pas pris en charge. Mettez à jour l'application pour vous connecter."}, "loginWithPasskeyInfo": {"message": "Utilisez une clé d'accès générée qui vous connectera automatiquement sans mot de passe. La biométrie, comme la reconnaissance faciale ou les empreintes digitales, ou une autre méthode de sécurité FIDO2 vérifiera votre identité."}, "newPasskey": {"message": "Nouvelle clé d'identification (passkey)"}, "learnMoreAboutPasswordless": {"message": "En savoir plus sur l'identification sans mots de passe"}, "creatingPasskeyLoading": {"message": "Création de la clé d'accès..."}, "creatingPasskeyLoadingInfo": {"message": "Gardez cette fenêtre ouverte et suivez les instructions de votre navigateur."}, "errorCreatingPasskey": {"message": "Erreur de création de la clé d'accès"}, "errorCreatingPasskeyInfo": {"message": "Il y a eu un problème lors de la crétion de votre clé d'accès."}, "passkeySuccessfullyCreated": {"message": "Clé d'identification (passkey) créée avec succès !"}, "customPasskeyNameInfo": {"message": "Nommez votre clé d'accès pour vous aider à l'identifier."}, "useForVaultEncryption": {"message": "Utiliser pour le cryptage du coffre"}, "useForVaultEncryptionInfo": {"message": "Connectez-vous et déverrouillez sur les appareils pris en charge sans votre mot de passe principal. Suivez les instructions de votre navigateur pour finaliser l'installation."}, "useForVaultEncryptionErrorReadingPasskey": {"message": "Erreur lors de la lecture du mot de passe. Réessayez ou décochez cette option."}, "encryptionNotSupported": {"message": "Chiffrement non pris en charge"}, "enablePasskeyEncryption": {"message": "Configurer le chiffrement"}, "usedForEncryption": {"message": "Utilisé pour le chiffrement"}, "loginWithPasskeyEnabled": {"message": "Se connecter avec une clé d'accès activée"}, "passkeySaved": {"message": "$NAME$ a été enregistrée", "placeholders": {"name": {"content": "$1", "example": "Personal yubikey"}}}, "passkeyRemoved": {"message": "Clé d'identification (passkey) retirée"}, "removePasskey": {"message": "Re<PERSON>rer la clé d'identification (passkey)"}, "removePasskeyInfo": {"message": "Si toutes les clés d'accès sont supprimées, vous ne pourrez pas vous connecter à de nouveaux appareils sans votre mot de passe principal."}, "passkeyLimitReachedInfo": {"message": "Limite de clé d'accès atteinte. Supprimez une clé d'accès pour en ajouter une autre."}, "tryAgain": {"message": "Essayez de nouveau"}, "createAccount": {"message": "<PERSON><PERSON>ez un compte"}, "newToBitwarden": {"message": "Nouveau sur Bitwarden?"}, "setAStrongPassword": {"message": "Définir un mot de passe fort"}, "finishCreatingYourAccountBySettingAPassword": {"message": "Terminer la création de votre compte en définissant un mot de passe"}, "newAroundHere": {"message": "Vous êtes nouveau ici ?"}, "startTrial": {"message": "Commencer la Période d'Essai"}, "logIn": {"message": "Se connecter"}, "logInToBitwarden": {"message": "Se connecter à Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "Entrez le code envoyé à votre adresse courriel"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Entrez le code de votre application d'authentification"}, "pressYourYubiKeyToAuthenticate": {"message": "Appuyez sur votre YubiKey pour vous authentifier"}, "authenticationTimeout": {"message": "<PERSON><PERSON><PERSON> d'authentification dépassé"}, "authenticationSessionTimedOut": {"message": "La session d'authentification a expiré. Veuillez redémarrer le processus de connexion."}, "verifyYourIdentity": {"message": "Vérifiez votre identité"}, "weDontRecognizeThisDevice": {"message": "Nous ne reconnaissons pas cet appareil. Entrez le code envoyé à votre courriel pour vérifier votre identité."}, "continueLoggingIn": {"message": "Con<PERSON>uer à vous connecter"}, "whatIsADevice": {"message": "Qu'est-ce qu'un appareil ?"}, "aDeviceIs": {"message": "Un appareil est une installation unique de l'application Bitwarden où vous vous êtes connecté. <PERSON><PERSON><PERSON><PERSON><PERSON>, effacer les données de l'application ou effacer vos cookies peut entraîner l'apparition d'un appareil plusieurs fois."}, "logInInitiated": {"message": "Connexion initiée"}, "logInRequestSent": {"message": "<PERSON><PERSON><PERSON> envoy<PERSON>"}, "submit": {"message": "So<PERSON><PERSON><PERSON>"}, "emailAddressDesc": {"message": "Vous utiliserez votre adresse électronique pour vous connecter."}, "yourName": {"message": "Votre nom"}, "yourNameDesc": {"message": "Comment doit-on vous appeler ?"}, "masterPass": {"message": "Mot de passe principal"}, "masterPassDesc": {"message": "Le mot de passe principal est le mot de passe que vous utilisez pour accéder à votre coffre. Il est très important de ne pas oublier votre mot de passe principal. Il n'existe aucun moyen de récupérer le mot de passe si vous l'oubliez."}, "masterPassImportant": {"message": "Votre mot de passe principal ne peut pas être récupéré si vous l'oubliez !"}, "masterPassHintDesc": {"message": "Un indice de mot de passe principal peut vous aider à vous souvenir de votre mot de passe si vous l'oubliez."}, "reTypeMasterPass": {"message": "<PERSON><PERSON><PERSON><PERSON> le mot de passe principal"}, "masterPassHint": {"message": "Indice du mot de passe principal (facultatif)"}, "newMasterPassHint": {"message": "Indice du nouveau mot de passe principal (facultatif)"}, "masterPassHintLabel": {"message": "Indice du mot de passe principal"}, "masterPassHintText": {"message": "Si vous oubliez votre mot de passe, l'indice de mot de passe peut être envoyé à votre adresse courriel. $CURRENT$/$MAXIMUM$ caractères maximum.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "settings": {"message": "Paramètres"}, "accountEmail": {"message": "<PERSON><PERSON><PERSON> du compte"}, "requestHint": {"message": "<PERSON><PERSON><PERSON> l'indice"}, "requestPasswordHint": {"message": "De<PERSON>er l'indice du mot de passe"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Entrez l'adresse courriel de votre compte et l'indice de votre mot de passe vous sera envoyé"}, "getMasterPasswordHint": {"message": "Obtenir l'indice du mot de passe principal"}, "emailRequired": {"message": "L'adresse électronique est requise."}, "invalidEmail": {"message": "Adresse électronique invalide."}, "masterPasswordRequired": {"message": "Le mot de passe principal est requis."}, "confirmMasterPasswordRequired": {"message": "Une nouvelle saisie du mot de passe principal est nécessaire."}, "masterPasswordMinlength": {"message": "Le mot de passe principal doit comporter au moins $VALUE$ caractères.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "La confirmation du mot de passe principal ne correspond pas."}, "newAccountCreated": {"message": "Votre nouveau compte a été créé ! Vous pouvez maintenant vous authentifier."}, "newAccountCreated2": {"message": "Votre nouveau compte a été créé !"}, "youHaveBeenLoggedIn": {"message": "Vous avez été connecté!"}, "trialAccountCreated": {"message": "Compte c<PERSON>é avec succès."}, "masterPassSent": {"message": "Nous vous avons envoyé un courriel avec votre indice de mot de passe principal."}, "unexpectedError": {"message": "Une erreur inattendue est survenue."}, "expirationDateError": {"message": "<PERSON><PERSON><PERSON><PERSON> sélectionner une date d'expiration qui est dans le futur."}, "emailAddress": {"message": "Adresse électronique"}, "yourVaultIsLockedV2": {"message": "Votre coffre est verrouillé."}, "yourAccountIsLocked": {"message": "Votre compte est verrouillé"}, "uuid": {"message": "UUID"}, "unlock": {"message": "Déverrouiller"}, "loggedInAsEmailOn": {"message": "Connecté en tant que $EMAIL$ sur $HOSTNAME$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "Mot de passe principal invalide"}, "invalidFilePassword": {"message": "Mot de passe de fichier invalide, veuillez utiliser le mot de passe que vous avez entré lorsque vous avez créé le fichier d'exportation."}, "lockNow": {"message": "Verrouiller maintenant"}, "noItemsInList": {"message": "Aucun élément à afficher."}, "noPermissionToViewAllCollectionItems": {"message": "Vous n'avez pas l'autorisation d'afficher tous les éléments de cette collection."}, "youDoNotHavePermissions": {"message": "Vous n'avez pas les autorisations pour cette collection"}, "noCollectionsInList": {"message": "Aucune collection à afficher."}, "noGroupsInList": {"message": "Aucun groupe à afficher."}, "noUsersInList": {"message": "Aucun utilisateur à afficher."}, "noMembersInList": {"message": "Il n'y a pas de membres à répertorier."}, "noEventsInList": {"message": "Aucun événement à afficher."}, "newOrganization": {"message": "Nouvelle organisation"}, "noOrganizationsList": {"message": "Vous ne faites partie d'aucune organisation. Les organisations vous permettent de partager des éléments de façon sécurisée avec d'autres utilisateurs."}, "notificationSentDevice": {"message": "Une notification a été envoyée à votre appareil."}, "notificationSentDevicePart1": {"message": "Déverrouillez Bitwarden sur votre appareil ou sur le "}, "areYouTryingToAccessYourAccount": {"message": "Essayez-vous d'accéder à votre compte ?"}, "accessAttemptBy": {"message": "Tentative d'accès par $EMAIL$", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "confirmAccess": {"message": "Confirmer l'accès"}, "denyAccess": {"message": "Refuser l'accès"}, "notificationSentDeviceAnchor": {"message": "application web"}, "notificationSentDevicePart2": {"message": "Assurez-vous que la phrase d'empreinte correspond à celle ci-dessous avant d'approuver."}, "notificationSentDeviceComplete": {"message": "Déverrouillez Bitwarden sur votre appareil. Assurez-vous que la phrase d'empreinte correspond à celle ci-dessous avant d'approuver."}, "aNotificationWasSentToYourDevice": {"message": "Une notification a été envoyée à votre appareil"}, "versionNumber": {"message": "Version $VERSION_NUMBER$", "placeholders": {"version_number": {"content": "$1", "example": "1.2.3"}}}, "verificationCodeEmailSent": {"message": "Courriel de vérification envoyé à $EMAIL$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Ne plus demander sur cet appareil pendant 30 jours"}, "selectAnotherMethod": {"message": "Sélectionnez une autre méthode", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Utilisez votre code de récupération"}, "insertU2f": {"message": "Insérez votre clé de sécurité dans le port USB de votre ordinateur. Si elle dispose d'un bouton, appuyez dessus."}, "loginUnavailable": {"message": "Identifiant non disponible"}, "noTwoStepProviders": {"message": "Ce compte dispose d'une configuration d'authentification à deux facteurs, cep<PERSON>ant, aucun des fournisseurs d'authentification à deux facteurs configurés n'est pris en charge par ce navigateur web."}, "noTwoStepProviders2": {"message": "Merci d'utiliser un navigateur web compatible (comme Chrome) et/ou d'ajouter des services additionnels d'identification en deux étapes qui sont mieux supportés par les navigateurs web (comme par exemple une application d'authentification)."}, "twoStepOptions": {"message": "Options d'authentification à deux facteurs"}, "selectTwoStepLoginMethod": {"message": "Sélectionnez la méthode d'authentification à deux facteurs"}, "recoveryCodeDesc": {"message": "Vous avez perdu l'accès à tous vos fournisseurs d'authentification à deux facteurs ? Utilisez votre code de récupération pour désactiver tous les fournisseurs d'authentification à deux facteurs de votre compte."}, "recoveryCodeTitle": {"message": "Code de récupération"}, "authenticatorAppTitle": {"message": "Application d'authentification"}, "authenticatorAppDescV2": {"message": "Entrez un code généré par une application d'authentification comme Bitwarden Authenticator.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "Clé de sécurité OTP de Yubico"}, "yubiKeyDesc": {"message": "Utilisez une YubiKey pour accéder à votre compte. Fonctionne avec les YubiKey séries 4, séries 5 et NEO."}, "duoDescV2": {"message": "Entrez un code généré par Duo Security.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Sécurisez votre organisation avec Duo Security à l'aide de l'application Duo Mobile, l'envoi d'un SMS, un appel vocal ou une clé de sécurité U2F.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "u2fDesc": {"message": "Utiliser n'importe quelle clé de sécurité FIDO U2F active pour accéder à votre compte."}, "u2fTitle": {"message": "Clé de sécurité FIDO U2F"}, "webAuthnTitle": {"message": "WebAuthn FIDO2"}, "webAuthnDesc": {"message": "Utilisez n'importe quelle clé de sécurité compatible avec WebAuthn pour accéder à votre compte."}, "webAuthnMigrated": {"message": "(Migré depuis FIDO)"}, "openInNewTab": {"message": "<PERSON><PERSON><PERSON><PERSON>r dans un nouvel onglet"}, "emailTitle": {"message": "<PERSON><PERSON><PERSON>"}, "emailDescV2": {"message": "Entrez le code envoyé à votre adresse courriel."}, "continue": {"message": "<PERSON><PERSON><PERSON>"}, "organization": {"message": "Organisation"}, "organizations": {"message": "Organisations"}, "moveToOrgDesc": {"message": "Choisissez une organisation vers laquelle vous souhaitez déplacer cet élément. Déplacer un élément vers une organisation transfère la propriété de l'élément à cette organisation. Vous ne serez plus le propriétaire direct de cet élément une fois qu'il aura été déplacé."}, "collectionsDesc": {"message": "Modifier les collections avec lesquelles cet élément est partagé. Seuls les utilisateurs de l'organisation avec un accès à ces collections pourront voir cet élément."}, "deleteSelectedItemsDesc": {"message": "$COUNT$ élément(s) va(vont) être envoyé(s) à la corbeille.", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "deleteSelectedCollectionsDesc": {"message": "$COUNT$ collection(s) sera(seront) supprimée(s) définitivement.", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "deleteSelectedConfirmation": {"message": "Êtes-vous sûr(e) de vouloir continuer ?"}, "moveSelectedItemsDesc": {"message": "Choisissez un dossier dans lequel vous souhaitez ajouter le(s) $COUNT$ élément(s) sélectionné(s).", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "verificationCodeTotp": {"message": "Code de vérification (TOTP)"}, "copyVerificationCode": {"message": "Copier le code de vérification"}, "copyUuid": {"message": "Copier l'UUID"}, "errorRefreshingAccessToken": {"message": "Erreur d'Actualisation du Jeton d'Accès"}, "errorRefreshingAccessTokenDesc": {"message": "Aucun jeton de rafraîchissement ou clé d'API trouvé. Veuillez essayer de vous déconnecter et de vous reconnecter."}, "warning": {"message": "Attention"}, "confirmVaultExport": {"message": "Confirmer l'export du coffre"}, "confirmSecretsExport": {"message": "Confirmer l'export des secrets"}, "exportWarningDesc": {"message": "Cet export contient vos données de coffre dans un format non chiffré. Vous ne devriez pas stocker ou envoyer le fichier exporté par des canaux non sécurisés (comme le courriel). Supprimez-le immédiatement dès que vous avez fini de l'utiliser."}, "exportSecretsWarningDesc": {"message": "Cet export contient vos données secrètes dans un format non chiffré. Vous ne devriez pas stocker ou envoyer le fichier exporté par des canaux non sécurisés (comme le courriel). Supprimez-le immédiatement dès que vous avez fini de l'utiliser."}, "encExportKeyWarningDesc": {"message": "Cet export chiffre vos données en utilisant la clé de chiffrement de votre compte. Si jamais vous modifiez la clé de chiffrement de votre compte, vous devriez exporter à nouveau car vous ne pourrez pas déchiffrer ce fichier."}, "encExportAccountWarningDesc": {"message": "Les clés de chiffrement du compte sont spécifiques à chaque utilisateur Bitwarden. Vous ne pouvez donc pas importer d'export chiffré dans un compte différent."}, "export": {"message": "Exporter"}, "exportFrom": {"message": "Exporter à partir de"}, "exportVault": {"message": "Exporter le coffre"}, "exportSecrets": {"message": "Exporter les secrets"}, "fileFormat": {"message": "Format de fichier"}, "fileEncryptedExportWarningDesc": {"message": "L'export de ce fichier sera protégé par un mot de passe et nécessitera le mot de passe du fichier pour être déchiffré."}, "exportPasswordDescription": {"message": "Ce mot de passe sera utilisé pour exporter et importer ce fichier"}, "confirmMasterPassword": {"message": "Confirmer le mot de passe principal"}, "confirmFormat": {"message": "Confirmer le format"}, "filePassword": {"message": "<PERSON>t de Passe du Fichier"}, "confirmFilePassword": {"message": "Confirm<PERSON> le Mot de Passe du Fichier"}, "accountRestrictedOptionDescription": {"message": "Utilisez la clé de chiffrement de votre compte, dérivée du nom d'utilisateur et du mot de passe principal de votre compte, pour chiffrer l'export et restreindre l'import au seul compte Bitwarden actuel."}, "passwordProtectedOptionDescription": {"message": "Créez un mot de passe généré par l'utilisateur pour protéger l'exportation. Utilisez ceci pour créer une exportation qui peut être utilisée dans d'autres comptes."}, "exportTypeHeading": {"message": "Type d'exportation"}, "accountRestricted": {"message": "Restreint à ce compte"}, "passwordProtected": {"message": "Protégé par mot de passe"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "Le \"Mot de Passe\" et le \"Mot de Passe Confirmé\" ne correspondent pas."}, "confirmVaultImport": {"message": "Confirmer l'Importation du Coffre"}, "confirmVaultImportDesc": {"message": "Ce fichier est protégé par un mot de passe. Veuillez saisir le mot de passe du fichier pour importer les données."}, "exportSuccess": {"message": "Les données de votre coffre-fort ont été exportées."}, "passwordGenerator": {"message": "Générateur de mot de passe"}, "minComplexityScore": {"message": "Score de complexité minimum"}, "minNumbers": {"message": "Minimum de chiffres"}, "minSpecial": {"message": "Minimum de caractères spéciaux", "description": "Minimum special characters"}, "ambiguous": {"message": "É<PERSON>ter les caractères ambigus", "description": "deprecated. Use avoidAmbiguous instead."}, "avoidAmbiguous": {"message": "É<PERSON>ter les caractères ambigus", "description": "Label for the avoid ambiguous characters checkbox."}, "length": {"message": "<PERSON><PERSON><PERSON>"}, "passwordMinLength": {"message": "Longueur minimale du mot de passe"}, "uppercase": {"message": "<PERSON><PERSON><PERSON> (A-Z)", "description": "deprecated. Use uppercaseLabel instead."}, "lowercase": {"message": "Minuscule (a-z)", "description": "deprecated. Use lowercaseLabel instead."}, "numbers": {"message": "<PERSON><PERSON><PERSON> (0-9)", "description": "deprecated. Use numbersLabel instead."}, "specialCharacters": {"message": "Caractères spéciaux (!@#$%^&*)"}, "numWords": {"message": "Nombre de mots"}, "wordSeparator": {"message": "Séparateur de mots"}, "capitalize": {"message": "Mettre la première lettre de chaque mot en majuscule", "description": "Make the first letter of a word uppercase."}, "includeNumber": {"message": "Inclure un Chiffre"}, "generatorPolicyInEffect": {"message": "Les exigences de la politique de sécurité Entreprise ont été appliquées aux options de votre générateur.", "description": "Indicates that a policy limits the credential generator screen."}, "passwordHistory": {"message": "Historique des mots de passe"}, "generatorHistory": {"message": "Historique du générateur"}, "clearGeneratorHistoryTitle": {"message": "Effacer l'historique du générateur"}, "cleargGeneratorHistoryDescription": {"message": "Si vous continuez, toutes les entrées seront définitivement supprimées de l'historique du générateur. Êtes-vous sûr de vouloir continuer?"}, "noPasswordsInList": {"message": "Aucun mot de passe à afficher."}, "clearHistory": {"message": "Effacer l'historique"}, "nothingToShow": {"message": "<PERSON><PERSON> afficher"}, "nothingGeneratedRecently": {"message": "Vous n'avez rien généré récemment"}, "clear": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "To clear something out. Example: To clear browser history."}, "accountUpdated": {"message": "Compte mis à jour"}, "changeEmail": {"message": "Changer le courriel"}, "changeEmailTwoFactorWarning": {"message": "En poursuivant, l'adresse électronique de votre compte sera changée. L'adresse électronique utilisée pour l'authentification à deux facteurs ne sera pas changée. Vous pouvez changer cette adresse électronique dans les paramètres d'authentification à deux facteurs."}, "newEmail": {"message": "Nouveau courriel"}, "code": {"message": "Code"}, "changeEmailDesc": {"message": "Nous avons envoyé un code de vérification à $EMAIL$. Veuillez vérifier votre courriel contenant ce code et saisissez-le ci-dessous pour finaliser le changement d'adresse électronique.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "loggedOutWarning": {"message": "<PERSON> poursuivant, vous serez déconnecté de votre session en cours, ce qui vous obligera à vous reconnecter. Les sessions actives sur d'autres appareils peuvent rester actives pendant encore une heure."}, "emailChanged": {"message": "<PERSON><PERSON><PERSON>"}, "logBackIn": {"message": "Veuillez vous reconnecter."}, "currentSession": {"message": "Session en cours"}, "requestPending": {"message": "Demande en attente"}, "logBackInOthersToo": {"message": "Veuillez vous reconnecter. Si vous utilisez d'autres applications Bitwarden, déconnectez-vous et reconnectez-vous également de celles-ci."}, "changeMasterPassword": {"message": "Changer le mot de passe principal"}, "masterPasswordChanged": {"message": "Mot de passe principal enregistré"}, "currentMasterPass": {"message": "Mot de passe principal actuel"}, "newMasterPass": {"message": "Nouveau mot de passe principal"}, "confirmNewMasterPass": {"message": "Confirmer le nouveau mot de passe principal"}, "encKeySettings": {"message": "Paramètres de la clé de chiffrement"}, "kdfAlgorithm": {"message": "Algorithme KDF"}, "kdfIterations": {"message": "Itérations KDF"}, "kdfIterationsDesc": {"message": "Des itérations KDF plus élevées peuvent aider à protéger votre mot de passe principal contre une attaque par force brute. Nous recommandons une valeur de $VALUE$ ou plus.", "placeholders": {"value": {"content": "$1", "example": "100,000"}}}, "kdfIterationsWarning": {"message": "Un réglage trop élevé des itérations, de la mémoire et du parallélisme KDF peut entraîner des performances médiocres lors de la connexion (et du déverrouillage) à Bitwarden sur des appareils plus lents ou plus anciens. Nous vous recommandons d'augmenter la valeur par incréments de $INCREMENT$ et de tester ensuite tous vos appareils.", "placeholders": {"increment": {"content": "$1", "example": "50,000"}}}, "kdfMemory": {"message": "Mémoire KDF (Mo)", "description": "Memory refers to computer memory (RAM). MB is short for megabytes."}, "argon2Warning": {"message": "Un réglage trop élevé des itérations, de la mémoire et du parallélisme KDF peut entraîner des performances médiocres lors de la connexion (et du déverrouillage) à Bitwarden sur des appareils plus lents ou plus anciens. Nous vous recommandons de les modifier individuellement par petits incréments, puis de tester tous vos appareils."}, "kdfParallelism": {"message": "Parallélisme KDF"}, "argon2Desc": {"message": "Des itérations KDF, une mémoire et un parallélisme plus élevés peuvent contribuer à protéger votre mot de passe principal contre l'attaque par force brute d'un assaillant."}, "changeKdf": {"message": "Changer <PERSON>"}, "encKeySettingsChanged": {"message": "Paramètres de la clé de chiffrement modifiés"}, "dangerZone": {"message": "Zone de danger"}, "deauthorizeSessions": {"message": "Révoquer les sessions"}, "deauthorizeSessionsDesc": {"message": "Vous craignez que votre compte soit connecté sur un autre appareil ? Poursuivez ci-dessous pour annuler l'autorisation de tous les ordinateurs ou appareils que vous avez précédemment utilisés. Cette étape de sécurité est recommandée si vous avez utilisé un ordinateur public ou si vous avez accidentellement enregistré votre mot de passe sur un appareil qui n'est pas le vôtre. Cette étape effacera également toutes les sessions d'authentification à deux facteurs précédemment mémorisées."}, "deauthorizeSessionsWarning": {"message": "En poursuivant, vous serez également déconnecté de votre session en cours, ce qui vous obligera à vous reconnecter. Vous serez également invité à vous reconnecter via l'authentification à deux facteurs, si elle est configurée. Les sessions actives sur d'autres appareils peuvent rester actives pendant encore une heure."}, "newDeviceLoginProtection": {"message": "Connexion à un nouvel appareil"}, "turnOffNewDeviceLoginProtection": {"message": "Désactivez la protection de connexion du nouvel appareil"}, "turnOnNewDeviceLoginProtection": {"message": "Activez la protection de connexion du nouvel appareil"}, "turnOffNewDeviceLoginProtectionModalDesc": {"message": "Procédez ci-dessous pour désactiver les courriels de vérification envoyés par Bitwarden lorsque vous vous connectez à partir d'un nouvel appareil."}, "turnOnNewDeviceLoginProtectionModalDesc": {"message": "Procédez ci-dessous pour que Bitwarden vous envoie des courriels de vérification lorsque vous vous connectez à partir d'un nouvel appareil."}, "turnOffNewDeviceLoginProtectionWarning": {"message": "Avec la protection de connexion d'un nouvel appareil désactivée, toute personne ayant votre mot de passe principal peut accéder à votre compte depuis n'importe quel appareil. Pour protéger votre compte sans courriel de vérification, configurez la connexion en deux étapes."}, "accountNewDeviceLoginProtectionSaved": {"message": "Modifications de la protection de connexion de l'appareil enregistrées"}, "sessionsDeauthorized": {"message": "Toutes les sessions ont été révoquées"}, "accountIsOwnedMessage": {"message": "Ce compte appartient à $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "Organization"}}}, "purgeVault": {"message": "<PERSON><PERSON><PERSON><PERSON> le coffre"}, "purgedOrganizationVault": {"message": "Le coffre de l'organisation a été effacé."}, "vaultAccessedByProvider": {"message": "Coffre-fort consulté par le fournisseur."}, "purgeVaultDesc": {"message": "Poursuivez ci-dessous pour supprimer tous les éléments et dossiers de votre coffre. Les éléments qui appartiennent à une organisation dont vous êtes membre ne seront pas supprimés."}, "purgeOrgVaultDesc": {"message": "Poursuivez ci-dessous pour supprimer tous les éléments du coffre de votre organisation."}, "purgeVaultWarning": {"message": "L'effacement votre coffre est définitif. Cette action ne peut pas être annulée."}, "vaultPurged": {"message": "Votre coffre a été effacé."}, "deleteAccount": {"message": "Supprimer le compte"}, "deleteAccountDesc": {"message": "Continuez ci-dessous pour supprimer votre compte et toutes les données associées."}, "deleteAccountWarning": {"message": "La suppression de votre compte est définitive. Cette action ne peut pas être annulée."}, "accountDeleted": {"message": "Compte supprimé"}, "accountDeletedDesc": {"message": "Votre compte a été fermé et toutes les données associées ont été supprimées."}, "deleteOrganizationWarning": {"message": "La suppression de votre organisation est permanente. Elle ne peut pas être annulée."}, "myAccount": {"message": "Mon compte"}, "tools": {"message": "Outils"}, "importData": {"message": "Importer des données"}, "onboardingImportDataDetailsPartOne": {"message": "Si vous n'avez aucune donnée à importer, vous pouvez créer un ", "description": "This will be part of a larger sentence, that will read like this: If you don't have any data to import, you can create a new item instead. (Optional second half: You may need to wait until your administrator confirms your organization membership.)"}, "onboardingImportDataDetailsLink": {"message": "nouvel élément", "description": "This will be part of a larger sentence, that will read like this: If you don't have any data to import, you can create a new item instead. (Optional second half: You may need to wait until your administrator confirms your organization membership.)"}, "onboardingImportDataDetailsLoginLink": {"message": "nouvel identifiant", "description": "This will be part of a larger sentence, that will read like this: If you don't have any data to import, you can create a new login instead. (Optional second half: You may need to wait until your administrator confirms your organization membership.)"}, "onboardingImportDataDetailsPartTwoNoOrgs": {"message": " à la place.", "description": "This will be part of a larger sentence, that will read like this: If you don't have any data to import, you can create a new item instead."}, "onboardingImportDataDetailsPartTwoWithOrgs": {"message": " à la place. Vous devrez peut-être attendre que votre administrateur confirme votre adhésion à l'organisation.", "description": "This will be part of a larger sentence, that will read like this: If you don't have any data to import, you can create a new item instead. You may need to wait until your administrator confirms your organization membership."}, "importError": {"message": "Erreur d'importation"}, "importErrorDesc": {"message": "Il y a eu un problème avec les données que vous avez essayé d'importer. Veuillez résoudre les erreurs listées ci-dessous dans votre fichier source et réessayer."}, "importSuccess": {"message": "Les données ont été importées dans votre coffre avec succès."}, "importSuccessNumberOfItems": {"message": "Un total de $AMOUNT$ élément(s) a été importé.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "dataExportSuccess": {"message": "Données exportées avec succès"}, "importWarning": {"message": "Vous importez des données vers $ORGANIZATION$. Vos données pourraient être partagées avec les membres de cette organisation. Voulez-vous continuer ?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "importFormatError": {"message": "Les données ne sont pas correctement mises en forme. Veuillez vérifier le fichier importé et réessayer."}, "importNothingError": {"message": "Rien à importer."}, "importEncKeyError": {"message": "Erreur lors du déchiffrement du fichier exporté. Votre clé de chiffrement ne correspond pas à la clé de chiffrement utilisée pour exporter les données."}, "destination": {"message": "Destination"}, "learnAboutImportOptions": {"message": "En savoir plus sur vos options d'importation"}, "selectImportFolder": {"message": "Choisir un dossier"}, "selectImportCollection": {"message": "Sélectionnez une collection"}, "importTargetHint": {"message": "Sélectionnez cette option si vous voulez que le contenu du fichier importé soit déplacé vers un(e) $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "Le fichier contient des éléments non assignés."}, "selectFormat": {"message": "Sélectionnez le format du fichier à importer"}, "selectImportFile": {"message": "Sélectionnez le fichier à importer"}, "chooseFile": {"message": "<PERSON><PERSON> un fichier"}, "noFileChosen": {"message": "<PERSON><PERSON><PERSON> fi<PERSON><PERSON> choisi"}, "orCopyPasteFileContents": {"message": "ou copiez/collez le contenu du fichier à importer"}, "instructionsFor": {"message": "Instructions $NAME$", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "options": {"message": "Options"}, "preferences": {"message": "Préférences"}, "preferencesDesc": {"message": "Personnalisez l'expérience de votre coffre web."}, "preferencesUpdated": {"message": "Préférences sa<PERSON>"}, "language": {"message": "<PERSON><PERSON>"}, "languageDesc": {"message": "Changez la langue utilisée par le coffre web."}, "enableFavicon": {"message": "Afficher les icônes des sites web"}, "faviconDesc": {"message": "Affichez une image reconnaissable à côté de chaque identifiant."}, "default": {"message": "<PERSON><PERSON> <PERSON><PERSON>"}, "domainRules": {"message": "<PERSON><PERSON><PERSON> de domaine"}, "domainRulesDesc": {"message": "Si vous avez le même identifiant sur plusieurs domaines de sites web différents, vous pouvez marquer le site web comme \"équivalent\". Les domaines \"globaux\" sont ceux qui ont déjà été créés pour vous par Bitwarden."}, "globalEqDomains": {"message": "Domaines équivalents globaux"}, "customEqDomains": {"message": "Domaines équivalents personnalisés"}, "exclude": {"message": "Exclure"}, "include": {"message": "Inclure"}, "customize": {"message": "Personnaliser"}, "newCustomDomain": {"message": "Nouveau domaine personnalisé"}, "newCustomDomainDesc": {"message": "Entrez une liste de domaines séparés par des virgules. Seuls les domaines « de base » sont autorisés. N’entrez pas de sous-domaines. Par exemple, entrez « google.com » au lieu de « www.google.com ». Vous pouvez également entrer « androidapp://package.name » pour associer une application android avec d’autres domaines de sites web."}, "customDomainX": {"message": "Domaine personnalisé $INDEX$", "placeholders": {"index": {"content": "$1", "example": "2"}}}, "domainsUpdated": {"message": "Nom de domaine mis à jour"}, "twoStepLogin": {"message": "Authentification à deux facteurs"}, "twoStepLoginEnforcement": {"message": "Mise en application de l'authentification à deux facteurs"}, "twoStepLoginDesc": {"message": "Sécurisez votre compte en exigeant une étape supplémentaire lors de la connexion."}, "twoStepLoginTeamsDesc": {"message": "Activez l'authentification à deux facteurs pour votre organisation."}, "twoStepLoginEnterpriseDescStart": {"message": "Imposer les options d'authentification à deux facteurs de Bitwarden pour les membres en utilisant le ", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Enforce Bitwarden Two-step Login options for members by using the Two-step Login Policy.'"}, "twoStepLoginPolicy": {"message": "Politique d'authentification à deux facteurs"}, "twoStepLoginOrganizationDuoDesc": {"message": "Pour imposer l'authentification à deux facteurs via Duo, utilisez les options ci-dessous."}, "twoStepLoginOrganizationSsoDesc": {"message": "Si vous avez configuré SSO ou si vous prévoyez de le faire, l'authentification à deux facteurs peut déjà être imposée via votre fournisseur d'identité."}, "twoStepLoginRecoveryWarning": {"message": "La configuration d'un système d'authentification à deux facteurs peut définitivement vous verrouiller l'accès à votre compte Bitwarden. Un code de récupération vous permet d'accéder à votre compte dans le cas où vous ne pourriez plus utiliser votre fournisseur normal d'authentification à deux facteurs (exemple : vous perdez votre appareil). L'assistance de Bitwarden ne pourra pas vous aider si vous perdez l'accès à votre compte. Nous vous recommandons de noter ou d'imprimer le code de récupération et de le conserver en lieu sûr."}, "restrictedItemTypePolicy": {"message": "Supprimer le type d'élément de la carte"}, "restrictedItemTypePolicyDesc": {"message": "Ne pas autoriser les membres à créer des types d'éléments de carte. Les cartes existantes seront automatiquement supprimées."}, "restrictCardTypeImport": {"message": "Impossible d'importer les types de l'élément de la carte"}, "restrictCardTypeImportDesc": {"message": "Une politique de sécurité définie par 1 organisation ou plus vous empêche d'importer des cartes dans vos coffres."}, "yourSingleUseRecoveryCode": {"message": "Votre code de récupération à usage unique peut être utilisé pour désactiver la connexion en deux étapes si vous perdez l'accès à votre fournisseur de connexion en deux étapes. Bitwarden vous recommande d'écrire le code de récupération et de le conserver dans un endroit sûr."}, "viewRecoveryCode": {"message": "Voir le code de récupération"}, "providers": {"message": "Fournisseurs", "description": "Two-step login providers such as YubiKey, Duo, Authenticator apps, Email, etc."}, "enable": {"message": "Activer"}, "enabled": {"message": "Activé"}, "restoreAccess": {"message": "Restaurer l'Accès"}, "premium": {"message": "Premium", "description": "Premium membership"}, "premiumMembership": {"message": "Adhésion Premium"}, "premiumRequired": {"message": "Premium requis"}, "premiumRequiredDesc": {"message": "Une adhésion Premium est requise pour utiliser cette fonctionnalité."}, "youHavePremiumAccess": {"message": "Vous avez un accès Premium"}, "alreadyPremiumFromOrg": {"message": "Vous avez déjà accès aux fonctionnalités Premium grâce à une organisation dont vous êtes membre."}, "manage": {"message": "<PERSON><PERSON><PERSON>"}, "manageCollection": {"message": "<PERSON><PERSON><PERSON> la collection"}, "viewItems": {"message": "Afficher les éléments"}, "viewItemsHidePass": {"message": "Afficher les éléments, les mots de passe cachés"}, "editItems": {"message": "Modifier les items"}, "editItemsHidePass": {"message": "Modifier les éléments, les mots de passe cachés"}, "disable": {"message": "Désactiver"}, "orgUserDetailsNotFound": {"message": "Informations sur le membre non trouvés."}, "revokeAccess": {"message": "Révoquer l'Accès"}, "revoke": {"message": "Révoquer"}, "twoStepLoginProviderEnabled": {"message": "Ce fournisseur d'authentification à deux facteurs est actif sur votre compte."}, "twoStepLoginAuthDesc": {"message": "Saisissez votre mot de passe principal pour modifier les paramètres d'authentification à deux facteurs."}, "twoStepAuthenticatorInstructionPrefix": {"message": "Télécharger une application d'authentification telle que"}, "twoStepAuthenticatorInstructionInfix1": {"message": ","}, "twoStepAuthenticatorInstructionInfix2": {"message": "ou"}, "twoStepAuthenticatorInstructionSuffix": {"message": "."}, "continueToExternalUrlTitle": {"message": "Continuer vers $URL$?", "placeholders": {"url": {"content": "$1", "example": "bitwarden.com"}}}, "continueToExternalUrlDesc": {"message": "Vous quittez Bitwarden et lancez un site web externe dans une nouvelle fenêtre."}, "twoStepContinueToBitwardenUrlTitle": {"message": "Continuer vers bitwarden.com?"}, "twoStepContinueToBitwardenUrlDesc": {"message": "Bitwarden Authenticator vous permet de stocker les clés d'authentification et de générer des codes TOTP pour les demande d'authentification en 2 étapes. Apprenez-en plus sur le site web bitwarden.com."}, "twoStepAuthenticatorScanCodeV2": {"message": "Scannez le code QR ci-dessous avec votre application d'authentification ou saisissez la clé."}, "twoStepAuthenticatorQRCanvasError": {"message": "Impossible de charger le code QR. R<PERSON>ez ou utilisez la clé ci-dessous."}, "key": {"message": "Clé"}, "twoStepAuthenticatorEnterCodeV2": {"message": "Code de vérification"}, "twoStepAuthenticatorReaddDesc": {"message": "Dans le cas où vous devez l’ajouter à un autre appareil, voici le code QR (ou clé) requis par votre application d'authentification."}, "twoStepDisableDesc": {"message": "Êtes-vous sûr de vouloir dés<PERSON>r ce fournisseur d'authentification à deux facteurs ?"}, "twoStepDisabled": {"message": "Fournisseur d'authentification à deux facteurs désactivé."}, "twoFactorYubikeyAdd": {"message": "Ajouter une nouvelle YubiKey à votre compte"}, "twoFactorYubikeyPlugIn": {"message": "Branchez la YubiKey dans un port USB de votre ordinateur."}, "twoFactorYubikeySelectKey": {"message": "Sélectionnez le premier champ YubiKey libre ci-dessous."}, "twoFactorYubikeyTouchButton": {"message": "Appuyez sur le bouton de la YubiKey."}, "twoFactorYubikeySaveForm": {"message": "Sauvegarder le formulaire."}, "twoFactorYubikeyWarning": {"message": "En raison des limitations de la plateforme, les YubiKey ne peuvent pas être utilisées sur toutes les applications Bitwarden. Vous devriez mettre en place un autre fournisseur d'authentification à deux facteurs pouvoir accéder à votre compte lorsque les YubiKeys ne peuvent pas être utilisées. Plateformes prises en charge :"}, "twoFactorYubikeySupportUsb": {"message": "Co<PERSON>re web, application de bureau, interface ligne de commande (CLI) et toutes les extensions de navigateur sur un appareil doté d'un port USB pouvant accepter votre YubiKey."}, "twoFactorYubikeySupportMobile": {"message": "Applications mobiles sur un appareil avec NFC ou un port USB pouvant accepter votre YubiKey."}, "yubikeyX": {"message": "YubiKey $INDEX$", "placeholders": {"index": {"content": "$1", "example": "2"}}}, "u2fkeyX": {"message": "Clé U2F $INDEX$", "placeholders": {"index": {"content": "$1", "example": "2"}}}, "webAuthnkeyX": {"message": "Clé WebAuthn $INDEX$", "placeholders": {"index": {"content": "$1", "example": "2"}}}, "nfcSupport": {"message": "Support du NFC"}, "twoFactorYubikeySupportsNfc": {"message": "Une de mes clés supporte le NFC."}, "twoFactorYubikeySupportsNfcDesc": {"message": "Si une de vos YubiKeys supporte le NFC (comme la YubiKey NEO), vous serez averti sur les appareils mobiles en cas de disponibilité du NFC."}, "yubikeysUpdated": {"message": "Les YubiKeys ont été mises à jour"}, "disableAllKeys": {"message": "Désactiver toutes les clés"}, "twoFactorDuoDesc": {"message": "Entrez les informations de l'application Bitwarden provenant de votre panneau Duo Admin."}, "twoFactorDuoClientId": {"message": "ID du client"}, "twoFactorDuoClientSecret": {"message": "Secret du client"}, "twoFactorDuoApiHostname": {"message": "Nom d'hôte de l'API"}, "twoFactorEmailDesc": {"message": "Suivre ces étapes pour mettre en place l'authentification à deux facteurs avec courriel :"}, "twoFactorEmailEnterEmail": {"message": "Saisis<PERSON>z le courriel où vous souhaitez recevoir les codes de vérification"}, "twoFactorEmailEnterCode": {"message": "Saisissez le code de vérification à 6 chiffres obtenu dans le courriel"}, "sendEmail": {"message": "Envoy<PERSON> le co<PERSON>riel"}, "twoFactorU2fAdd": {"message": "Ajouter une clé de sécurité FIDO U2F à votre compte"}, "removeU2fConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer cette clé de sécurité ?"}, "twoFactorWebAuthnAdd": {"message": "Ajoutez une clé de sécurité WebAuthn à votre compte"}, "readKey": {"message": "<PERSON>re la clé"}, "keyCompromised": {"message": "La clé est compromise."}, "twoFactorU2fGiveName": {"message": "Donnez un nom convivial à la clé de sécurité pour l'identifier."}, "twoFactorU2fPlugInReadKey": {"message": "Branchez la clé de sécurité sur un port USB de votre ordinateur et cliquez sur le bouton \"Lire la clé\"."}, "twoFactorU2fTouchButton": {"message": "Si la clé de sécurité a un bouton, touchez-le."}, "twoFactorU2fSaveForm": {"message": "Sauvegarder le formulaire."}, "twoFactorU2fWarning": {"message": "En raison des limitations de la plateforme, FIDO U2F ne peut pas être utilisé sur toutes les applications Bitwarden. Vous devriez mettre en place un autre fournisseur d'authentification à deux facteurs pouvoir accéder à votre compte lorsque FIDO U2F ne peut pas être utilisé. Plateformes prises en charge :"}, "twoFactorU2fSupportWeb": {"message": "Coffre web et extensions sur un ordinateur fixe/portable avec un navigateur compatible U2F (Chrome, Opera, Vivaldi ou Firefox avec FIDO U2F activé)."}, "twoFactorU2fWaiting": {"message": "Attente de l'appui sur le bouton de votre clé de sécurité"}, "twoFactorU2fClickSave": {"message": "Utilise<PERSON> le bouton \"Sauvegarde\" ci-dessous pour activer cette clé de sécurité pour l'authentification à deux facteurs."}, "twoFactorU2fProblemReadingTryAgain": {"message": "Un problème est survenu lors de la lecture de la clé de sécurité. Veuillez réessayer."}, "twoFactorWebAuthnWarning1": {"message": "En raison des limitations de plate-forme, WebAuthn ne peut pas être utilisé sur toutes les applications Bitwarden. Vous devriez mettre en place un autre fournisseur d'authentification à deux facteurs afin de pouvoir accéder à votre compte lorsque WebAuthn ne peut pas être utilisé."}, "twoFactorRecoveryYourCode": {"message": "Votre code de récupération de d'authentification à deux facteurs Bitwarden"}, "twoFactorRecoveryNoCode": {"message": "Vous n'avez pas encore mis en place de fournisseurs d'authentification à deux facteurs. Une fois que vous aurez mis en place un fournisseur d'authentification à deux facteurs, vous pourrez revenir ici pour obtenir votre code de récupération."}, "printCode": {"message": "Imprimer le code", "description": "Print 2FA recovery code"}, "reports": {"message": "Rapports"}, "reportsDesc": {"message": "Identifiez et comblez les failles de sécurité de vos comptes en ligne en cliquant sur les rapports ci-dessous.", "description": "Vault health reports can be used to evaluate the security of your Bitwarden individual or organization vault."}, "orgsReportsDesc": {"message": "Identifiez et fermez les trous de sécurité dans les comptes de votre organisation en cliquant sur les rapports ci-dessous.", "description": "Vault health reports can be used to evaluate the security of your Bitwarden individual or organization vault."}, "unsecuredWebsitesReport": {"message": "Sites web non sécurisés"}, "unsecuredWebsitesReportDesc": {"message": "Les URL qui commencent par http:// n'utilisent pas le meilleur chiffrement disponible. Modifiez les URI de connexion de ces comptes en https:// pour une navigation plus sûre."}, "unsecuredWebsitesFound": {"message": "Sites web non sécurisés trouvés"}, "unsecuredWebsitesFoundReportDesc": {"message": "Nous avons $COUNT$ éléments dans $VAULT$ avec des URI non sécurisées. Vous devriez modifier leur schéma URI en https:// si le site le permet.", "placeholders": {"count": {"content": "$1", "example": "8"}, "vault": {"content": "$2", "example": "this will be 'vault' or 'vaults'"}}}, "noUnsecuredWebsites": {"message": "Aucun élément dans votre coffre n'a d'URI non sécurisés."}, "inactive2faReport": {"message": "Authentification à deux facteurs inactive"}, "inactive2faReportDesc": {"message": "L'authentification à deux facteurs ajoute une couche de protection à vos comptes. Mettez en place l'authentification à deux facteurs en utilisant Bitwarden authenticator pour ces comptes ou utilisez une méthode alternative."}, "inactive2faFound": {"message": "Identifiants sans authentification à deux facteurs trouvés"}, "inactive2faFoundReportDesc": {"message": "Nous avons trouvé $COUNT$ site(s) web dans $VAULT$ qui ne sont peut-être pas configurés avec une connexion en deux étapes (selon le 2fa.directory). Pour protéger davantage ces comptes, vous devez configurer une connexion en deux étapes.", "placeholders": {"count": {"content": "$1", "example": "8"}, "vault": {"content": "$2", "example": "this will be 'vault' or 'vaults'"}}}, "noInactive2fa": {"message": "Aucun site web n'a été trouvé dans votre coffre avec une configuration d'authentification à deux facteurs manquante."}, "instructions": {"message": "Instructions"}, "exposedPasswordsReport": {"message": "Mots de passe exposés"}, "exposedPasswordsReportDesc": {"message": "Les mots de passe exposés lors d'une brèche de données sont des cibles faciles pour les attaquants. Changez ces mots de passe pour éviter des intrusions potentielles."}, "exposedPasswordsFound": {"message": "Mots de passe exposés trouvés"}, "exposedPasswordsFoundReportDesc": {"message": "Nous avons trouvé $COUNT$ éléments dans $VAULT$ qui ont des mots de passe qui ont été exposés dans des fuites de données connues. Vous devriez les changer pour utiliser un nouveau mot de passe.", "placeholders": {"count": {"content": "$1", "example": "8"}, "vault": {"content": "$2", "example": "this will be 'vault' or 'vaults'"}}}, "noExposedPasswords": {"message": "Aucun élément de votre coffre n'a de mots de passe qui ont été révélés lors de fuites de données connues."}, "checkExposedPasswords": {"message": "Vérifier les mots de passe exposés"}, "timesExposed": {"message": "Nombre d'expositions"}, "exposedXTimes": {"message": "Exposé $COUNT$ fois", "placeholders": {"count": {"content": "$1", "example": "52"}}}, "weakPasswordsReport": {"message": "Mots de passe faibles"}, "weakPasswordsReportDesc": {"message": "Les mots de passe faibles peuvent être facilement devinés par les assaillants. Remplacez ces mots de passe par des mots de passe robustes à l'aide du générateur de mots de passe."}, "weakPasswordsFound": {"message": "Mots de passe faibles trouvés"}, "weakPasswordsFoundReportDesc": {"message": "Nous avons trouvé $COUNT$ éléments dans $VAULT$ avec des mots de passe faibles. Vous devriez les mettre à jour pour utiliser des mots de passe plus forts.", "placeholders": {"count": {"content": "$1", "example": "8"}, "vault": {"content": "$2", "example": "this will be 'vault' or 'vaults'"}}}, "noWeakPasswords": {"message": "Aucun élément dans votre coffre n'a de mots de passe faibles."}, "weakness": {"message": "Faiblesse"}, "reusedPasswordsReport": {"message": "Mots de passe réutilisés"}, "reusedPasswordsReportDesc": {"message": "La réutilisation des mots de passe permet aux assaillants de s'introduire plus facilement dans plusieurs comptes. Modifiez ces mots de passe afin que chacun soit unique."}, "reusedPasswordsFound": {"message": "Mots de passe réutilisés trouvés"}, "reusedPasswordsFoundReportDesc": {"message": "Nous avons trouvé $COUNT$ mots de passe qui sont réutilisés dans $VAULT$. Vous devriez les changer pour utiliser des mots de passe uniques.", "placeholders": {"count": {"content": "$1", "example": "8"}, "vault": {"content": "$2", "example": "this will be 'vault' or 'vaults'"}}}, "noReusedPasswords": {"message": "Aucun identifiant dans votre coffre n'a de mots de passe qui sont réutilisés."}, "timesReused": {"message": "Nombre de réutilisations"}, "reusedXTimes": {"message": "Réutilisé $COUNT$ fois", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "dataBreachReport": {"message": "Brèches de données"}, "breachDesc": {"message": "Les comptes ayant fait l'objet d'une brèche de données peuvent exposer vos informations personnelles. Sécurisez les comptes ayant fait l'objet d'une brèche de données en activant le 2FA ou en créant un mot de passe plus robuste."}, "breachCheckUsernameEmail": {"message": "Vérifiez tous les noms d'utilisateur ou adresses électroniques que vous utilisez."}, "checkBreaches": {"message": "Vérifier les fuites"}, "breachUsernameNotFound": {"message": "$USERNAME$ n'a été trouvé dans aucune fuite de données.", "placeholders": {"username": {"content": "$1", "example": "<EMAIL>"}}}, "goodNews": {"message": "Bonne nouvelle", "description": "ex. Good News, No Breached Accounts Found!"}, "breachUsernameFound": {"message": "$USERNAME$ a été trouvé dans $COUNT$ fuites de données différentes en ligne.", "placeholders": {"username": {"content": "$1", "example": "<EMAIL>"}, "count": {"content": "$2", "example": "7"}}}, "breachFound": {"message": "Des comptes ayant fuité ont été trouvés"}, "compromisedData": {"message": "Données compromises"}, "website": {"message": "Site web"}, "affectedUsers": {"message": "Utilisateurs concernés"}, "breachOccurred": {"message": "Une fuite a eu lieu"}, "breachReported": {"message": "Fuite signalée"}, "reportError": {"message": "Une erreur est survenue en essayant de charger le rapport. Réessayez"}, "billing": {"message": "Facturation"}, "billingPlanLabel": {"message": "Plan de Facturation"}, "paymentType": {"message": "Type de Paiement"}, "accountCredit": {"message": "<PERSON><PERSON><PERSON> c<PERSON><PERSON>", "description": "Financial term. In the case of Bitwarden, a positive balance means that you owe money, while a negative balance means that you have a credit (Bitwarden owes you money)."}, "accountBalance": {"message": "Solde du compte", "description": "Financial term. In the case of Bitwarden, a positive balance means that you owe money, while a negative balance means that you have a credit (Bitwarden owes you money)."}, "addCredit": {"message": "A<PERSON>ter du crédit", "description": "Add more credit to your account's balance."}, "amount": {"message": "<PERSON><PERSON>", "description": "Dollar amount, or quantity."}, "creditDelayed": {"message": "Le crédit ajouté apparaîtra sur votre compte une fois que le paiement aura été entièrement traité. Certains moyens de paiement sont différés et peuvent prendre plus de temps à traiter que d'autres."}, "makeSureEnoughCredit": {"message": "Veuillez vous assurer que votre compte dispose d'un crédit suffisant pour cet achat. Si le crédit de votre compte n'est pas suffisant, votre mode de paiement par défaut sera utilisé pour régler la différence. Vous pouvez ajouter du crédit à votre compte à partir de la page Facturation."}, "creditAppliedDesc": {"message": "Le crédit de votre compte peut être utilisé pour régler vos achats. Tout crédit disponible sera automatiquement appliqué aux factures générées pour ce compte."}, "goPremium": {"message": "Passez Premium", "description": "Another way of saying \"Get a Premium membership\""}, "premiumUpdated": {"message": "Vous avez mis à niveau vers Premium."}, "premiumUpgradeUnlockFeatures": {"message": "Mettez à niveau votre compte vers une adhésion Premium et débloquez de superbes fonctionnalités supplémentaires."}, "premiumSignUpStorage": {"message": "1 Go de stockage chiffré pour les fichiers joints."}, "premiumSignUpTwoStepOptions": {"message": "Options de connexion propriétaires à deux facteurs telles que Yu<PERSON> et Duo."}, "premiumSignUpEmergency": {"message": "Accès d'urgence"}, "premiumSignUpReports": {"message": "Hygiène du mot de passe, santé du compte et rapports sur les brèches de données pour assurer la sécurité de votre coffre."}, "premiumSignUpTotp": {"message": "Générateur de code de vérification TOTP (2FA) pour les identifiants dans votre coffre."}, "premiumSignUpSupport": {"message": "Assistance client prioritaire."}, "premiumSignUpFuture": {"message": "Toutes les futures fonctionnalités Premium. Plus à venir prochainement !"}, "premiumPrice": {"message": "Tout pour seulement $PRICE$/an !", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceWithFamilyPlan": {"message": "Passez Premium pour seulement $PRICE$/an, ou obtenez des comptes Premium pour $FAMILYPLANUSERCOUNT$ utilisateurs et un partage familial illimité avec un ", "placeholders": {"price": {"content": "$1", "example": "$10"}, "familyplanusercount": {"content": "$2", "example": "6"}}}, "bitwardenFamiliesPlan": {"message": "Plan Bitwarden Familles."}, "addons": {"message": "Add-ons"}, "premiumAccess": {"message": "Accès Premium"}, "premiumAccessDesc": {"message": "Vous pouvez ajouter l'accès Premium à tous les membres de votre organisation pour $PRICE$/$INTERVAL$.", "placeholders": {"price": {"content": "$1", "example": "$3.33"}, "interval": {"content": "$2", "example": "'month' or 'year'"}}}, "additionalStorageGb": {"message": "Stockage additionnel (Go)"}, "additionalStorageGbDesc": {"message": "# Go additionnels"}, "additionalStorageIntervalDesc": {"message": "Votre offre comprend $SIZE$ de stockage de fichiers chiffrés. Vous pouvez ajouter du stockage supplémentaire pour $PRICE$ par Go/$INTERVAL$.", "placeholders": {"size": {"content": "$1", "example": "1 GB"}, "price": {"content": "$2", "example": "$4.00"}, "interval": {"content": "$3", "example": "'month' or 'year'"}}}, "summary": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "total": {"message": "Total"}, "year": {"message": "an"}, "yr": {"message": "an"}, "month": {"message": "mois"}, "monthAbbr": {"message": "mois", "description": "Short abbreviation for 'month'"}, "paymentChargedAnnually": {"message": "Votre mode de paiement sera débité immédiatement, puis de façon récurrente chaque année. Vous pouvez résilier à tout moment."}, "paymentCharged": {"message": "Votre mode de paiement sera facturé immédiatement et de manière récurrente chaque $INTERVAL$. Vous pouvez annuler à tout moment.", "placeholders": {"interval": {"content": "$1", "example": "month or year"}}}, "paymentChargedWithUnpaidSubscription": {"message": "Votre mode de paiement sera facturé pour tous les abonnements impayés."}, "paymentChargedWithTrial": {"message": "Votre offre comprend un essai gratuit de 7 jours. Votre mode de paiement ne sera pas facturé avant la fin de la période d'essai. Vous pouvez annuler à tout moment."}, "paymentInformation": {"message": "Informations de paiement"}, "billingInformation": {"message": "Informations de facturation"}, "billingTrialSubLabel": {"message": "Votre mode de paiement ne sera pas débité pendant la période d'essai gratuite de 7 jours."}, "creditCard": {"message": "Carte de <PERSON>"}, "paypalClickSubmit": {"message": "Sélectionnez le bouton PayPal pour vous connecter à votre compte PayPal, puis cliquez sur le bouton Soumettre ci-dessous pour continuer."}, "cancelSubscription": {"message": "Annuler l'abonnement"}, "subscriptionExpiration": {"message": "Expiration de l'abonnement"}, "subscriptionCanceled": {"message": "L'abonnement a été annulé."}, "pendingCancellation": {"message": "Annulation en attente"}, "subscriptionPendingCanceled": {"message": "L’abonnement a été marqué pour être annulé à la fin de la période de facturation actuelle."}, "reinstateSubscription": {"message": "Rétablir l'abonnement"}, "reinstateConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer la demande d'annulation en attente et rétablir votre abonnement ?"}, "reinstated": {"message": "Votre abonnement a été rétabli."}, "cancelConfirmation": {"message": "Êtes-vous sûr de vouloir annuler ? Vous perdrez l’accès à toutes les fonctionnalités de l’abonnement à la fin de ce cycle de facturation."}, "canceledSubscription": {"message": "Abonnement annulé"}, "neverExpires": {"message": "N'expire jamais"}, "status": {"message": "État"}, "nextCharge": {"message": "Prochain paiement"}, "details": {"message": "Détails"}, "downloadLicense": {"message": "Télécharger la licence"}, "viewBillingToken": {"message": "Voir le Jeton de Facturation"}, "updateLicense": {"message": "Mettre à jour la licence"}, "manageSubscription": {"message": "<PERSON><PERSON>rer l'abonnement"}, "launchCloudSubscription": {"message": "Démarrer l'Abonnement Cloud"}, "storage": {"message": "Stockage"}, "addStorage": {"message": "Ajouter du stockage"}, "removeStorage": {"message": "Retirer du stockage"}, "subscriptionStorage": {"message": "Votre abonnement possède un total de $MAX_STORAGE$ Go de stockage de fichiers chiffrés. Vous utilisez actuellement $USED_STORAGE$.", "placeholders": {"max_storage": {"content": "$1", "example": "4"}, "used_storage": {"content": "$2", "example": "65 MB"}}}, "paymentMethod": {"message": "Moyen de paiement"}, "noPaymentMethod": {"message": "Aucun moyen de paiement enregistré."}, "addPaymentMethod": {"message": "Ajouter un moyen de paiement"}, "changePaymentMethod": {"message": "Changer de moyen de paiement"}, "invoices": {"message": "Factures"}, "noUnpaidInvoices": {"message": "Aucune facture impayée."}, "noPaidInvoices": {"message": "Aucune facture payée."}, "paid": {"message": "Payée", "description": "Past tense status of an invoice. ex. Paid or unpaid."}, "unpaid": {"message": "Due", "description": "Past tense status of an invoice. ex. Paid or unpaid."}, "transactions": {"message": "Transactions", "description": "Payment/credit transactions."}, "noTransactions": {"message": "Aucune transaction."}, "chargeNoun": {"message": "Débit", "description": "Noun. A charge from a payment method."}, "refundNoun": {"message": "Remboursement", "description": "Noun. A refunded payment that was charged."}, "chargesStatement": {"message": "Tous les frais apparaîtront sur votre relevé en tant que $STATEMENT_NAME$.", "placeholders": {"statement_name": {"content": "$1", "example": "BITWARDEN"}}}, "gbStorageAdd": {"message": "Go de stockage à ajouter"}, "gbStorageRemove": {"message": "Go de stockage à supprimer"}, "storageAddNote": {"message": "L'ajout d'espace de stockage entraînera des ajustements sur vos totaux de facturation et facturera immédiatement le moyen de paiement enregistré. La première facturation sera calculée au prorata du reste du cycle de facturation en cours."}, "storageRemoveNote": {"message": "La suppression du stockage entraînera des ajustements de vos totaux de facturation qui seront calculés au prorata sous forme de crédits pour votre prochaine facturation."}, "adjustedStorage": {"message": "$AMOUNT$ Go de stockage mis à jour.", "placeholders": {"amount": {"content": "$1", "example": "5"}}}, "contactSupport": {"message": "Contacter le support client"}, "contactSupportShort": {"message": "<PERSON><PERSON> le <PERSON>"}, "updatedPaymentMethod": {"message": "Moyen de paiement mise à jour."}, "purchasePremium": {"message": "Acheter Premium"}, "licenseFile": {"message": "Fichier de licence"}, "licenseFileDesc": {"message": "Votre fichier de licence aura un nom similaire à $FILE_NAME$", "placeholders": {"file_name": {"content": "$1", "example": "bitwarden_premium_license.json"}}}, "uploadLicenseFilePremium": {"message": "Pour mettre à niveau votre compte vers une adhésion Premium, vous devez charger un fichier de licence valide."}, "uploadLicenseFileOrg": {"message": "Pour créer une organisation sur une instance auto-hébergée vous devez fournir un fichier de licence valide."}, "accountEmailMustBeVerified": {"message": "L'adresse électronique de votre compte doit être vérifiée."}, "newOrganizationDesc": {"message": "Les organisations permettent de partager des parties de votre coffre avec d'autres personnes ainsi que de gérer des utilisateurs pour une entité spécifique comme une famille, une petite équipe ou une grande entreprise."}, "generalInformation": {"message": "Informations générales"}, "organizationName": {"message": "Nom de l'organisation"}, "accountOwnedBusiness": {"message": "Ce compte est détenu par une entreprise."}, "billingEmail": {"message": "<PERSON><PERSON>riel de facturation"}, "businessName": {"message": "Nom de l'entreprise"}, "chooseYourPlan": {"message": "Choisissez votre plan"}, "users": {"message": "Utilisateurs"}, "userSeats": {"message": "Nombre de sièges utilisateurs"}, "additionalUserSeats": {"message": "Sièges d'utilisateurs supplémentaires"}, "userSeatsDesc": {"message": "Nombre de licences utilisateur"}, "userSeatsAdditionalDesc": {"message": "Votre offre comprend $BASE_SEATS$ licences utilisateurs. Vous pouvez ajouter des utilisateurs supplémentaires pour $SEAT_PRICE$ par utilisateur/mois.", "placeholders": {"base_seats": {"content": "$1", "example": "5"}, "seat_price": {"content": "$2", "example": "$2.00"}}}, "userSeatsHowManyDesc": {"message": "De combien de licences utilisateur avez-vous besoin ? Vous pouvez également en ajouter ultérieurement si besoin."}, "planNameFree": {"message": "<PERSON><PERSON><PERSON>", "description": "Free as in 'free beer'."}, "planDescFree": {"message": "Pour tester ou pour les utilisateurs individuels qui souhaitent partager avec $COUNT$ autre(s) utilisateur(s).", "placeholders": {"count": {"content": "$1", "example": "1"}}}, "planNameFamilies": {"message": "Familles"}, "planDescFamilies": {"message": "Pour une utilisation personnelle, pour partager avec la famille et les amis."}, "planNameTeams": {"message": "Équipes"}, "planDescTeams": {"message": "Pour les entreprises et autres équipes."}, "planNameTeamsStarter": {"message": "Équipes Essentiel"}, "planNameEnterprise": {"message": "Entreprise"}, "planDescEnterprise": {"message": "Pour les entreprises et autres grandes organisations."}, "freeForever": {"message": "Gratuit pour toujours"}, "includesXUsers": {"message": "comprend $COUNT$ utilisateurs", "placeholders": {"count": {"content": "$1", "example": "5"}}}, "additionalUsers": {"message": "Utilisateurs supplémentaires"}, "costPerUser": {"message": "$COST$ par utilisateur", "placeholders": {"cost": {"content": "$1", "example": "$3"}}}, "limitedUsers": {"message": "Limité à $COUNT$ utilisateurs (vous inclus)", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "limitedCollections": {"message": "Limité à $COUNT$ collections", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "addShareLimitedUsers": {"message": "Ajoutez et partagez avec jusqu'à $COUNT$ utilisateurs", "placeholders": {"count": {"content": "$1", "example": "5"}}}, "addShareUnlimitedUsers": {"message": "Ajoutez et partagez avec un nombre illimité d'utilisateurs"}, "createUnlimitedCollections": {"message": "Créez un nombre illimité de collections"}, "gbEncryptedFileStorage": {"message": "$SIZE$ de stockage de fichiers chiffrés", "placeholders": {"size": {"content": "$1", "example": "1 GB"}}}, "onPremHostingOptional": {"message": "Hébergement local (optionnel)"}, "usersGetPremium": {"message": "Les utilisateurs ont accès aux fonctionnalités Premium"}, "controlAccessWithGroups": {"message": "Contrôlez l'accès des utilisateurs avec des groupes"}, "syncUsersFromDirectory": {"message": "Synchronisez vos utilisateurs et vos groupes à partir d'un répertoire"}, "trackAuditLogs": {"message": "Suivez les actions des utilisateurs avec les journaux d'audit"}, "enforce2faDuo": {"message": "Imposer 2FA avec Duo"}, "priorityCustomerSupport": {"message": "Support client prioritaire"}, "xDayFreeTrial": {"message": "$COUNT$ jours d'essai gratuit, annulez à tout moment", "placeholders": {"count": {"content": "$1", "example": "7"}}}, "trialThankYou": {"message": "Merci de vous être inscrit à Bitwarden avec le $PLAN$!", "placeholders": {"plan": {"content": "$1", "example": "Teams"}}}, "trialSecretsManagerThankYou": {"message": "Merc<PERSON> de vous être inscrit au $PLAN$ pour Secrets Manager de Bitwarden !", "placeholders": {"plan": {"content": "$1", "example": "Teams"}}}, "trialPaidInfoMessage": {"message": "Votre essai gratuit de 7 jours à $PLAN$ sera converti en un abonnement payant après 7 jours.", "placeholders": {"plan": {"content": "$1", "example": "Teams"}}}, "trialConfirmationEmail": {"message": "Nous avons envoyé un courriel de confirmation sur le courriel de facturation de votre équipe à "}, "monthly": {"message": "<PERSON><PERSON><PERSON>"}, "annually": {"message": "<PERSON><PERSON><PERSON>"}, "annual": {"message": "<PERSON><PERSON>"}, "basePrice": {"message": "Prix de base"}, "organizationCreated": {"message": "L'organisation a été créée"}, "organizationReadyToGo": {"message": "Votre nouvelle organisation est prête !"}, "organizationUpgraded": {"message": "Votre organisation a été mise à niveau."}, "leave": {"message": "<PERSON><PERSON><PERSON>"}, "leaveOrganizationConfirmation": {"message": "Êtes-vous sûr de vouloir quitter cette organisation ?"}, "leftOrganization": {"message": "<PERSON><PERSON> avez quitté l'organisation."}, "defaultCollection": {"message": "Collection par défaut"}, "myItems": {"message": "Mes éléments"}, "getHelp": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON> l'aide"}, "getApps": {"message": "Télécharger les applications"}, "loggedInAs": {"message": "Connecté en tant que"}, "eventLogs": {"message": "Journal des événements"}, "people": {"message": "<PERSON><PERSON>"}, "policies": {"message": "Politiques de sécurité"}, "singleSignOn": {"message": "Authentification unique"}, "editPolicy": {"message": "Modifier la politique"}, "groups": {"message": "Groupes"}, "newGroup": {"message": "Nouveau groupe"}, "addGroup": {"message": "Ajouter un groupe"}, "editGroup": {"message": "Éditer le groupe"}, "deleteGroupConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer ce groupe?"}, "deleteMultipleGroupsConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer le(s) $QUANTITY$ groupe(s) suivant(s) ?", "placeholders": {"quantity": {"content": "$1", "example": "3"}}}, "removeUserConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer cet utilisateur ?"}, "removeOrgUserConfirmation": {"message": "Lorsqu'un membre est supprimé, il n'a plus accès aux données de l'organisation et cette action est irréversible. Pour réintégrer le membre dans l'organisation, il faut l'inviter et l'intégrer à nouveau."}, "revokeUserConfirmation": {"message": "Lorsqu'un membre est révoqué, il n'a plus accès aux données de l'organisation. Pour restaurer rapidement l'accès des membres, allez dans l'onglet Révoqué."}, "removeUserConfirmationKeyConnector": {"message": "Attention ! Cet utilisateur a besoin de Key Connector pour gérer son chiffrement. Si vous supprimez cet utilisateur de votre organisation, son compte sera définitivement désactivé. Cette action ne peut être annulée. Voulez-vous poursuivre ?"}, "externalId": {"message": "Identifiant externe"}, "externalIdDesc": {"message": "L’identifiant externe peut être utilisé comme référence ou pour lier cette ressource à un système externe tel qu’un répertoire utilisateur."}, "ssoExternalId": {"message": "ID externe SSO"}, "ssoExternalIdDesc": {"message": "ID externe SSO est une référence non chiffrée entre Bitwarden et votre fournisseur SSO configuré."}, "nestCollectionUnder": {"message": "Collection imbriquée sous"}, "accessControl": {"message": "Contrôle d’accès"}, "readOnly": {"message": "Lecture seule"}, "newCollection": {"message": "Nouvelle collection"}, "addCollection": {"message": "Ajouter une collection"}, "editCollection": {"message": "Modifier la collection"}, "collectionInfo": {"message": "Informations de la collection"}, "deleteCollectionConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer cette collection?"}, "editMember": {"message": "Éditer le membre"}, "fieldOnTabRequiresAttention": {"message": "Un champ de l'onglet '$TAB$' requiert votre attention.", "placeholders": {"tab": {"content": "$1", "example": "Collection info"}}}, "inviteUserDesc": {"message": "Invitez un nouvel utilisateur dans votre organisation en saisissant l'adresse électronique de son compte Bitwarden ci-dessous. S'il n'a pas encore de compte Bitwarden, il lui sera demandé de créer un nouveau compte."}, "inviteMultipleEmailDesc": {"message": "Saisissez jusqu'à $COUNT$ courriels en les séparant par une virgule.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inviteSingleEmailDesc": {"message": "Il vous reste 1 invitation."}, "inviteZeroEmailDesc": {"message": "Il vous reste 0 invitations."}, "userUsingTwoStep": {"message": "Cet utilisateur utilise l'authentification à deux facteurs pour protéger son compte."}, "search": {"message": "<PERSON><PERSON><PERSON>"}, "invited": {"message": "Invi<PERSON>"}, "confirmed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "clientOwnerEmail": {"message": "<PERSON><PERSON><PERSON> du propriétaire du client"}, "owner": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ownerDesc": {"message": "<PERSON><PERSON><PERSON> tous les aspects de votre organisation, y compris la facturation et les abonnements"}, "clientOwnerDesc": {"message": "Cet utilisateur doit être indépendant du fournisseur. Si le fournisseur est dissocié de l'organisation, cet utilisateur conservera la propriété de l'organisation."}, "admin": {"message": "Administrateur"}, "adminDesc": {"message": " Les administrateurs peuvent voir et gérer tous les éléments, les collections et les utilisateurs de votre organisation."}, "user": {"message": "Utilisa<PERSON>ur"}, "userDesc": {"message": "Un utilisateur normal avec accès aux collections de votre organisation."}, "all": {"message": "Tous"}, "addAccess": {"message": "Ajouter Access"}, "addAccessFilter": {"message": "Ajouter un filtre Access"}, "refresh": {"message": "Actualiser"}, "timestamp": {"message": "Horodatage"}, "event": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unknown": {"message": "Inconnu"}, "loadMore": {"message": "Plus de résultats"}, "mobile": {"message": "Mobile", "description": "Mobile app"}, "extension": {"message": "Extension", "description": "Browser extension/addon"}, "desktop": {"message": "Ordinateur", "description": "Desktop app"}, "webVault": {"message": "Coffre web"}, "cli": {"message": "CLI"}, "bitWebVault": {"message": "<PERSON><PERSON><PERSON> <PERSON> de Bitwarden"}, "bitSecretsManager": {"message": "Secrets Manager <PERSON>"}, "loggedIn": {"message": "Connecté."}, "changedPassword": {"message": "Mot de passe changé."}, "enabledUpdated2fa": {"message": "Authentification à deux facteurs sauvegardée"}, "disabled2fa": {"message": "Authentification à deux facteurs désactivée"}, "recovered2fa": {"message": "Compte récupéré à partir de l'authentification à deux facteurs."}, "failedLogin": {"message": "Tentative de connexion avec mot de passe incorrect."}, "failedLogin2fa": {"message": "Tentative de connexion échouée avec une authentification à deux facteurs incorrecte."}, "incorrectPassword": {"message": "Mot de passe incorrect"}, "incorrectCode": {"message": "Code incorrect"}, "incorrectPin": {"message": "NIP incorrect"}, "pin": {"message": "NIP", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "exportedVault": {"message": "Le coffre a été exporté."}, "exportedOrganizationVault": {"message": "Le coffre de l'organisation a été exporté."}, "editedOrgSettings": {"message": "Paramètres de l’organisation modifiés."}, "createdItemId": {"message": "Élément $ID$ créé.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "editedItemId": {"message": "Élément $ID$ modifié.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "deletedItemId": {"message": "L'élément $ID$ a été envoyé dans la corbeille.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "movedItemIdToOrg": {"message": "L'élément $ID$ a été déplacé vers une organisation.", "placeholders": {"id": {"content": "$1", "example": "'Google'"}}}, "viewAllLogInOptions": {"message": "Afficher toutes les options de connexion"}, "viewAllLoginOptions": {"message": "Voir toutes les options de connexion"}, "viewedItemId": {"message": "L'élément $ID$ a été consulté.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "viewedPasswordItemId": {"message": "Le mot de passe de l'élément $ID$ a été consulté.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "viewedHiddenFieldItemId": {"message": "Un champ masqué de l’élément $ID$ a été consulté.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "viewedCardNumberItemId": {"message": "Numéro de Carte visionné pour l'article $ID$.", "placeholders": {"id": {"content": "$1", "example": "Unique ID"}}}, "viewedSecurityCodeItemId": {"message": "Le code de sécurité de l’élément $ID$ a été consulté.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "viewCollectionWithName": {"message": "Afficher la collection - $NAME$", "placeholders": {"name": {"content": "$1", "example": "Collection1"}}}, "editItemWithName": {"message": "Éditer l'élément - $NAME$", "placeholders": {"name": {"content": "$1", "example": "Google Login"}}}, "copiedPasswordItemId": {"message": "Le mot de passe de l'élément $ID$ a été copié.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "copiedHiddenFieldItemId": {"message": "Un champ masqué de l’élément $ID$ a été copié.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "copiedSecurityCodeItemId": {"message": "Le code de sécurité de l’élément $ID$ a été copié.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "autofilledItemId": {"message": "Élément $ID$ saisi automatiquement.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "createdCollectionId": {"message": "Collection $ID$ créée.", "placeholders": {"id": {"content": "$1", "example": "Server Passwords"}}}, "editedCollectionId": {"message": "Collection $ID$ modifiée.", "placeholders": {"id": {"content": "$1", "example": "Server Passwords"}}}, "deletedCollections": {"message": "Collections supprimées"}, "deletedCollectionId": {"message": "Collection $ID$ supprimée.", "placeholders": {"id": {"content": "$1", "example": "Server Passwords"}}}, "editedPolicyId": {"message": "Politique $ID$ modifiée.", "placeholders": {"id": {"content": "$1", "example": "Master Password"}}}, "createdGroupId": {"message": "Groupe $ID$ créé.", "placeholders": {"id": {"content": "$1", "example": "Developers"}}}, "editedGroupId": {"message": "Groupe $ID$ modifié.", "placeholders": {"id": {"content": "$1", "example": "Developers"}}}, "deletedGroupId": {"message": "Groupe $ID$ supprimé.", "placeholders": {"id": {"content": "$1", "example": "Developers"}}}, "deletedManyGroups": {"message": "$QUANTITY$ groupe(s) supprimé(s).", "placeholders": {"quantity": {"content": "$1", "example": "3"}}}, "removedUserId": {"message": "Utilisateur $ID$ supprimé.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "removeUserIdAccess": {"message": "Supprimer l'accès à $ID$", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "revokedUserId": {"message": "Accès à l'organisation révoqué pour $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "restoredUserId": {"message": "Accès à l'organisation restauré pour $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "revokeUserId": {"message": "Révoquer l'accès à $ID$", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "createdAttachmentForItem": {"message": "La pièce jointe pour l'élément $ID$ a été créée.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "deletedAttachmentForItem": {"message": "La pièce jointe de l'élément $ID$ a été supprimée.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "editedCollectionsForItem": {"message": "Collections éditées pour l'élément $ID$.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "invitedUserId": {"message": "Utilisateur $ID$ invité.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "confirmedUserId": {"message": "Utilisateur $ID$ confirmé.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "editedUserId": {"message": "Utilisateur $ID$ modifié.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "editedGroupsForUser": {"message": "Groupes édités pour l'utilisateur $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "unlinkedSso": {"message": "SSO non lié."}, "unlinkedSsoUser": {"message": "SSO dissocié pour l'utilisateur $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "createdOrganizationId": {"message": "Organisation $ID$ créée.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "addedOrganizationId": {"message": "Organisation $ID$ ajoutée.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "removedOrganizationId": {"message": "Organisation $ID$ supprimée.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "accessedClientVault": {"message": "Coffre-fort de l'organisation $ID$ consulté.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "device": {"message": "Appareil"}, "loginStatus": {"message": "Statut de connexion"}, "firstLogin": {"message": "Première connexion"}, "trusted": {"message": "Approu<PERSON><PERSON>"}, "needsApproval": {"message": "Requiert une approbation"}, "areYouTryingtoLogin": {"message": "Essayez-vous de vous connecter ?"}, "logInAttemptBy": {"message": "Tentative de connexion par $EMAIL$", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "deviceType": {"message": "Type d'appareil"}, "ipAddress": {"message": "Adresse IP"}, "confirmLogIn": {"message": "Confirmer la connexion"}, "denyLogIn": {"message": "Refuser la connexion"}, "thisRequestIsNoLongerValid": {"message": "Cette demande n'est plus valide."}, "logInConfirmedForEmailOnDevice": {"message": "Connexion confirmée pour $EMAIL$ sur $DEVICE$", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "device": {"content": "$2", "example": "iOS"}}}, "youDeniedALogInAttemptFromAnotherDevice": {"message": "Vous avez refusé une tentative de connexion depuis un autre appareil. Si c'était vraiment vous, essayez de vous connecter à nouveau avec l'appareil."}, "loginRequestHasAlreadyExpired": {"message": "La demande de connexion a déjà expiré."}, "justNow": {"message": "À l’instant"}, "requestedXMinutesAgo": {"message": "Demandé il y a $MINUTES$ minutes", "placeholders": {"minutes": {"content": "$1", "example": "5"}}}, "creatingAccountOn": {"message": "Création du compte sur"}, "checkYourEmail": {"message": "Vérifiez vos courriels"}, "followTheLinkInTheEmailSentTo": {"message": "Su<PERSON>z le lien dans le courriel envoyé à"}, "andContinueCreatingYourAccount": {"message": "et continuer à créer votre compte."}, "noEmail": {"message": "Pas co<PERSON><PERSON>?"}, "goBack": {"message": "Revenir en arrière"}, "toEditYourEmailAddress": {"message": "pour modifier votre adresse courriel."}, "view": {"message": "Voir"}, "invalidDateRange": {"message": "Plage de dates non valide."}, "errorOccurred": {"message": "Une erreur est survenue."}, "userAccess": {"message": "Accès utilisateur"}, "userType": {"message": "Type d'utilisateur"}, "groupAccess": {"message": "Accès groupes"}, "groupAccessUserDesc": {"message": "Modifier les groupes auxquels appartient cet utilisateur."}, "invitedUsers": {"message": "Utilisa<PERSON>ur(s) invité(s)."}, "resendInvitation": {"message": "Renvoyer l'invitation"}, "resendEmail": {"message": "Renvoyer l'email"}, "hasBeenReinvited": {"message": "$USER$ a été réinvité.", "placeholders": {"user": {"content": "$1", "example": "<PERSON>"}}}, "confirm": {"message": "Confirmer"}, "confirmUser": {"message": "Confirmer l'utilisateur"}, "hasBeenConfirmed": {"message": "$USER$ a été confirmé.", "placeholders": {"user": {"content": "$1", "example": "<PERSON>"}}}, "confirmUsers": {"message": "Confirmer les utilisateurs"}, "usersNeedConfirmed": {"message": "Il y a des utilisateurs qui ont accepté leur invitation, mais qui doivent encore être confirmés. Les utilisateurs n'auront pas accès à l'organisation tant qu'ils n'auront pas été confirmés."}, "startDate": {"message": "Date de début"}, "endDate": {"message": "Date de fin"}, "verifyEmail": {"message": "Véri<PERSON>r le courriel"}, "verifyEmailDesc": {"message": "Vérifiez l'adresse électronique de votre compte pour débloquer l'accès à toutes les fonctionnalités."}, "verifyEmailFirst": {"message": "L'adresse électronique de votre compte doit d'abord être vérifiée."}, "checkInboxForVerification": {"message": "Vérifiez votre boîte de réception pour le lien de vérification."}, "emailVerified": {"message": "<PERSON><PERSON><PERSON> du compte vérifié"}, "emailVerifiedV2": {"message": "<PERSON><PERSON><PERSON>"}, "emailVerifiedFailed": {"message": "Impossible de vérifier votre courriel. Essayez en envoyant un nouveau courriel de vérification."}, "emailVerificationRequired": {"message": "Vérification de courriel requise"}, "emailVerificationRequiredDesc": {"message": "V<PERSON> devez vérifier votre courriel pour utiliser cette fonctionnalité."}, "updateBrowser": {"message": "Mettre à jour le navigateur"}, "generatingYourRiskInsights": {"message": "Génération de vos Aperçus de Risque..."}, "updateBrowserDesc": {"message": "Vous utilisez un navigateur non supporté. Le coffre web pourrait ne pas fonctionner correctement."}, "youHaveAPendingLoginRequest": {"message": "Vous avez une demande de connexion en attente depuis un autre appareil."}, "reviewLoginRequest": {"message": "Examiner la demande de connexion"}, "freeTrialEndPromptCount": {"message": "Votre essai gratuit se termine dans $COUNT$ jours.", "placeholders": {"count": {"content": "$1", "example": "remaining days"}}}, "freeTrialEndPromptMultipleDays": {"message": "$ORGANIZATION$, votre essai gratuit se termine dans $COUNT$ jours.", "placeholders": {"count": {"content": "$2", "example": "remaining days"}, "organization": {"content": "$1", "example": "organization name"}}}, "freeTrialEndPromptTomorrow": {"message": "$ORGANIZATION$, votre essai gratuit se termine demain.", "placeholders": {"organization": {"content": "$1", "example": "organization name"}}}, "freeTrialEndPromptTomorrowNoOrgName": {"message": "Votre essai gratuit se termine demain."}, "freeTrialEndPromptToday": {"message": "$ORGANIZATION$, votre essai gratuit se termine aujourd'hui.", "placeholders": {"organization": {"content": "$1", "example": "organization name"}}}, "freeTrialEndingTodayWithoutOrgName": {"message": "Votre essai gratuit se termine aujourd'hui."}, "clickHereToAddPaymentMethod": {"message": "Cliquer ici pour ajouter une méthode de paiement."}, "joinOrganization": {"message": "Rejoindre l'organisation"}, "joinOrganizationName": {"message": "Rejoindre $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "joinOrganizationDesc": {"message": "Vous avez été invité à rejoindre l'organisation ci-dessus. Pour accepter l'invitation, vous devez vous connecter ou créer un nouveau compte Bitwarden."}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Terminer de rejoindre cette organisation en définissant un mot de passe principal."}, "inviteAccepted": {"message": "Invitation acceptée"}, "inviteAcceptedDesc": {"message": "Vous pourrez accéder à cette organisation dès qu'un administrateur aura confirmé votre adhésion. Nous vous enverrons un courriel lorsque cela se produira."}, "inviteInitAcceptedDesc": {"message": "<PERSON><PERSON> pouvez maintenant accéder à cette organisation."}, "inviteAcceptFailed": {"message": "Impossible d’accepter l’invitation. Demandez à un administrateur de l'organisation d’envoyer une nouvelle invitation."}, "inviteAcceptFailedShort": {"message": "Impossible d'accepter l'invitation. $DESCRIPTION$", "placeholders": {"description": {"content": "$1", "example": "You must set up 2FA on your user account before you can join this organization."}}}, "rememberEmail": {"message": "Se souvenir du courriel"}, "recoverAccountTwoStepDesc": {"message": "Si vous ne pouvez pas accéder à votre compte par les méthodes normales d'authentification à deux facteurs, vous pouvez utiliser votre code de récupération d'authentification à deux facteurs pour désactiver tous les fournisseurs à deux facteurs sur votre compte."}, "logInBelowUsingYourSingleUseRecoveryCode": {"message": "Connectez-vous ci-dessous en utilisant votre code de récupération à usage unique. <PERSON><PERSON> d<PERSON>ra tous les fournisseurs d'authentification à deux facteurs de votre compte."}, "recoverAccountTwoStep": {"message": "Récupérer l'authentification à deux facteurs"}, "twoStepRecoverDisabled": {"message": "Authentification à deux facteurs désactivée sur votre compte."}, "learnMore": {"message": "En savoir plus"}, "deleteRecoverDesc": {"message": "Saisissez votre adresse électronique ci-dessous pour récupérer et supprimer votre compte."}, "deleteRecoverEmailSent": {"message": "Si votre compte existe, nous vous avons envoyé un courriel avec des instructions supplémentaires."}, "deleteRecoverConfirmDesc": {"message": "Vous avez demandé de supprimer votre compte Bitwarden. Cliquez sur le bouton ci-dessous pour confirmer."}, "deleteRecoverOrgConfirmDesc": {"message": "Vous avez demandé à supprimer votre organisation Bitwarden."}, "myOrganization": {"message": "Mon organisation"}, "organizationInfo": {"message": "Informations sur l'Organisation"}, "deleteOrganization": {"message": "Supprimer l'organisation"}, "deletingOrganizationContentWarning": {"message": "Saisir le mot de passe principal pour confirmer la suppression de $ORGANIZATION$ et de toutes les données associées. Les données du coffre de $ORGANIZATION$ comprennent :", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "deletingOrganizationActiveUserAccountsWarning": {"message": "Les comptes utilisateur resteront actifs après la suppression, mais ne seront plus associés à cette organisation."}, "deletingOrganizationIsPermanentWarning": {"message": "La suppression de $ORGANIZATION$ est permanente et irréversible.", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "organizationDeleted": {"message": "Organisation supprimée"}, "organizationDeletedDesc": {"message": "L’organisation et toutes les données associées ont été supprimées."}, "organizationUpdated": {"message": "Organisation mise à jour"}, "taxInformation": {"message": "Informations fiscales"}, "taxInformationDesc": {"message": "Pour les clients aux États-Unis, le code postal (ZIP) est requis pour satisfaire aux exigences de la taxe de vente. Pour d'autres pays, vous pouvez éventuellement fournir un numéro d'identification fiscale (TVA/TPS) et/ou une adresse à afficher sur vos factures."}, "billingPlan": {"message": "Offre", "description": "A billing plan/package. For example: Families, Teams, Enterprise, etc."}, "changeBillingPlan": {"message": "Changer d'offre", "description": "A billing plan/package. For example: Families, Teams, Enterprise, etc."}, "changeBillingPlanUpgrade": {"message": "Met<PERSON>z à niveau votre compte vers un autre plan en fournissant les informations ci-dessous. Assurez-vous qu'un mode de paiement actif a été ajouté au compte.", "description": "A billing plan/package. For example: Families, Teams, Enterprise, etc."}, "invoiceNumber": {"message": "Facture n°$NUMBER$", "description": "ex. Invoice #79C66F0-0001", "placeholders": {"number": {"content": "$1", "example": "79C66F0-0001"}}}, "viewInvoice": {"message": "Afficher la facture"}, "downloadInvoice": {"message": "Télécharger la facture"}, "verifyBankAccount": {"message": "Vérifier le compte bancaire"}, "verifyBankAccountDesc": {"message": "Nous avons effectué deux dépôts d'un faible montant sur votre compte bancaire (ils peuvent prendre 1-2 jours ouvrés pour apparaître). Saisissez ces montants pour valider le compte bancaire."}, "verifyBankAccountInitialDesc": {"message": "Le paiement avec un compte bancaire est seulement disponible pour les clients résidant aux États-Unis. Il vous sera demandé de valider votre compte bancaire. Nous effectuerons deux dépôts d'un faible montant sous 1-2 jours ouvrés. Saisissez ces montants sur la page de facturation de votre organisation pour valider votre compte bancaire."}, "verifyBankAccountFailureWarning": {"message": "Une erreur lors de la validation de votre compte bancaire annulera le paiement et votre abonnement sera désactivé."}, "verifiedBankAccount": {"message": "Le compte bancaire a été vérifié."}, "bankAccount": {"message": "Compte bancaire"}, "amountX": {"message": "Montant $COUNT$", "description": "Used in bank account verification of micro-deposits. Amount, as in a currency amount. Ex. Amount 1 is $2.00, Amount 2 is $1.50", "placeholders": {"count": {"content": "$1", "example": "1"}}}, "routingNumber": {"message": "Code banque", "description": "Bank account routing number"}, "accountNumber": {"message": "Numéro de compte"}, "accountHolderName": {"message": "Nom du titulaire du compte"}, "bankAccountType": {"message": "Type de compte"}, "bankAccountTypeCompany": {"message": "Entreprise (professionnel)"}, "bankAccountTypeIndividual": {"message": "<PERSON><PERSON><PERSON><PERSON> (personnel)"}, "enterInstallationId": {"message": "Saisissez l'identifiant de votre installation"}, "limitSubscriptionDesc": {"message": "Définissez une limite de licences pour votre abonnement. Une fois cette limite atteinte, vous ne pourrez plus inviter de nouveaux membres."}, "limitSmSubscriptionDesc": {"message": "Définissez une limite de places pour votre abonnement au Secrets Manager. Une fois cette limite atteinte, vous ne pourrez plus inviter de nouveaux membres."}, "maxSeatLimit": {"message": "Nombre de licences maximum (optionnel)", "description": "Upper limit of seats to allow through autoscaling"}, "maxSeatCost": {"message": "Coût potentiel maximal des licences"}, "addSeats": {"message": "Ajouter des sièges", "description": "Seat = User Seat"}, "removeSeats": {"message": "Supprimer des licences", "description": "Seat = User Seat"}, "subscriptionDesc": {"message": "Les ajustements apportés à votre abonnement entraîneront des modifications au prorata de vos totaux de facturation. Si les utilisateurs nouvellement invités dépassent votre nombre de licences, vous recevrez immédiatement des frais au prorata pour les utilisateurs supplémentaires."}, "subscriptionUserSeats": {"message": "Votre abonnement permet un total de $COUNT$ membres.", "placeholders": {"count": {"content": "$1", "example": "50"}}}, "limitSubscription": {"message": "Limiter l'abonnement (facultatif)"}, "subscriptionSeats": {"message": "Licences"}, "subscriptionUpdated": {"message": "Abonnement mis à jour"}, "subscribedToSecretsManager": {"message": "Abonnement mis à jour. Vous avez maintenant accès au Secrets Manager."}, "additionalOptions": {"message": "Options supplémentaires"}, "additionalOptionsDesc": {"message": "Pour obtenir de l'aide supplémentaire dans la gestion de votre abonnement, veuillez contacter le support client."}, "subscriptionUserSeatsUnlimitedAutoscale": {"message": "Les ajustements apportés à votre abonnement entraîneront des modifications au prorata de vos totaux de facturation. Si les utilisateurs nouvellement invités dépassent votre nombre de licences, vous recevrez immédiatement des frais au prorata pour les utilisateurs supplémentaires."}, "smStandaloneTrialSeatCountUpdateMessageFragment1": {"message": "Si vous souhaitez ajouter"}, "smStandaloneTrialSeatCountUpdateMessageFragment2": {"message": "des licences sans l'offre groupée, veuillez contacter"}, "subscriptionUserSeatsLimitedAutoscale": {"message": "Les ajustements apportés à votre abonnement entraîneront des modifications au prorata de vos totaux de facturation. Si les utilisateurs nouvellement invités dépassent votre nombre de licences, vous recevrez immédiatement des frais au prorata pour les utilisateurs supplémentaires jusqu'à ce que votre limite de $MAX$ licences soit atteinte.", "placeholders": {"max": {"content": "$1", "example": "50"}}}, "subscriptionUserSeatsWithoutAdditionalSeatsOption": {"message": "Vous pouvez inviter jusqu'à $COUNT$ membres sans coût additionnel. Contacter le Support Client pour améliorer votre abonnement à et inviter plus de membres.", "placeholders": {"count": {"content": "$1", "example": "10"}}}, "subscriptionFreePlan": {"message": "Vous ne pouvez pas inviter plus de $COUNT$ utilisateurs sans mettre votre offre à niveau.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "subscriptionUpgrade": {"message": "Vous ne pouvez pas inviter plus de $COUNT$ membres sans passer à une offre supérieure.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "subscriptionSponsoredFamiliesPlan": {"message": "Votre offre permet un total de $COUNT$ utilisateurs. Votre abonnement est parrainé et facturé à une organisation externe.", "placeholders": {"count": {"content": "$1", "example": "6"}}}, "subscriptionMaxReached": {"message": "Les ajustements apportés à votre abonnement entraîneront des modifications au prorata de vos totaux de facturation. Vous ne pouvez pas inviter plus de $COUNT$ utilisateurs sans augmenter votre nombre de licences.", "placeholders": {"count": {"content": "$1", "example": "50"}}}, "subscriptionSeatMaxReached": {"message": "Vous ne pouvez pas inviter plus de membres $COUNT$ sans augmenter le nombre de licences de votre abonnement.", "placeholders": {"count": {"content": "$1", "example": "50"}}}, "seatsToAdd": {"message": "Sièges à ajouter"}, "seatsToRemove": {"message": "Licences à supprimer"}, "seatsAddNote": {"message": "L'ajout de licences utilisateur entraînera des ajustements sur vos totaux de facturation et facturera immédiatement le moyen de paiement enregistré. La première facturation sera calculée au prorata du reste du cycle de facturation en cours."}, "seatsRemoveNote": {"message": "La suppression de licences utilisateur entraînera des ajustements de vos totaux de facturation qui seront répartis au prorata sous forme de crédits sur votre prochaine facturation."}, "adjustedSeats": {"message": "$AMOUNT$ licences utilisateurs mis à jour.", "placeholders": {"amount": {"content": "$1", "example": "15"}}}, "editFieldLabel": {"message": "Modifier $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Réorganiser $LABEL$. Utilisez la touche fléchée pour déplacer l'élément vers le haut ou vers le bas.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderFieldUp": {"message": "$LABEL$ déplacé vers le haut, position $INDEX$ de $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "reorderFieldDown": {"message": "$LABEL$ déplacé vers le bas, position $INDEX$ de $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "updateEncryptionKeyWarning": {"message": "Après avoir mis à jour votre clé de chiffrement, vous devez vous déconnecter et vous reconnecter à toutes les applications Bitwarden que vous utilisez actuellement (comme l'application mobile ou les extensions de navigateur). Si vous ne vous déconnectez pas et ne vous reconnectez pas (ce qui télécharge votre nouvelle clé de chiffrement), cela peut entraîner une corruption des données. Nous tenterons de vous déconnecter automatiquement, mais cela peut être retardé."}, "updateEncryptionKeyAccountExportWarning": {"message": "Toutes les exportations restreintes du compte que vous avez enregistrées seront invalides."}, "legacyEncryptionUnsupported": {"message": "L'ancien chiffrement n'est plus pris en charge. Veuillez contacter le support pour récupérer votre compte."}, "subscription": {"message": "Abonnement"}, "loading": {"message": "Chargement"}, "upgrade": {"message": "Mettre à niveau"}, "upgradeOrganization": {"message": "Mettre à jour l'organisation"}, "upgradeOrganizationDesc": {"message": "Cette fonctionnalité n'est pas disponible pour les organisations gratuites. Passez à une offre payante pour débloquer plus de fonctionnalités."}, "createOrganizationStep1": {"message": "Créer une organisation: Étape 1"}, "createOrganizationCreatePersonalAccount": {"message": "Avant de créer votre organisation, vous devez d’abord créer un compte personnel gratuit."}, "refunded": {"message": "Re<PERSON><PERSON><PERSON>"}, "nothingSelected": {"message": "Vous n'avez rien sélectionné."}, "receiveMarketingEmailsV2": {"message": "Obtenez des conseils, des annonces et des opportunités de recherche de la part de Bitwarden dans votre boîte de réception."}, "unsubscribe": {"message": "Désabonnez-vous"}, "atAnyTime": {"message": "à tout moment."}, "byContinuingYouAgreeToThe": {"message": "En continuant, vous acceptez les"}, "and": {"message": "et"}, "acceptPolicies": {"message": "En cochant cette case vous acceptez ce qui suit :"}, "acceptPoliciesRequired": {"message": "Les conditions d'utilisation et la politique de confidentialité n'ont pas été acceptées."}, "termsOfService": {"message": "Conditions d'utilisation"}, "privacyPolicy": {"message": "Politique de confidentialité"}, "filters": {"message": "Filtres"}, "vaultTimeout": {"message": "<PERSON><PERSON><PERSON> d'expiration du coffre"}, "vaultTimeout1": {"message": "<PERSON><PERSON><PERSON> d'expiration"}, "vaultTimeoutDesc": {"message": "Choisissez quand votre coffre entreprendra l'action après le délai d'expiration du coffre."}, "vaultTimeoutLogoutDesc": {"message": "Choisissez quand votre coffre sera déconnecté."}, "oneMinute": {"message": "1 minute"}, "fiveMinutes": {"message": "5 minutes"}, "fifteenMinutes": {"message": "15 minutes"}, "thirtyMinutes": {"message": "30 minutes"}, "oneHour": {"message": "1 heure"}, "fourHours": {"message": "4 heures"}, "onRefresh": {"message": "Au rechargement de la page"}, "dateUpdated": {"message": "Mis à jour", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "Mot de passe mis à jour", "description": "ex. Date this password was updated"}, "organizationIsDisabled": {"message": "L'organisation est désactivée."}, "secretsAccessSuspended": {"message": "Impossible d'accéder aux organisations suspendues. Veuillez contacter le propriétaire de votre organisation pour obtenir de l'aide."}, "secretsCannotCreate": {"message": "Les secrets ne peuvent pas être créés dans les organisations suspendues. Veuillez contacter le propriétaire de votre organisation pour obtenir de l'aide."}, "projectsCannotCreate": {"message": "Les projets ne peuvent pas être créés dans les organisations suspendues. Veuillez contacter le propriétaire de votre organisation pour obtenir de l'aide."}, "serviceAccountsCannotCreate": {"message": "Les comptes de service ne peuvent pas être créés dans les organisations suspendues. Veuillez contacter le propriétaire de votre organisation pour obtenir de l'aide."}, "disabledOrganizationFilterError": {"message": "Les éléments des Organisations désactivées ne sont pas accessibles. Contactez le propriétaire de votre Organisation pour obtenir de l'aide."}, "licenseIsExpired": {"message": "La licence a expiré."}, "updatedUsers": {"message": "Utilisateurs mis à jour"}, "selected": {"message": "Sélectionné(s)"}, "recommended": {"message": "Recommandé"}, "ownership": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "whoOwnsThisItem": {"message": "À qui appartient cet élément ?"}, "strong": {"message": "Fort", "description": "ex. A strong password. Scale: Very Weak -> Weak -> Good -> Strong"}, "good": {"message": "Suffisant", "description": "ex. A good password. Scale: Very Weak -> Weak -> Good -> Strong"}, "weak": {"message": "Faible", "description": "ex. A weak password. Scale: Very Weak -> Weak -> Good -> Strong"}, "veryWeak": {"message": "<PERSON><PERSON><PERSON> faible", "description": "ex. A very weak password. Scale: Very Weak -> Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "Mot de passe principal faible"}, "weakMasterPasswordDesc": {"message": "Le mot de passe principal que vous avez choisi est faible. Vous devriez utiliser un mot de passe principal fort (ou une phrase de passe) pour protéger correctement votre compte Bitwarden. Êtes-vous sûr de vouloir utiliser ce mot de passe principal ?"}, "rotateAccountEncKey": {"message": "Régénérer également la clé de chiffrement de mon compte"}, "rotateEncKeyTitle": {"message": "Régénérer la clé de chiffrement"}, "rotateEncKeyConfirmation": {"message": "Êtes-vous sûr de vouloir révoquer la clé de chiffrement de votre compte ?"}, "attachmentsNeedFix": {"message": "Cet élément a d'anciennes pièces jointes qui doivent être réparées."}, "attachmentFixDescription": {"message": "Cette pièce jointe utilise un chiffrement obsolète. Choi<PERSON><PERSON>z 'Réparer' pour télécharger, chiffrer à nouveau et téléverser à nouveau la pièce jointe."}, "fix": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "This is a verb. ex. 'Fix The Car'"}, "oldAttachmentsNeedFixDesc": {"message": "Il y a d'anciennes pièces jointes dans votre coffre qui doivent être réparées avant que vous ne puissiez régénérer la clé de chiffrement de votre compte."}, "yourAccountsFingerprint": {"message": "Phrase d'empreinte de votre compte", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "fingerprintEnsureIntegrityVerify": {"message": "Pour assurer l'intégrité de vos clés de chiffrement, merci de saisir la phrase d'empreinte de l'utilisateur avant de continuer.", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "fingerprintMatchInfo": {"message": "V<PERSON><PERSON><PERSON> vous assurer que votre coffre est déverrouillé et que la Phrase d'Empreinte correspond à l'autre appareil."}, "fingerprintPhraseHeader": {"message": "Phrase d'empreinte"}, "dontAskFingerprintAgain": {"message": "Ne jamais demander de vérifier la phrase d'empreinte pour les utilisateurs invités (non recommandé)", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "Vous serez notifié une fois que la demande sera approuvée"}, "free": {"message": "<PERSON><PERSON><PERSON>", "description": "Free, as in 'Free beer'"}, "apiKey": {"message": "Clé API"}, "apiKeyDesc": {"message": "Votre clé API peut être utilisée pour vous authentifier auprès de l'API publique de Bitwarden."}, "apiKeyRotateDesc": {"message": "La régénération de la clé API invalidera la clé précédente. Vous pouvez régénérer votre clé API si vous estimez que la clé actuelle n'est plus utilisable en toute sécurité."}, "apiKeyWarning": {"message": "Votre clé API possède un accès complet à l'organisation. Elle devrait être tenue secrète."}, "userApiKeyDesc": {"message": "Votre clé API peut être utilisée pour vous authentifier dans la CLI de Bitwarden."}, "userApiKeyWarning": {"message": "Votre clé API est un mécanisme d'authentification alternatif. Elle devrait être tenue secrète."}, "oauth2ClientCredentials": {"message": "Identifiants du client OAuth 2.0", "description": "'OAuth 2.0' is a programming protocol. It should probably not be translated."}, "viewApiKey": {"message": "Afficher la clé API"}, "rotateApiKey": {"message": "Régénérer la clé API"}, "selectOneCollection": {"message": "<PERSON><PERSON> de<PERSON> sélectionner au moins une collection."}, "couldNotChargeCardPayInvoice": {"message": "Nous n'avons pas pu compléter le paiement sur votre carte. Veuillez consulter et payer la facture impayée ci-dessous."}, "minLength": {"message": "Longueur minimale"}, "clone": {"message": "<PERSON><PERSON><PERSON>"}, "masterPassPolicyTitle": {"message": "Exigences du mot de passe principal"}, "masterPassPolicyDesc": {"message": "Définir les exigences de robustesse du mot de passe principal."}, "passwordStrengthScore": {"message": "Score de force du mot de passe $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "twoStepLoginPolicyTitle": {"message": "Exiger une authentification à deux facteurs"}, "twoStepLoginPolicyDesc": {"message": "Exiger des membres qu'ils mettent en place une authentification à deux facteurs."}, "twoStepLoginPolicyWarning": {"message": "Les membres de l'organisation qui ne sont pas propriétaires ou administrateurs et qui n'ont pas configuré l'authentification à deux facteurs pour leur compte seront supprimés de l'organisation et recevront un courriel les informant de ce changement."}, "twoStepLoginPolicyUserWarning": {"message": "Vous êtes un membre d'une organisation qui exige que l'authentification à deux facteurs soit configurée sur votre compte utilisateur. Si vous désactivez tous les fournisseurs d'authentification à deux facteurs, vous serez automatiquement retiré de ces organisations."}, "passwordGeneratorPolicyDesc": {"message": "Définir les exigences pour le générateur de mot de passe."}, "masterPasswordPolicyInEffect": {"message": "Une ou plusieurs politiques de sécurité de l'organisation exigent que votre mot de passe principal réponde aux exigences suivantes :"}, "policyInEffectMinComplexity": {"message": "Score de complexité minimum de $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "Longueur minimale de $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "Contenir une ou plusieurs majuscules"}, "policyInEffectLowercase": {"message": "Contenir une ou plusieurs minuscules"}, "policyInEffectNumbers": {"message": "Contenir un ou plusieurs chiffres"}, "policyInEffectSpecial": {"message": "Contenir un ou plusieurs des caractères spéciaux suivants $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "Votre nouveau mot de passe principal ne répond pas aux exigences de politique de sécurité."}, "minimumNumberOfWords": {"message": "Nombre minimum de mots"}, "overridePasswordTypePolicy": {"message": "Type de Mot de Passe", "description": "Name of the password generator policy that overrides the user's password/passphrase selection."}, "userPreference": {"message": "Choix laissé à l'utilisateur"}, "vaultTimeoutAction": {"message": "Action après délai d'expiration du coffre"}, "vaultTimeoutActionLockDesc": {"message": "Un mot de passe principal ou une autre méthode de déverrouillage est nécessaire pour accéder à nouveau à votre coffre."}, "vaultTimeoutActionLogOutDesc": {"message": "Une nouvelle authentification est requise pour accéder à nouveau à votre coffre."}, "lock": {"message": "Verrouiller", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Noun: A special folder for holding deleted items that have not yet been permanently deleted"}, "searchTrash": {"message": "Rechercher dans la corbeille"}, "permanentlyDelete": {"message": "Supprimer définitivement"}, "permanentlyDeleteSelected": {"message": "Supprimer définitivement la sélection"}, "permanentlyDeleteItem": {"message": "Supprimer définitivement l'élément"}, "permanentlyDeleteItemConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer définitivement cet élément ?"}, "permanentlyDeletedItem": {"message": "Élément supprimé définitivement"}, "permanentlyDeletedItems": {"message": "Éléments supprimés définitivement"}, "permanentlyDeleteSelectedItemsDesc": {"message": "Vous avez sélectionné $COUNT$ élément(s) à supprimer définitivement. Êtes-vous sûr de vouloir supprimer définitivement tous ces éléments ?", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "permanentlyDeletedItemId": {"message": "Élément $ID$ supprimé définitivement.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "restore": {"message": "<PERSON><PERSON><PERSON>"}, "restoreSelected": {"message": "Restaurer la sélection"}, "restoredItem": {"message": "Élément restauré"}, "restoredItems": {"message": "Éléments restaurés"}, "restoredItemId": {"message": "Élément $ID$ restauré.", "placeholders": {"id": {"content": "$1", "example": "Google"}}}, "vaultTimeoutLogOutConfirmation": {"message": "La déconnexion supprimera tout accès à votre coffre et nécessitera une authentification en ligne après la période d'expiration. Êtes-vous sûr de vouloir utiliser ce paramètre ?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "Confirmation de l'action lors de l'expiration du délai"}, "hidePasswords": {"message": "Masquer les mots de passe"}, "countryPostalCodeRequiredDesc": {"message": "Nous avons besoin de ces renseignements seulement pour calculer les taxes de vente et compléter les rapports financiers."}, "includeVAT": {"message": "Inclure des informations pour la TVA/TPS (facultatif)"}, "taxIdNumber": {"message": "Identifiant TVA/TPS"}, "taxInfoUpdated": {"message": "Informations sur les taxes mises à jour."}, "setMasterPassword": {"message": "Définir le mot de passe principal"}, "identifier": {"message": "Identifiant"}, "organizationIdentifier": {"message": "Identifiant de l'organisation"}, "ssoLogInWithOrgIdentifier": {"message": "Connectez-vous en utilisant le portail de connexion unique de votre organisation. Veuillez entrer l'identifiant de votre organisation pour commencer."}, "singleSignOnEnterOrgIdentifier": {"message": "Entrez l'identifiant SSO de votre organisation pour commencer"}, "singleSignOnEnterOrgIdentifierText": {"message": "Pour vous connecter avec votre fournisseur de SSO, entrez l'identifiant SSO de votre organisation pour commencer. Vous devrez peut-être entrer cet identifiant SSO lorsque vous vous connecterez à partir d'un nouvel appareil."}, "enterpriseSingleSignOn": {"message": "Portail de connexion unique Entreprise"}, "ssoHandOff": {"message": "Vous pouvez maintenant fermer cet onglet et continuer dans l'extension."}, "youSuccessfullyLoggedIn": {"message": "Vous vous êtes connecté avec succès"}, "thisWindowWillCloseIn5Seconds": {"message": "Cette fenêtre se fermera automatiquement dans 5 secondes"}, "youMayCloseThisWindow": {"message": "<PERSON>ous pouvez fermer cette fenêtre"}, "includeAllTeamsFeatures": {"message": "Toutes les fonctionnalités pour les équipes, plus :"}, "includeAllTeamsStarterFeatures": {"message": "Toutes les fonctionnalités d'Équipes Essentiel, plus :"}, "chooseMonthlyOrAnnualBilling": {"message": "Choisissez la facturation mensuelle ou annuelle"}, "abilityToAddMoreThanNMembers": {"message": "Possibilité d'ajouter plus de $COUNT$ membres", "placeholders": {"count": {"content": "$1", "example": "10"}}}, "includeSsoAuthentication": {"message": "Authentification SSO via SAML2.0 et OpenID Connect"}, "includeEnterprisePolicies": {"message": "Politiques de sécurité de l'entreprise"}, "ssoValidationFailed": {"message": "Échec de la validation SSO"}, "ssoIdentifierRequired": {"message": "L'identifiant de l'organisation est requis."}, "ssoIdentifier": {"message": "Identifiant SSO"}, "ssoIdentifierHintPartOne": {"message": "Fournir cet ID à vos membres pour se connecter avec SSO. Pour contourner cette étape, configurer ", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Provide this ID to your members to login with SSO. To bypass this step, set up Domain verification'"}, "unlinkSso": {"message": "Délier SSO"}, "unlinkSsoConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer le lien SSO pour cette organisation?"}, "linkSso": {"message": "Lier SSO"}, "singleOrg": {"message": "Organisation Unique"}, "singleOrgDesc": {"message": "Empêcher les utilisateurs de pouvoir rejoindre d'autres organisations."}, "singleOrgPolicyDesc": {"message": "Restreindre aux membres de rejoindre d'autres organisations. Cette politique de sécurité est nécessaire pour les organisations qui ont activé la vérification de domaine."}, "singleOrgBlockCreateMessage": {"message": "Votre organisation actuelle a une politique qui ne vous permet pas de rejoindre plus d'une organisation. Veuillez contacter les administrateurs de votre organisation ou vous inscrire à partir d'un compte Bitwarden différent."}, "singleOrgPolicyMemberWarning": {"message": "Les membres non conformes seront placés en statut révoqué jusqu'à ce qu'ils quittent toutes les autres organisations. Les administrateurs sont exemptés et peuvent restaurer les membres une fois que la conformité est respectée."}, "requireSso": {"message": "Authentification par Connexion Unique (Single Sign-On)"}, "requireSsoPolicyDesc": {"message": "Exiger que les membres se connectent avec la méthode du portail de connexion unique Entreprise."}, "prerequisite": {"message": "Prérequis"}, "requireSsoPolicyReq": {"message": "La politique d'organisation unique Entreprise doit être activée avant d'activer cette politique."}, "requireSsoPolicyReqError": {"message": "La politique \"Organisation Unique\" n'est pas activée."}, "requireSsoExemption": {"message": "Les propriétaires et les administrateurs de l'organisation sont exonérés de l'application de cette politique."}, "limitSendViews": {"message": "Limiter le nombre d'affichages"}, "limitSendViewsHint": {"message": "<PERSON>ne ne peut afficher ce Send une fois la limite atteinte.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ affichages restants", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "sendDetails": {"message": "<PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeTextToShare": {"message": "Texte à partager"}, "sendTypeFile": {"message": "<PERSON><PERSON><PERSON>"}, "sendTypeText": {"message": "Texte"}, "sendPasswordDescV3": {"message": "Ajouter un mot de passe facultatif pour que les destinataires puissent accéder à ce Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "Nouveau Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "Modifier le Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "Send créé", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "Send modifié", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletedSend": {"message": "Send supprimé", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSend": {"message": "Supp<PERSON><PERSON> le Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer définitivement ce Send ?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "Date de suppression"}, "deletionDateDescV2": {"message": "Le Send sera définitivement supprimé à cette date.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "Date d'expiration"}, "expirationDateDesc": {"message": "<PERSON>, l'accès à ce Send expirera à la date et heure spécifiées.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "maxAccessCount": {"message": "Nombre maximum d'accès"}, "disabled": {"message": "Désactivé"}, "revoked": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sendLink": {"message": "<PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "copyLink": {"message": "Copier le lien"}, "copySendLink": {"message": "Copier le lien du Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "Supprimer le mot de passe"}, "removedPassword": {"message": "Mot de passe supprimé"}, "removePasswordConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer le mot de passe ?"}, "allSends": {"message": "Tous les Sends"}, "maxAccessCountReached": {"message": "Nombre maximum d'accès atteint", "description": "This text will be displayed after a Send has been accessed the maximum amount of times."}, "pendingDeletion": {"message": "En attente de suppression"}, "hideTextByDefault": {"message": "Masquer le texte par défaut"}, "expired": {"message": "Expiré"}, "searchSends": {"message": "Rechercher dans les Sends", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendProtectedPassword": {"message": "Ce Send est protégé par un mot de passe. Veuillez saisir le mot de passe ci-dessous pour continuer.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendProtectedPasswordDontKnow": {"message": "Vous ne connaissez pas le mot de passe ? Demandez à l'expéditeur le mot de passe nécessaire pour accéder à ce Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendHiddenByDefault": {"message": "Ce Send est masqué par défaut. Vous pouvez changer sa visibilité en utilisant le bouton ci-dessous.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "downloadAttachments": {"message": "Télécharger les pièces jointes"}, "sendAccessUnavailable": {"message": "Le Send que vous essayez d'accéder n'existe pas ou n'est plus disponible.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "missingSendFile": {"message": "Le fichier associé à ce Send est introuvable.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "noSendsInList": {"message": "Il n'y a aucun Send à afficher.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "emergencyAccess": {"message": "Accès d'urgence"}, "emergencyAccessDesc": {"message": "Accordez et gérez l'accès d'urgence pour les contacts de confiance. Les contacts de confiance peuvent demander l'accès pour afficher ou reprendre le contrôle de votre compte en cas d'urgence. Consultez notre page d'aide pour plus d'informations et de détails sur le fonctionnement du partage à divulgation nulle de connaissance (zero knowledge sharing)."}, "emergencyAccessOwnerWarning": {"message": "Vous êtes propriétaire d'une ou de plusieurs organisations. Si vous autorisez la prise de contrôle à un contact d'urgence, il sera en mesure d'utiliser toutes vos autorisations de propriétaire après la prise de contrôle."}, "trustedEmergencyContacts": {"message": "Contacts d'urgence de confiance"}, "noTrustedContacts": {"message": "Vous n'avez pas encore ajouté de contacts d'urgence, invitez un contact de confiance pour commencer."}, "addEmergencyContact": {"message": "Ajouter un contact d'urgence"}, "designatedEmergencyContacts": {"message": "Désigné comme contact d'urgence"}, "noGrantedAccess": {"message": "Vous n'avez pas encore été désigné comme contact d'urgence pour qui que ce soit."}, "inviteEmergencyContact": {"message": "Inviter un contact d'urgence"}, "editEmergencyContact": {"message": "Modifier le contact d'urgence"}, "inviteEmergencyContactDesc": {"message": "Invitez un nouveau contact d'urgence en saisissant l'adresse électronique de son compte Bitwarden ci-dessous. S'il n'a pas encore de compte Bitwarden, il lui sera demandé de créer un nouveau compte."}, "emergencyAccessRecoveryInitiated": {"message": "Accès d'urgence initié"}, "emergencyAccessRecoveryApproved": {"message": "Accès d'urgence approuvé"}, "viewDesc": {"message": "<PERSON><PERSON>t afficher tous les éléments dans votre propre coffre."}, "takeover": {"message": "Prise de contrôle"}, "takeoverDesc": {"message": "Peut réinitialiser votre compte avec un nouveau mot de passe principal."}, "waitTime": {"message": "Période d'attente"}, "waitTimeDesc": {"message": "Temps nécessaire avant d'accorder l'accès automatiquement."}, "oneDay": {"message": "1 jour"}, "days": {"message": "$DAYS$ jours", "placeholders": {"days": {"content": "$1", "example": "1"}}}, "invitedUser": {"message": "Utilisateur invité."}, "acceptEmergencyAccess": {"message": "Vous avez été invité à devenir un contact d'urgence pour l'utilisateur mentionné ci-dessus. Pour accepter l'invitation, vous devez vous connecter ou créer un nouveau compte Bitwarden."}, "emergencyInviteAcceptFailed": {"message": "Impossible d'accepter l'invitation. <PERSON><PERSON><PERSON> à l'utilisateur d'envoyer une nouvelle invitation."}, "emergencyInviteAcceptFailedShort": {"message": "Impossible d'accepter l'invitation. $DESCRIPTION$", "placeholders": {"description": {"content": "$1", "example": "You must set up 2FA on your user account before you can join this organization."}}}, "emergencyInviteAcceptedDesc": {"message": "Vous pourrez accéder aux options d'urgence pour cet utilisateur après que votre identité aura été confirmée. Nous vous enverrons un courriel lorsque cela se produira."}, "requestAccess": {"message": "<PERSON><PERSON><PERSON> l’accès"}, "requestAccessConfirmation": {"message": "Êtes-vous sûr de vouloir demander un accès d'urgence ? L'accès vous sera accordé après $WAITTIME$ jour(s) ou dès que l'utilisateur approuve manuellement la demande.", "placeholders": {"waittime": {"content": "$1", "example": "1"}}}, "requestSent": {"message": "Accès d'urgence demandé pour $USER$. Nous vous informerons par courriel lorsqu'il sera possible de continuer.", "placeholders": {"user": {"content": "$1", "example": "<PERSON>"}}}, "approve": {"message": "Accepter"}, "reject": {"message": "Refuser"}, "approveAccessConfirmation": {"message": "Êtes-vous sûr de vouloir approuver l'accès d'urgence ? Cela accordera à $USER$ les permissions suivantes sur votre compte: $ACTION$.", "placeholders": {"user": {"content": "$1", "example": "<PERSON>"}, "action": {"content": "$2", "example": "View"}}}, "emergencyApproved": {"message": "Accès d'urgence approuvé."}, "emergencyRejected": {"message": "Accès d'urgence refusé."}, "grantorDetailsNotFound": {"message": "Détails de la source non trouvés"}, "passwordResetFor": {"message": "Mot de passe réinitialisé pour $USER$. Vous pouvez maintenant vous connecter en utilisant le nouveau mot de passe.", "placeholders": {"user": {"content": "$1", "example": "<PERSON>"}}}, "organizationDataOwnership": {"message": "Forcer la propriété des données de l'organisation"}, "personalOwnership": {"message": "Supp<PERSON>er le coffre individuel"}, "personalOwnershipPolicyDesc": {"message": "Exiger que les utilisateurs enregistrent des éléments du coffre dans une organisation en retirant l'option de propriété individuelle."}, "personalOwnershipExemption": {"message": "Les propriétaires et les administrateurs de l'organisation sont exonérés de l'application de cette politique."}, "personalOwnershipSubmitError": {"message": "En raison d'une politique de sécurité Entreprise, il vous est interdit d'enregistrer des éléments dans votre coffre personnel. Sélectionnez une organisation dans l'option Propriété et choisissez parmi les collections disponibles."}, "disableSend": {"message": "Supp<PERSON><PERSON> le Send"}, "disableSendPolicyDesc": {"message": "Ne pas autoriser les utilisateurs à créer ou modifier un Send Bitwarden. La suppression d'un envoi existant est toujours autorisée.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disableSendExemption": {"message": "Les membres de l'organisation qui peuvent gérer les politiques de sécurité de l'organisation sont exemptés de l'application de cette politique de sécurité."}, "sendDisabled": {"message": "Send supprimé", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "En raison d'une politique de sécurité Entreprise, vous ne pouvez que supprimer un Send existant.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendOptions": {"message": "Options Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendOptionsPolicyDesc": {"message": "Définir les options pour la création et la modification des Sends.", "description": "'Sends' is a plural noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendOptionsExemption": {"message": "Les membres de l'organisation qui peuvent gérer les politiques de sécurité de l'organisation sont exemptés de l'application de cette politique de sécurité."}, "disableHideEmail": {"message": "Toujours afficher l'adresse électronique du membre avec les destinataires lors de la création ou de l'édition d'un Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "modifiedPolicyId": {"message": "Politique $ID$ modifiée.", "placeholders": {"id": {"content": "$1", "example": "Master Password"}}}, "planPrice": {"message": "Prix du forfait"}, "estimatedTax": {"message": "Taxe estimée"}, "custom": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "customDesc": {"message": "Accorder des autorisations personnalisées aux membres"}, "customDescNonEnterpriseStart": {"message": "Rôles personnalisés est une ", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Custom roles is an enterprise feature. Contact our support team to upgrade your subscription'"}, "customDescNonEnterpriseLink": {"message": "fonctionnalité d'entreprise", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Custom roles is an enterprise feature. Contact our support team to upgrade your subscription'"}, "customDescNonEnterpriseEnd": {"message": ". Contactez notre équipe de support pour mettre à jour votre abonnement", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Custom roles is an enterprise feature. Contact our support team to upgrade your subscription'"}, "customNonEnterpriseError": {"message": "Pour activer les autorisations personnalisées, l'organisation doit être sur un plan Entreprise 2020."}, "permissions": {"message": "Permissions"}, "permission": {"message": "Autorisation"}, "accessEventLogs": {"message": "Accéder aux journaux d'événements"}, "accessImportExport": {"message": "Accéder aux options d'import et d'export"}, "accessReports": {"message": "Accéder aux rapports"}, "missingPermissions": {"message": "Vous n’avez pas les permissions requises pour accomplir cette action."}, "manageAllCollections": {"message": "<PERSON><PERSON><PERSON> toutes les collections"}, "createNewCollections": {"message": "Créer de nouvelles collections"}, "editAnyCollection": {"message": "Modifier n'importe quelle collection"}, "deleteAnyCollection": {"message": "Supprimer n'importe quelle collection"}, "manageGroups": {"message": "G<PERSON>rer les groupes"}, "managePolicies": {"message": "<PERSON><PERSON>rer les politiques de sécurité"}, "manageSso": {"message": "<PERSON><PERSON><PERSON> le SSO"}, "manageUsers": {"message": "<PERSON><PERSON><PERSON> les utilisateurs"}, "manageAccountRecovery": {"message": "Gérer la récupération de compte"}, "disableRequiredError": {"message": "<PERSON><PERSON> de désactiver cette politique, vous devez préalablement manuellement désactiver la politique \"$POLICYNAME$\".", "placeholders": {"policyName": {"content": "$1", "example": "Single Sign-On Authentication"}}}, "personalOwnershipPolicyInEffect": {"message": "Une politique d'organisation affecte vos options de propriété."}, "personalOwnershipPolicyInEffectImports": {"message": "Une politique d'organisation a désactivé l'import d'éléments dans votre coffre personnel."}, "personalOwnershipCheckboxDesc": {"message": "Supprimer la propriété individuelle des utilisateurs de l'organisation"}, "send": {"message": "Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendAccessTaglineProductDesc": {"message": "Les Send Bitwarden permettent d'envoyer des informations sensibles et temporaires à d'autres, facilement et en toute sécurité.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendAccessTaglineLearnMore": {"message": "Apprenez-en plus sur", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read '**Learn more about** Bitwarden Send or sign up to try it today.'"}, "sendVaultCardProductDesc": {"message": "Partagez du texte ou des fichiers directement avec n'importe qui."}, "sendVaultCardLearnMore": {"message": "Apprenez-en plus", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read '**Learn more**, see how it works, or try it now. '"}, "sendVaultCardSee": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Learn more, **see** how it works, or try it now.'"}, "sendVaultCardHowItWorks": {"message": "comment cela fonctionne", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Learn more, see **how it works**, or try it now.'"}, "sendVaultCardOr": {"message": "ou", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Learn more, see how it works, **or** try it now.'"}, "developmentDevOpsAndITTeamsChooseBWSecret": {"message": "Les équipes de développement, DevOps et IT choisissent Bitwarden Secrets Manager pour gérer et déployer en toute sécurité leurs infrastructures et leurs secrets de machine."}, "centralizeSecretsManagement": {"message": "Centraliser la gestion des secrets."}, "centralizeSecretsManagementDescription": {"message": "<PERSON><PERSON> et gérez les secrets en toute sécurité à un seul endroit pour éviter un étalement secret à travers votre organisation."}, "preventSecretLeaks": {"message": "Empê<PERSON> les fuites de secrets."}, "preventSecretLeaksDescription": {"message": "Protégez les secrets avec le chiffrement de bout en bout. Plus de codage dur des secrets ou de partage via des fichiers .env."}, "enhanceDeveloperProductivity": {"message": "Améliorer la productivité des développeurs."}, "enhanceDeveloperProductivityDescription": {"message": "Récupération et déploiement programmatiques des secrets lors de l'exécution, afin que les développeurs puissent se concentrer sur ce qui compte le plus, comme l'amélioration de la qualité du code."}, "strengthenBusinessSecurity": {"message": "Renforcer la sécurité de votre entreprise."}, "strengthenBusinessSecurityDescription": {"message": "Gardez un contrôle rigoureux sur l'accès machine et humain aux secrets grâce aux intégrations SSO, aux journaux d'événements et à la rotation d'accès."}, "tryItNow": {"message": "Essayez-le maintenant"}, "sendRequest": {"message": "Envoyer la requête"}, "addANote": {"message": "Ajouter une note"}, "bitwardenSecretsManager": {"message": "Secrets Manager <PERSON>"}, "moreProductsFromBitwarden": {"message": "Plus de produits de Bitwarden"}, "requestAccessToSecretsManager": {"message": "<PERSON><PERSON><PERSON> l'accès au Secrets Manager"}, "youNeedApprovalFromYourAdminToTrySecretsManager": {"message": "Vous avez besoin de l'approbation de votre administrateur pour essayer Secrets Manager."}, "smAccessRequestEmailSent": {"message": "<PERSON><PERSON><PERSON> de demande d'accès au Secrets Manager envoyé aux administrateurs."}, "requestAccessSMDefaultEmailContent": {"message": "Bonjour, \n\nJe demande un abonnement à Secrets Manager de Bitwarden pour mon équipe. Votre support serait très apprécié!\n\nSecrets Manager de Bitwarden est une solution de gestion de chiffrement bout en bout des secrets pour conserver, partager et déployer des identifiants d'appareils comme des clés d'API, des mots de passe de bases de données et des certificats d'authentification de façon sécuritaire.\n\nSecrets Manager nous aidera à :\n\n- Améliorer notre sécurité\n- Accélérer nos opérations\n- Prévenir les fuites coûteuses de nos secrets\n\nPour demander un essai gratuit pour notre équipe, veuillez contacter Bitwarden.\n\nMerci pour votre aide!"}, "giveMembersAccess": {"message": "<PERSON><PERSON> accès aux membres :"}, "viewAndSelectTheMembers": {"message": "voir et sélectionner les membres que vous voulez donner accès à Secrets Manager."}, "openYourOrganizations": {"message": "Ouvrir votre organisation"}, "usingTheMenuSelect": {"message": "À l'aide du menu, sélectionnez"}, "toGrantAccessToSelectedMembers": {"message": "pour donner accès aux membres sélectionnés."}, "sendVaultCardTryItNow": {"message": "essay<PERSON><PERSON>le maintenant", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Learn more, see how it works, or **try it now**.'"}, "sendAccessTaglineOr": {"message": "ou", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Learn more about Bitwarden Send **or** sign up to try it today.'"}, "sendAccessTaglineSignUp": {"message": "inscrivez-vous", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Learn more about Bitwarden Send or **sign up** to try it today.'"}, "sendAccessTaglineTryToday": {"message": "pour l'essayer dès aujourd'hui.", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read '<PERSON>rn more about Bitwarden Send or sign up to **try it today.**'"}, "sendAccessCreatorIdentifier": {"message": "Le membre Bitwarden $USER_IDENTIFIER$ a partagé ce qui suit avec vous", "placeholders": {"user_identifier": {"content": "$1", "example": "An email address"}}}, "viewSend": {"message": "Voir le Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "viewSendHiddenEmailWarning": {"message": "L'utilisateur de Bitwarden qui a créé ce Send a choisi de masquer son adresse électronique. Vous devriez vous assurer que vous faites confiance à la source de ce lien avant d'utiliser ou de télécharger son contenu.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDateIsInvalid": {"message": "La date d'expiration indiquée n'est pas valide."}, "deletionDateIsInvalid": {"message": "La date de suppression indiquée n'est pas valide."}, "expirationDateAndTimeRequired": {"message": "Une date et une heure d'expiration sont requises."}, "deletionDateAndTimeRequired": {"message": "Une date et une heure de suppression sont requises."}, "dateParsingError": {"message": "Une erreur s'est produite lors de l'enregistrement de vos dates de suppression et d'expiration."}, "hideYourEmail": {"message": "Masquer mon adresse courriel aux destinataires."}, "webAuthnFallbackMsg": {"message": "Pour vérifier votre 2FA, veuillez cliquer sur le bouton ci-dessous."}, "webAuthnAuthenticate": {"message": "Authentifier WebAuthn"}, "readSecurityKey": {"message": "Lire la clé de sécurité"}, "awaitingSecurityKeyInteraction": {"message": "En attente de l'interaction de la clé de sécurité..."}, "webAuthnNotSupported": {"message": "WebAuthn n'est pas pris en charge dans ce navigateur."}, "webAuthnSuccess": {"message": "WebAuthn vérifié avec succès ! Vous pouvez fermer cet onglet."}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Votre nouveau mot de passe ne peut être le même que votre mot de passe actuel."}, "hintEqualsPassword": {"message": "Votre indice de mot de passe ne peut pas être identique à votre mot de passe."}, "enrollAccountRecovery": {"message": "S'inscrire à la récupération du compte"}, "enrolledAccountRecovery": {"message": "Inscrit à la récupération du compte"}, "withdrawAccountRecovery": {"message": "Retirer de la récupération du compte"}, "enrollPasswordResetSuccess": {"message": "Inscription réussie !"}, "withdrawPasswordResetSuccess": {"message": "Retrait réussi !"}, "eventEnrollAccountRecovery": {"message": "L'utilisateur $ID$ est inscrit à la récupération du compte.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "eventWithdrawAccountRecovery": {"message": "L'utilisateur $ID$ a été retiré de la récupération du compte.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "eventAdminPasswordReset": {"message": "Réinitialisation du mot de passe principal pour l'utilisateur $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "eventResetSsoLink": {"message": "Réinitialiser le lien SSO pour l'utilisateur $ID$", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "firstSsoLogin": {"message": "$ID$ s'est connecté avec le SSO pour la première fois", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "resetPassword": {"message": "Réinitialiser le mot de passe"}, "resetPasswordLoggedOutWarning": {"message": "En poursuivant, $NAME$ sera déconnecté de sa session actuelle, ce qui l'obligera à se reconnecter. Les sessions actives sur d'autres appareils peuvent rester actives pendant encore une heure.", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}}}, "emergencyAccessLoggedOutWarning": {"message": "En poursuivant, $NAME$ sera déconnecté de sa session actuelle, ce qui l'obligera à se reconnecter. Les sessions actives sur d'autres appareils peuvent rester actives pendant encore une heure.", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}}}, "thisUser": {"message": "cet utilisateur"}, "resetPasswordMasterPasswordPolicyInEffect": {"message": "Une ou plusieurs politiques de sécurité de l'organisation exigent que votre mot de passe principal réponde aux exigences suivantes :"}, "changePasswordDelegationMasterPasswordPolicyInEffect": {"message": "Une ou plusieurs politiques de sécurité de l'organisation exigent que votre mot de passe principal réponde aux exigences suivantes :"}, "resetPasswordSuccess": {"message": "Mot de passe réinitialisé avec succès !"}, "resetPasswordEnrollmentWarning": {"message": "L'inscription permettra aux administrateurs de l'organisation de changer votre mot de passe principal"}, "accountRecoveryPolicy": {"message": "Administration de récupération du compte"}, "accountRecoveryPolicyDesc": {"message": "Se basant sur la méthode de chiffrement, récupère les comptes lorsque des mots de passe principaux ou des appareils de confiance sont oubliés ou perdus."}, "accountRecoveryPolicyWarning": {"message": "Les comptes existants avec les mots de passe principaux exigeront que les membres s'inscrivent d'eux-mêmes avant que les administrateurs puissent récupérer leurs comptes. L'inscription automatique activera la récupération du compte pour les nouveaux membres."}, "accountRecoverySingleOrgRequirementDesc": {"message": "La politique de sécurité d'organisation unique Entreprise doit être activée avant d'activer cette politique de sécurité."}, "resetPasswordPolicyAutoEnroll": {"message": "Inscription automatique"}, "resetPasswordPolicyAutoEnrollCheckbox": {"message": "Exiger que les nouveaux utilisateurs soient inscrits automatiquement"}, "resetPasswordAutoEnrollInviteWarning": {"message": "Cette organisation dispose d'une politique d'entreprise qui vous inscrira automatiquement à la réinitialisation du mot de passe. L'inscription permettra aux administrateurs de l'organisation de changer votre mot de passe principal."}, "resetPasswordOrgKeysError": {"message": "Erreur lors du traitement de votre demande : \"Organization Keys response is null\""}, "resetPasswordDetailsError": {"message": "Erreur lors du traitement de votre demande : \"Reset Password Details response is null\""}, "trashCleanupWarning": {"message": "Les éléments présents dans la corbeille depuis plus de 30 jours seront automatiquement supprimés."}, "trashCleanupWarningSelfHosted": {"message": "Les éléments présents dans la corbeille depuis un moment seront automatiquement supprimés."}, "passwordPrompt": {"message": "<PERSON><PERSON><PERSON><PERSON> le mot de passe principal"}, "passwordConfirmation": {"message": "Confirmation du mot de passe principal"}, "passwordConfirmationDesc": {"message": "Cette action est protégée. Pour continuer, veuillez saisir à nouveau votre mot de passe principal pour vérifier votre identité."}, "reinviteSelected": {"message": "<PERSON><PERSON>er les invitations"}, "resendNotification": {"message": "Renvoyer la notification"}, "noSelectedUsersApplicable": {"message": "Cette action n'est applicable à aucun des utilisateurs sélectionnés."}, "removeUsersWarning": {"message": "Êtes-vous sûr de vouloir supprimer les utilisateurs suivants ? Le processus peut prendre quelques secondes pour s'achever et ne peut être interrompu ou annulé."}, "removeOrgUsersConfirmation": {"message": "Lorsque les membres sont supprimés, ils n'ont plus accès aux données de l'organisation et cette action est irréversible. Pour réintégrer le membre dans l'organisation, il faut l'inviter et l'intégrer à nouveau. Le processus peut prendre quelques secondes pour s'achever et ne peut pas être interrompu ou annulé."}, "revokeUsersWarning": {"message": "Lorsque les membres sont révoqués, ils n'ont plus accès aux données de l'organisation. Pour restaurer rapidement l'accès des membres, allez dans l'onglet Révoqué. Le processus peut prendre quelques secondes et ne peut pas être interrompu ou annulé."}, "theme": {"message": "Thème"}, "themeDesc": {"message": "Choisissez un thème pour votre coffre web."}, "themeSystem": {"message": "Utiliser le thème du système"}, "themeDark": {"message": "Sombre"}, "themeLight": {"message": "<PERSON>"}, "confirmSelected": {"message": "Confirmer la sélection"}, "bulkConfirmStatus": {"message": "Statut de l'action de masse"}, "bulkConfirmMessage": {"message": "Confirm<PERSON> avec succès."}, "bulkReinviteMessage": {"message": "Réinvité avec succès."}, "bulkRemovedMessage": {"message": "Supprimé avec succès"}, "bulkRevokedMessage": {"message": "Accès à l'organisation révoqué avec succès"}, "bulkRestoredMessage": {"message": "Accès à l'organisation restauré avec succès"}, "bulkFilteredMessage": {"message": "Exclu, non applicable pour cette action."}, "nonCompliantMembersTitle": {"message": "Membres non conformes"}, "nonCompliantMembersError": {"message": "Les membres qui ne sont pas conformes à la politique d'organisation unique ou de connexion en deux étapes ne peuvent pas être restaurés jusqu'à ce qu'ils adhèrent aux exigences de la politique"}, "fingerprint": {"message": "Empreinte"}, "fingerprintPhrase": {"message": "Phrase d'empreinte :"}, "error": {"message": "<PERSON><PERSON><PERSON>"}, "decryptionError": {"message": "<PERSON><PERSON><PERSON> <PERSON> d<PERSON>chiff<PERSON>"}, "couldNotDecryptVaultItemsBelow": {"message": "Bitwarden n'a pas pu déchiffrer le(s) élément(s) du coffre listé(s) ci-dessous."}, "contactCSToAvoidDataLossPart1": {"message": "Contacter Customer Success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "pour éviter des pertes de données supplémentaires.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "accountRecoveryManageUsers": {"message": "G<PERSON>rer les utilisateurs exige également d'avoir l'autorisation de gérer la restauration de compte"}, "setupProvider": {"message": "Configuration du fournisseur"}, "setupProviderLoginDesc": {"message": "Vous avez été invité à configurer un nouveau fournisseur. <PERSON><PERSON> continuer, vous devez vous connecter ou créer un nouveau compte Bitwarden."}, "setupProviderDesc": {"message": "Veuillez entrer les détails ci-dessous pour finaliser la configuration du fournisseur. Contactez le support client si vous avez des questions."}, "providerName": {"message": "Nom du fournisseur"}, "providerSetup": {"message": "Le fournisseur a été configuré."}, "clients": {"message": "Clients"}, "client": {"message": "Client", "description": "This is used as a table header to describe which client application created an event log."}, "providerAdmin": {"message": "Administrateur du fournisseur"}, "providerAdminDesc": {"message": "L'utilisateur à l'accès le plus élevé. Il peut gérer tous les aspects de votre fournisseur ainsi que gérer les organisations clientes et y accéder."}, "serviceUser": {"message": "Utilisateur de service"}, "serviceUserDesc": {"message": "Les utilisateurs de service peuvent gérer toutes les organisations clientes et y accéder."}, "providerInviteUserDesc": {"message": "Invitez un nouvel utilisateur à votre fournisseur en saisissant l'adresse électronique de son compte Bitwarden ci-dessous. S'il n'a pas encore de compte Bitwarden, il lui sera demandé de créer un nouveau compte."}, "joinProvider": {"message": "Rejoindre le fournisseur"}, "joinProviderDesc": {"message": "Vous avez été invité à rejoindre le fournisseur listé ci-dessus. Pour accepter l'invitation, vous devez vous connecter ou créer un nouveau compte Bitwarden."}, "providerInviteAcceptFailed": {"message": "Impossible d'accepter l'invitation. Demandez à un administrateur du fournisseur d'envoyer une nouvelle invitation."}, "providerInviteAcceptedDesc": {"message": "Vous pourrez accéder à ce fournisseur dès qu'un administrateur aura confirmé votre adhésion. Nous vous enverrons un courriel lorsque cela se produira."}, "providerUsersNeedConfirmed": {"message": "Il y a des utilisateurs qui ont accepté leur invitation, mais qui doivent encore être confirmés. Les utilisateurs n'auront pas accès au fournisseur tant qu'ils n'auront pas été confirmés."}, "provider": {"message": "Fournisseur"}, "newClientOrganization": {"message": "Nouvelle organisation cliente"}, "newClientOrganizationDesc": {"message": "Créez une nouvelle organisation cliente qui sera associée avec vous en tant que fournisseur. Vous pourrez accéder à cette organisation et la gérer."}, "newClient": {"message": "Nouveau client"}, "addExistingOrganization": {"message": "Ajouter une organisation existante"}, "addNewOrganization": {"message": "Ajouter une nouvelle organisation"}, "myProvider": {"message": "<PERSON> <PERSON>"}, "addOrganizationConfirmation": {"message": "Êtes-vous sûr de vouloir ajouter $ORGANIZATION$ comme cliente de $PROVIDER$?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}, "provider": {"content": "$2", "example": "My Provider Name"}}}, "organizationJoinedProvider": {"message": "L'organisation a bien été ajoutée au fournisseur"}, "accessingUsingProvider": {"message": "Accès à l'organisation en utilisant le fournisseur $PROVIDER$", "placeholders": {"provider": {"content": "$1", "example": "My Provider Name"}}}, "providerIsDisabled": {"message": "Le fournisseur est désactivé."}, "providerUpdated": {"message": "Fournisseur mis à jour"}, "yourProviderIs": {"message": "Votre fournisseur est $PROVIDER$. Ils ont des privilèges d'administration et de facturation pour votre organisation.", "placeholders": {"provider": {"content": "$1", "example": "My Provider Name"}}}, "detachedOrganization": {"message": "L'organisation $ORGANIZATION$ a été détachée de votre fournisseur.", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "detachOrganizationConfirmation": {"message": "Êtes-vous sûr de vouloir détacher cette organisation ? L'organisation continuera d'exister mais ne sera plus gérée par le fournisseur."}, "add": {"message": "Ajouter"}, "updatedMasterPassword": {"message": "Mot de passe principal enregistré"}, "updateMasterPassword": {"message": "Mettre à jour le mot de passe principal"}, "updateMasterPasswordWarning": {"message": "Votre mot de passe principal a été récemment changé par un administrateur de votre organisation. Pour pouvoir accéder au coffre, vous devez mettre à jour votre mot de passe principal maintenant. En poursuivant, vous serez déconnecté de votre session actuelle et vous devrez vous reconnecter. Les sessions actives sur d'autres appareils peuvent rester actives pendant encore une heure."}, "masterPasswordInvalidWarning": {"message": "Votre mot de passe principal ne répond pas aux exigences de politique de sécurité de cette organisation. Pour accéder au coffre, vous devez mettre à jour votre mot de passe principal dès maintenant. En poursuivant, vous serez déconnecté de votre session actuelle et vous devrez vous reconnecter. Les sessions actives sur d'autres appareils peuver rester actives pendant encore une heure."}, "updateWeakMasterPasswordWarning": {"message": "Votre mot de passe principal ne répond pas aux exigences des politiques de sécurité de cette organisation. Pour pouvoir accéder au coffre, vous devez mettre à jour votre mot de passe principal dès maintenant. En poursuivant, vous serez déconnecté de votre session actuelle et vous devrez vous reconnecter. Les sessions actives sur d'autres appareils peuver rester actives pendant encore une heure."}, "automaticAppLogin": {"message": "Se connecter automatiquement aux utilisateurs pour les applications autorisées"}, "automaticAppLoginDesc": {"message": "Les formulaires de connexion seront automatiquement remplis et soumis pour les applications lancées à partir de votre fournisseur d'identité configuré."}, "automaticAppLoginIdpHostLabel": {"message": "Hôte du fournisseur d'identité"}, "automaticAppLoginIdpHostDesc": {"message": "Entrez l'URL d'hôte de votre fournisseur d'identité. Entrez plusieurs URL en séparant avec une virgule."}, "tdeDisabledMasterPasswordRequired": {"message": "Votre organisation a mis à jour vos options de déchiffrement. Veuillez définir un mot de passe principal pour accéder à votre coffre."}, "maximumVaultTimeout": {"message": "<PERSON><PERSON><PERSON> d'expiration du coffre"}, "maximumVaultTimeoutDesc": {"message": "Configurer un délai maximum d'expiration du coffre pour tous les utilisateurs."}, "maximumVaultTimeoutLabel": {"message": "<PERSON><PERSON><PERSON> maximum d'expiration du coffre"}, "invalidMaximumVaultTimeout": {"message": "<PERSON><PERSON><PERSON> maximum d'expiration du coffre invalide."}, "hours": {"message": "<PERSON><PERSON>"}, "minutes": {"message": "Minutes"}, "vaultTimeoutPolicyInEffect": {"message": "Les politiques de sécurité de votre organisation ont défini le délai d'expiration de votre coffre à $HOURS$ heure(s) et $MINUTES$ minute(s) maximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "$HOURS$ heure(s) et $MINUTES$ minute(s) maximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Les politiques de sécurité de votre organisation affectent le délai d'expiration de votre coffre. Le délai autorisé d'expiration du coffre est de $HOURS$ heure(s) et $MINUTES$ minute(s) maximum. L'action après délai d'expiration de votre coffre est fixée à $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Les politiques de sécurité de votre organisation ont défini l'action après délai d'expiration de votre coffre à $ACTION$.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutToLarge": {"message": "Le délai d'expiration de votre coffre-fort dépasse les restrictions définies par votre organisation."}, "vaultCustomTimeoutMinimum": {"message": "Le délai d'expiration personnalisé minimum est de 1 minute."}, "vaultTimeoutRangeError": {"message": "Le délai de mise en veille du coffre n'est pas dans l'intervalle de temps autorisé."}, "disablePersonalVaultExport": {"message": "Supprimer l'exportation individuelle du coffre"}, "disablePersonalVaultExportDescription": {"message": "Ne pas autoriser les membres à exporter des données de leur coffre individuel."}, "vaultExportDisabled": {"message": "Export du coffre supprimé"}, "personalVaultExportPolicyInEffect": {"message": "Une ou plusieurs politiques de sécurité de l'organisation vous empêchent d'exporter votre coffre individuel."}, "activateAutofill": {"message": "Activer la saisie automatique"}, "activateAutofillPolicyDesc": {"message": "Activer le paramètre de saisie automatique au chargement de la page sur l'extension du navigateur pour tous les membres existants et nouveaux."}, "experimentalFeature": {"message": "Les sites web compromis ou non fiables peuvent exploiter la saisie automatique au chargement de la page."}, "learnMoreAboutAutofill": {"message": "En savoir plus sur la saisie automatique"}, "selectType": {"message": "Sélectionnez le type de SSO"}, "type": {"message": "Type"}, "openIdConnectConfig": {"message": "Configuration OpenID Connect"}, "samlSpConfig": {"message": "Configuration SAML Service Provider"}, "samlIdpConfig": {"message": "Configuration SAML Identity Provider"}, "callbackPath": {"message": "URL de callback"}, "signedOutCallbackPath": {"message": "Chemin de \"callback\" lorsque déconnecté"}, "authority": {"message": "Autorité"}, "clientId": {"message": "Client ID"}, "clientSecret": {"message": "Client Secret"}, "metadataAddress": {"message": "Adresse des métadonnées"}, "oidcRedirectBehavior": {"message": "Comportement de la redirection OIDC"}, "getClaimsFromUserInfoEndpoint": {"message": "Récupérer les claims depuis l'endpoint d'informations utilisateur (User Info Endpoint)"}, "additionalScopes": {"message": "<PERSON><PERSON><PERSON> person<PERSON>"}, "additionalUserIdClaimTypes": {"message": "Types de claim personnalisés pour l'identifiant de l'utilisateur"}, "additionalEmailClaimTypes": {"message": "Types de réclamation par courriel"}, "additionalNameClaimTypes": {"message": "Types de claim personnalisés pour le nom"}, "acrValues": {"message": "Valeurs Authentication Context Class Reference demandées"}, "expectedReturnAcrValue": {"message": "Valeur attendue pour le claim \"acr\" dans la réponse"}, "spEntityId": {"message": "ID de l'entité du Service Provider"}, "spMetadataUrl": {"message": "SAML 2.0 Metadata URL"}, "spAcsUrl": {"message": "Assertion Consumer Service (ACS) URL"}, "spNameIdFormat": {"message": "Name ID Format"}, "spOutboundSigningAlgorithm": {"message": "Algorithme de signature sortant"}, "spSigningBehavior": {"message": "Comportement de la signature"}, "spMinIncomingSigningAlgorithm": {"message": "Algorithme de signature entrant minimal"}, "spWantAssertionsSigned": {"message": "Exiger des assertions signées"}, "spValidateCertificates": {"message": "Vérifier les certificats"}, "spUniqueEntityId": {"message": "Définir une ID unique à l'entité SP"}, "spUniqueEntityIdDesc": {"message": "Générer un identifiant qui est unique à votre organisation"}, "idpEntityId": {"message": "ID de l'entité"}, "idpBindingType": {"message": "Type de liaison"}, "idpSingleSignOnServiceUrl": {"message": "URL du service de connexion unique (SSO)"}, "idpSingleLogoutServiceUrl": {"message": "URL du service de déconnexion unique"}, "idpX509PublicCert": {"message": "Certificat public X.509"}, "idpOutboundSigningAlgorithm": {"message": "Algorithme de signature sortant"}, "idpAllowUnsolicitedAuthnResponse": {"message": "Autoriser les réponses d'authentification non sollicitées"}, "idpAllowOutboundLogoutRequests": {"message": "Autoriser les demandes de déconnexion sortantes"}, "idpSignAuthenticationRequests": {"message": "Signer les demandes d'authentification"}, "ssoSettingsSaved": {"message": "La configuration SSO a été enregistrée."}, "sponsoredFamilies": {"message": "Bitwarden Familles gratuit"}, "sponsoredBitwardenFamilies": {"message": "Familles parrainées"}, "noSponsoredFamiliesMessage": {"message": "Aucune famille par<PERSON>"}, "nosponsoredFamiliesDetails": {"message": "Les abonnements des familles non membres parrainées apparaîtront ici"}, "sponsorshipFreeBitwardenFamilies": {"message": "Les membres de votre organisation sont éligibles pour les Familles Gratuites de Bitwarden. Vous pouvez parrainer Familles Gratuites de Bitwarden pour les employés qui ne sont pas membres de votre organisation Bitwarden. Parrainer un non-membre nécessite une licence disponible au sein de votre organisation."}, "sponsoredFamiliesRemoveActiveSponsorship": {"message": "Lorsque vous supprimez un parrainage actif, une licence au sein de votre organisation sera disponible après la date de renouvellement de l'organisation parrainée."}, "sponsoredFamiliesEligible": {"message": "Vous et votre famille êtes éligibles à Bitwarden Familles gratuitement. Réclamez-le avec votre courriel personnel pour sécuriser vos données, même lorsque vous n'êtes pas au travail."}, "sponsoredFamiliesEligibleCard": {"message": "Obtenez votre abonnement Bitwarden Familles gratuit aujourd'hui pour garder vos données en sécurité même lorsque vous n'êtes pas au travail."}, "sponsoredFamiliesIncludeMessage": {"message": "L'abonnement Bitwarden Familles inclut"}, "sponsoredFamiliesPremiumAccess": {"message": "Accès Premium jusqu'à 6 utilisateurs"}, "sponsoredFamiliesSharedCollectionsForFamilyMembers": {"message": "Collections partagées pour les membres de la famille"}, "memberFamilies": {"message": "Familles de membre"}, "noMemberFamilies": {"message": "Familles de non-membre"}, "noMemberFamiliesDescription": {"message": "Les membres qui ont réclamé des plans familiaux s'afficheront ici"}, "membersWithSponsoredFamilies": {"message": "Les membres de votre organisation sont éligibles à l'abonnement aux Familles Gratuites de Bitwarden. Vous pouvez voir ici les membres qui ont parrainé une organisation Familles."}, "organizationHasMemberMessage": {"message": "Un parrainage ne peut pas être envoyé à $EMAIL$ car il est membre de votre organisation.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "badToken": {"message": "Le lien n'est plus valide. Merci de demander à votre parrain de renvoyer l'offre."}, "reclaimedFreePlan": {"message": "Abonnement gratuit récupéré"}, "redeem": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sponsoredFamiliesSelectOffer": {"message": "Sélectionnez l'organisation que vous souhaiteriez voir parrainée"}, "familiesSponsoringOrgSelect": {"message": "Quelle offre Familles gratuite aimeriez-vous obtenir ?"}, "sponsoredFamiliesEmail": {"message": "Saisissez votre courriel personnel pour réclamer Bitwarden Familles"}, "sponsoredFamiliesLeaveCopy": {"message": "Si vous supprimez une offre ou si vous n'êtes plus membre de l'organisation de parrainage, votre parrainage Familles expirera à la prochaine date de renouvellement."}, "acceptBitwardenFamiliesHelp": {"message": "Acceptez l'offre pour une organisation existante ou créer une nouvelle organisation Familles."}, "setupSponsoredFamiliesLoginDesc": {"message": "Il vous a été offert un plan organisation Bitwarden \"Families\" gratuit. <PERSON>ur continuer, vous devez vous connecter au compte qui a reçu l'offre."}, "sponsoredFamiliesAcceptFailed": {"message": "Impossible d'accepter l'offre. Veuillez renvoyer le courriel de l'offre depuis votre compte Entreprise et réessayer."}, "sponsoredFamiliesAcceptFailedShort": {"message": "Impossible d'accepter l'offre. $DESCRIPTION$", "placeholders": {"description": {"content": "$1", "example": "You must have at least one existing Families organization."}}}, "sponsoredFamiliesOffer": {"message": "Accepter l'offre Bitwarden Familles gratuite"}, "sponsoredFamiliesOfferRedeemed": {"message": "L'offre gratuite de Bitwarden Familles a été récupérée avec succès"}, "redeemed": {"message": "Obtenue"}, "redeemedAccount": {"message": "Compte <PERSON>"}, "revokeAccountMessage": {"message": "Révoquer le compte $NAME$", "placeholders": {"name": {"content": "$1", "example": "My Sponsorship Name"}}}, "resendEmailLabel": {"message": "Renvoyer le courriel de parrainage au parrainage $NAME$", "placeholders": {"name": {"content": "$1", "example": "My Sponsorship Name"}}}, "freeFamiliesPlan": {"message": "Abonnement gratuit à Bitwarden Familles"}, "redeemNow": {"message": "Obtenir maintenant"}, "recipient": {"message": "<PERSON><PERSON><PERSON>"}, "removeSponsorship": {"message": "Supp<PERSON>er le parrainage"}, "removeSponsorshipConfirmation": {"message": "Après avoir supprimé un parrainage, vous serez responsable de cet abonnement et des factures afférentes. Êtes-vous sûr de vouloir continuer ?"}, "sponsorshipCreated": {"message": "Parrainage c<PERSON>"}, "emailSent": {"message": "<PERSON><PERSON><PERSON> envoy<PERSON>"}, "removeSponsorshipSuccess": {"message": "Parrainage supprimé"}, "ssoKeyConnectorError": {"message": "Erreur de Connecteur de Clé : assurez-vous que le Connecteur de Clé est disponible et qu'il fonctionne correctement."}, "keyConnectorUrl": {"message": "URL de Key Connector"}, "sendVerificationCode": {"message": "Envoyer un code de vérification à votre adresse email"}, "sendCode": {"message": "Envoyer le code"}, "codeSent": {"message": "Code envoyé"}, "verificationCode": {"message": "Code de vérification"}, "confirmIdentity": {"message": "Confirmez votre identité pour continuer."}, "verificationCodeRequired": {"message": "Le code de vérification est requis."}, "webauthnCancelOrTimeout": {"message": "L'authentification a été annulée ou a pris trop de temps. Veuillez réessayer."}, "invalidVerificationCode": {"message": "Code de vérification invalide"}, "removeMasterPasswordForOrganizationUserKeyConnector": {"message": "Un mot de passe maître n'est plus requis pour les membres de l'organisation suivante. Veuillez confirmer le domaine ci-dessous avec l'administrateur de votre organisation."}, "keyConnectorDomain": {"message": "Domaine Key Connector"}, "leaveOrganization": {"message": "Quitter l'organisation"}, "removeMasterPassword": {"message": "Supprimer le mot de passe principal"}, "removedMasterPassword": {"message": "Mot de passe principal supprimé"}, "allowSso": {"message": "Autoriser l'authentification SSO"}, "allowSsoDesc": {"message": "Une fois configuré, votre configuration sera enregistrée et les membres seront en mesure de s'authentifier en utilisant leurs identifiants de l'Identity Provider."}, "ssoPolicyHelpStart": {"message": "Utiliser la", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Use the require single-sign-on authentication policy to require all members to log in with SSO.'"}, "ssoPolicyHelpAnchor": {"message": "la politique d'authentification unique exigée", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Use the require single-sign-on authentication policy to require all members to log in with SSO.'"}, "ssoPolicyHelpEnd": {"message": "pour exiger que tous les membres se connectent avec le SSO.", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Use the require single-sign-on authentication policy to require all members to log in with SSO.'"}, "memberDecryptionOption": {"message": "Options de déchiffrement des membres"}, "memberDecryptionPassDesc": {"message": "Une fois authentifiés, les membres déchiffreront les données du coffre à l'aide de leur mot de passe principal."}, "keyConnector": {"message": "Key Connector"}, "memberDecryptionKeyConnectorDescStart": {"message": "Connectez l'identifiant par SSO à votre serveur de clés de déchiffrement auto-hébergé. En utilisant cette option, les membres n'auront pas besoin d'utiliser leur mot de passe principal pour déchiffrer les données du coffre. Contactez le support Bitwarden pour une assistance à la configuration. L'", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Connect login with SSO to your self-hosted decryption key server. Using this option, members won’t need to use their master passwords to decrypt vault data. The require SSO authentication and single organization policies are required to set up Key Connector decryption. Contact Bitwarden Support for set up assistance.'"}, "memberDecryptionKeyConnectorDescLink": {"message": "authentification SSO requise et les politiques de sécurité d'organisation unique", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Connect login with SSO to your self-hosted decryption key server. Using this option, members won’t need to use their master passwords to decrypt vault data. The require SSO authentication and single organization policies are required to set up Key Connector decryption. Contact Bitwarden Support for set up assistance.'"}, "memberDecryptionKeyConnectorDescEnd": {"message": "sont nécessaires pour configurer le déchiffrement de Key Connector. Contactez le support Bitwarden pour obtenir de l'aide.", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Connect login with SSO to your self-hosted decryption key server. Using this option, members won’t need to use their master passwords to decrypt vault data. The require SSO authentication and single organization policies are required to set up Key Connector decryption. Contact Bitwarden Support for set up assistance.'"}, "keyConnectorPolicyRestriction": {"message": "\"Connexion avec SSO et déchiffrement avec Key Connector\" est activé. Cette politique ne s'appliquera qu'aux propriétaires et aux administrateurs."}, "enabledSso": {"message": "SSO activé"}, "disabledSso": {"message": "SSO désactivé"}, "enabledKeyConnector": {"message": "Key Connector activé"}, "disabledKeyConnector": {"message": "Key Connector désactivé"}, "keyConnectorWarning": {"message": "Une fois que les membres commencent à utiliser Key Connector, votre organisation ne peut plus revenir au déchiffrement par mot de passe principal. Ne procédez que si vous êtes à l'aise avec le déploiement et la gestion d'un serveur de clés."}, "migratedKeyConnector": {"message": "Migré vers Key Connector"}, "paymentSponsored": {"message": "Veuillez fournir un moyen de paiement à associer à l'organisation. Ne vous inquiétez pas, nous ne vous facturerons rien à moins que vous ne sélectionniez des fonctionnalités supplémentaires ou que votre parrainage expire. "}, "orgCreatedSponsorshipInvalid": {"message": "L'offre de parrainage a expiré, vous pouvez supprimer l'organisation que vous avez créée pour éviter d'être facturé à la fin de votre essai de 7 jours. Dans le cas contraire, vous pouvez fermer ce message pour conserver l'organisation et assumer la responsabilité de la facturation."}, "newFamiliesOrganization": {"message": "Nouvelle organisation Familles"}, "acceptOffer": {"message": "Accepter l'offre"}, "sponsoringOrg": {"message": "Organisation marraine"}, "keyConnectorTest": {"message": "Test"}, "keyConnectorTestSuccess": {"message": "Succès! Key Connector atteint."}, "keyConnectorTestFail": {"message": "Impossible d'atteindre Key Connector. Vérifiez l'URL."}, "sponsorshipTokenHasExpired": {"message": "L'offre de parrainage a expiré."}, "freeWithSponsorship": {"message": "GRATUIT avec le parrainage"}, "viewBillingSyncToken": {"message": "Voir le Jeton de Synchronisation de Facturation"}, "generateBillingToken": {"message": "Générer un jeton de facturation"}, "copyPasteBillingSync": {"message": "<PERSON><PERSON><PERSON> et collez ce jeton dans les paramètres de la Synchronisation de Facturation de votre organisation auto-hébergée."}, "billingSyncCanAccess": {"message": "Votre Jeton de Synchronisation de Facturation peut accéder et modifier les paramètres d'abonnement de cette organisation."}, "manageBillingTokenSync": {"message": "<PERSON><PERSON><PERSON> le Jeton de Facturation"}, "setUpBillingSync": {"message": "Configurer la Synchronisation de Facturation"}, "generateToken": {"message": "<PERSON><PERSON><PERSON><PERSON> le Jeton"}, "rotateToken": {"message": "Régénérer le jeton"}, "rotateBillingSyncTokenWarning": {"message": "<PERSON> vous continuez, vous devrez reconfigurer la synchronisation de facturation sur votre serveur auto-hébergé."}, "rotateBillingSyncTokenTitle": {"message": "Permuter le Jeton de Synchronisation de Facturation invalidera le jeton précédent."}, "selfHostedServer": {"message": "auto-hébergé"}, "customEnvironment": {"message": "Environnement personnalisé"}, "selfHostedBaseUrlHint": {"message": "Spécifiez l'URL de base de votre installation auto-hébergée par Bitwarden. Exemple: https://bitwarden.compagnie.com"}, "selfHostedCustomEnvHeader": {"message": "Pour un configuration avancée. V<PERSON> pouvez spécifier l'URL de base indépendamment pour chaque service."}, "selfHostedEnvFormInvalid": {"message": "V<PERSON> devez ajouter soit l'URL du Serveur de base, soit au moins un environnement personnalisé."}, "apiUrl": {"message": "URL du serveur API"}, "webVaultUrl": {"message": "URL du serveur du coffre Web"}, "identityUrl": {"message": "URL du serveur d'identité"}, "notificationsUrl": {"message": "URL du serveur de notifications"}, "iconsUrl": {"message": "URL du serveur d’icônes"}, "environmentSaved": {"message": "URLs d'environnement enregistrées"}, "selfHostingTitle": {"message": "Auto-Hébergement"}, "selfHostingEnterpriseOrganizationSectionCopy": {"message": "Pour configurer votre organisation sur votre propre serveur, vous devrez télécharger votre fichier de licence. Pour prendre en charge les forfaits Familles Gratuits et les fonctionnalités avancées de facturation pour votre organisation auto-hébergée, vous devrez configurer la synchronisation de facturation."}, "billingSyncApiKeyRotated": {"message": "<PERSON><PERSON> régéné<PERSON>"}, "billingSyncKeyDesc": {"message": "Un jeton de synchronisation de facturation provenant des paramètres d'abonnement de votre organisation dans le cloud est nécessaire pour remplir ce formulaire."}, "billingSyncKey": {"message": "Jeton de Synchronisation de Facturation"}, "automaticBillingSyncDesc": {"message": "La synchronisation automatique déverrouille les parrainages des Familles et vous permet de synchroniser votre licence sans téléverser de fichier. Après avoir effectué des mises à jour sur le serveur cloud de Bitwarden, sélectionnez la licence de synchronisation pour appliquer les modifications."}, "active": {"message": "Actif"}, "inactive": {"message": "Inactif"}, "sentAwaitingSync": {"message": "Envoyé (en Attente de Synchronisation)"}, "sent": {"message": "<PERSON><PERSON><PERSON>"}, "requestRemoved": {"message": "Supprimé (en attente de synchronisation)"}, "requested": {"message": "<PERSON><PERSON><PERSON>"}, "formErrorSummaryPlural": {"message": "$COUNT$ champs ci-dessus nécessitent votre attention.", "placeholders": {"count": {"content": "$1", "example": "5"}}}, "formErrorSummarySingle": {"message": "1 champ ci-dessus nécessite votre attention."}, "fieldRequiredError": {"message": "$FIELDNAME$ est requis.", "placeholders": {"fieldname": {"content": "$1", "example": "Full name"}}}, "required": {"message": "requis"}, "charactersCurrentAndMaximum": {"message": "$CURRENT$/$MAX$ caractères maximum", "placeholders": {"current": {"content": "$1", "example": "0"}, "max": {"content": "$2", "example": "100"}}}, "characterMaximum": {"message": "$MAX$ caractères maximum", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "idpSingleSignOnServiceUrlRequired": {"message": "Requis si l'ID d'Entité n'est pas une URL."}, "offerNoLongerValid": {"message": "Cette offre n'est plus valide. Contactez les administrateurs de votre organisation pour plus d'informations."}, "openIdOptionalCustomizations": {"message": "Personnalisations Optionnelles"}, "openIdAuthorityRequired": {"message": "Requis si l'Autorité n'est pas valide."}, "separateMultipleWithComma": {"message": "Séparer avec des virgules."}, "sessionTimeout": {"message": "Votre session a expiré. Veuillez revenir en arrière et essayer de vous connecter à nouveau."}, "exportingPersonalVaultTitle": {"message": "Export du coffre personnel"}, "exportingOrganizationVaultTitle": {"message": "Export du coffre de l'organisation"}, "exportingIndividualVaultDescription": {"message": "Seuls les éléments individuels du coffre associés à $EMAIL$ seront exportés. Les éléments du coffre de l'organisation ne seront pas inclus. Seules les informations sur les éléments du coffre seront exportées et n'incluront pas les pièces jointes associées.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Seuls les éléments individuels du coffre et les pièces jointes associées à $EMAIL$ seront exportés. Les éléments du coffre de l'organisation ne seront pas inclus", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultDesc": {"message": "Seul le coffre d'organisation associé à $ORGANIZATION$ sera exporté. Les éléments dans les coffres individuels ou d'autres organisations ne seront pas inclus.", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "accessDenied": {"message": "<PERSON><PERSON>ès Refusé. Vous n'avez pas l'autorisation d'afficher cette page."}, "masterPassword": {"message": "Mot de passe principal"}, "security": {"message": "Sécurité"}, "keys": {"message": "<PERSON><PERSON><PERSON>"}, "billingHistory": {"message": "Historique de facturation"}, "backToReports": {"message": "Retour aux rapports"}, "organizationPicker": {"message": "Sélecteur d'organisation"}, "currentOrganization": {"message": "Organisation actuelle", "description": "This is used by screen readers to indicate the organization that is currently being shown to the user."}, "accountLoggedInAsName": {"message": "Compte : connecté en tant que $NAME$", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}}}, "accountSettings": {"message": "Paramètres du compte"}, "generator": {"message": "Générateur", "description": "Short for 'credential generator'."}, "generateUsername": {"message": "<PERSON><PERSON><PERSON>rer le Nom d'Utilisateur"}, "generateEmail": {"message": "Générer un courriel"}, "generatePassword": {"message": "Générer un mot de passe"}, "generatePassphrase": {"message": "Gén<PERSON>rer une phrase de passe"}, "passwordGenerated": {"message": "Mot de passe généré"}, "passphraseGenerated": {"message": "Phrase de passe générée"}, "usernameGenerated": {"message": "Nom d'utilisateur généré"}, "emailGenerated": {"message": "<PERSON><PERSON><PERSON>"}, "spinboxBoundariesHint": {"message": "La valeur doit être comprise entre $MIN$ et $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Utilisez des caractères $RECOMMENDED$ ou plus pour générer un mot de passe fort.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Utilisez des mots $RECOMMENDED$ ou plus pour générer une phrase de passe forte.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "<PERSON><PERSON><PERSON>", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Utilisez les capacités de sous-adressage de votre fournisseur de messagerie."}, "catchallEmail": {"message": "Collecteur <PERSON><PERSON><PERSON> (catch-all)"}, "catchallEmailDesc": {"message": "Utilisez la boîte de réception du collecteur (catch-all) configurée de votre domaine."}, "useThisEmail": {"message": "Utiliser ce courriel"}, "random": {"message": "Aléatoire", "description": "Generates domain-based username using random letters"}, "randomWord": {"message": "Mot Aléatoire"}, "usernameGenerator": {"message": "Générateur de nom d'utilisateur"}, "useThisPassword": {"message": "Utiliser ce mot de passe"}, "useThisPassphrase": {"message": "Utiliser cette phrase de passe"}, "useThisUsername": {"message": "Utiliser ce nom d'utilisateur"}, "securePasswordGenerated": {"message": "Mot de passe sécuritaire généré! N'oubliez pas de mettre à jour votre mot de passe sur le site Web aussi."}, "useGeneratorHelpTextPartOne": {"message": "Utiliser le générateur", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "pour créer un mot de passe fort unique", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "service": {"message": "Service"}, "unknownCipher": {"message": "É<PERSON><PERSON> inconnu, vous devrez peut-être demander l'autorisation d'accéder à cet élément."}, "cannotSponsorSelf": {"message": "Vous ne pouvez pas réclamer pour le compte actif. Saisissez un courriel différent."}, "revokeWhenExpired": {"message": "Expire le $DATE$", "placeholders": {"date": {"content": "$1", "example": "12/31/2020"}}}, "awaitingSyncSingular": {"message": "Jeton permuté il y a $DAYS$ jour. Mettez à jour le jeton de synchronisation de facturation dans les paramètres de votre organisation auto-hébergée.", "placeholders": {"days": {"content": "$1", "example": "1"}}}, "awaitingSyncPlural": {"message": "Jeton permuté il y a $DAYS$ jour. Mettez à jour le jeton de synchronisation de facturation dans les paramètres de votre organisation auto-hébergée.", "placeholders": {"days": {"content": "$1", "example": "1"}}}, "lastSync": {"message": "Dernière Synchronisation", "description": "Used as a prefix to indicate the last time a sync occurred. Example \"Last sync 1968-11-16 00:00:00\""}, "sponsorshipsSynced": {"message": "Des parrainages auto-hébergés ont été synchronisés."}, "billingManagedByProvider": {"message": "Géré par $PROVIDER$", "placeholders": {"provider": {"content": "$1", "example": "Managed Services Company"}}}, "billingContactProviderForAssistance": {"message": "Veuillez les contacter pour plus d'assistance", "description": "This text is displayed if an organization's billing is managed by a Provider. It tells the user to contact the Provider for assistance."}, "forwardedEmail": {"message": "Alias d'email transféré"}, "forwardedEmailDesc": {"message": "Générer un alias de courriel avec un service de transfert externe."}, "forwarderDomainName": {"message": "Domaine du courriel", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Choisissez un domaine qui est supporté par le service sélectionné", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "Erreur $SERVICENAME$ : $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "<PERSON><PERSON><PERSON><PERSON> par Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Site web : $WEBSITE$. Généré par Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Jeton API $SERVICENAME$ invalide", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Jeton API $SERVICENAME$ non valide : $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ a refusé votre demande. Veuillez contacter votre fournisseur de service pour assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ a refusé votre demande : $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Impossible d'obtenir l'ID du compte de courriel masqué $SERVICENAME$.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Domaine de $SERVICENAME$ invalide.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "L'URL $SERVICENAME$ invalide.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Une erreur $SERVICENAME$ inconnue s'est produite.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Émetteur inconnu: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "Nom d'hôte", "description": "Part of a URL."}, "deviceVerification": {"message": "Vérification de l'Appareil"}, "enableDeviceVerification": {"message": "Activer la Vérification de l'Appareil"}, "deviceVerificationDesc": {"message": "Les codes de vérification sont envoyés à votre adresse électronique lorsque vous vous connectez à partir d'un appareil non reconnu"}, "updatedDeviceVerification": {"message": "Vérification de l'Appareil mise à jour"}, "areYouSureYouWantToEnableDeviceVerificationTheVerificationCodeEmailsWillArriveAtX": {"message": "Êtes-vous sûr de vouloir activer la vérification des appareils ? Les courriels de code de vérification arriveront à : $EMAIL$", "placeholders": {"email": {"content": "$1", "example": "My Email"}}}, "premiumSubcriptionRequired": {"message": "Abonnement Premium requis"}, "scim": {"message": "Provisionnement SCIM", "description": "The text, 'SCIM', is an acronym and should not be translated."}, "scimDescription": {"message": "Fournit automatiquement aux utilisateurs et aux groupes votre fournisseur d'identité préféré via l'approvisionnement SCIM", "description": "the text, 'SCIM', is an acronym and should not be translated."}, "scimIntegrationDescription": {"message": "Fournissez automatiquement aux utilisateurs et aux groupes avec votre fournisseur d'identité préféré via l'approvisionnement SCIM. Trouvez les intégrations supportées", "description": "the text, 'SCIM', is an acronym and should not be translated."}, "scimEnabledCheckboxDesc": {"message": "Activer SCIM", "description": "the text, 'SCIM', is an acronym and should not be translated."}, "scimEnabledCheckboxDescHelpText": {"message": "Configurez votre fournisseur d'identité préféré en configurant l'URL et la clé API SCIM", "description": "the text, 'SCIM', is an acronym and should not be translated."}, "scimApiKeyHelperText": {"message": "Cette clé API possède l'accès pour gérer les utilisateurs au sein de votre organisation. Elle devrait être tenue secrète."}, "copyScimKey": {"message": "Copiez la clé API SCIM dans votre presse-papiers", "description": "the text, 'SCIM' and 'API', are acronyms and should not be translated."}, "rotateScimKey": {"message": "Régénérer la clé API SCIM", "description": "the text, 'SCIM' and 'API', are acronyms and should not be translated."}, "rotateScimKeyWarning": {"message": "Êtes-vous sûr de vouloir régénérer la clé API SCIM ? La clé actuelle ne fonctionnera plus pour aucune intégration existante.", "description": "the text, 'SCIM' and 'API', are acronyms and should not be translated."}, "rotateKey": {"message": "Régénérer la clé"}, "scimApiKey": {"message": "Clé API SCIM", "description": "the text, 'SCIM' and 'API', are acronyms and should not be translated."}, "copyScimUrl": {"message": "Copiez l'URL du point de terminaison SCIM dans votre presse-papiers", "description": "the text, 'SCIM' and 'URL', are acronyms and should not be translated."}, "scimUrl": {"message": "URL SCIM", "description": "the text, 'SCIM' and 'URL', are acronyms and should not be translated."}, "scimApiKeyRotated": {"message": "Clé API SCIM régénérée avec succès", "description": "the text, 'SCIM' and 'API', are acronyms and should not be translated."}, "scimSettingsSaved": {"message": "Les paramètres SCIM ont été enregistrés avec succès", "description": "the text, 'SCIM', is an acronym and should not be translated."}, "inputRequired": {"message": "Entrée requise."}, "inputEmail": {"message": "L'entrée n'est pas une adresse électronique."}, "inputMinLength": {"message": "L'entrée doit comporter au moins $COUNT$ caractères.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "L'entrée ne doit pas dépasser $COUNT$ caractères de long.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "Les caractères suivants ne sont pas autorisés : $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "La valeur d'entrée doit être au moins $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "La valeur d'entrée ne doit pas excéder $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "1 ou plusieurs courriels sont invalides"}, "tooManyEmails": {"message": "Vous ne pouvez soumettre que jusqu'à $COUNT$ courriels à la fois", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "fieldsNeedAttention": {"message": "$COUNT$ champ(s) ci-dessus nécessitent votre attention.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 champ nécessite votre attention."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ champs nécessitent votre attention.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Erreur de connexion avec le service Duo. Utilisez une autre méthode de connexion en deux étapes ou contactez Duo pour obtenir de l'aide."}, "duoRequiredByOrgForAccount": {"message": "L'authentification à double facteur DUO est requise pour votre compte."}, "duoTwoFactorRequiredPageSubtitle": {"message": "L'authentification à deux facteurs est requise pour votre compte. Su<PERSON>z les étapes ci-dessous pour terminer la connexion."}, "followTheStepsBelowToFinishLoggingIn": {"message": "<PERSON><PERSON>z les étapes ci-dessous pour terminer de vous connecter."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Suivez les étapes ci-dessous pour terminer la connexion avec votre clé de sécurité."}, "launchDuo": {"message": "Lancer <PERSON>U<PERSON>"}, "turnOn": {"message": "Activer"}, "on": {"message": "Activé"}, "off": {"message": "Désactivée"}, "members": {"message": "Me<PERSON><PERSON>"}, "reporting": {"message": "<PERSON><PERSON><PERSON>"}, "numberOfUsers": {"message": "Nombre d’utilisateurs"}, "pickAnAvatarColor": {"message": "Choisis<PERSON>z une couleur d'avatar"}, "customizeAvatar": {"message": "Personnaliser l'avatar"}, "avatarUpdated": {"message": "Avatar mis à jour"}, "brightBlue": {"message": "<PERSON><PERSON><PERSON>"}, "green": {"message": "<PERSON>ert"}, "orange": {"message": "Orange"}, "lavender": {"message": "<PERSON><PERSON><PERSON>"}, "yellow": {"message": "Jaune"}, "indigo": {"message": "Indigo"}, "teal": {"message": "<PERSON><PERSON><PERSON>"}, "salmon": {"message": "<PERSON><PERSON><PERSON>"}, "pink": {"message": "<PERSON>"}, "customColor": {"message": "<PERSON><PERSON><PERSON>"}, "selectPlaceholder": {"message": "-- S<PERSON><PERSON><PERSON>ner--"}, "multiSelectPlaceholder": {"message": "-- Ta<PERSON>z pour Filtrer --"}, "multiSelectLoading": {"message": "Récupération des options..."}, "multiSelectNotFound": {"message": "Aucun élément trouvé"}, "multiSelectClearAll": {"message": "Effacer tout"}, "toggleCharacterCount": {"message": "Afficher/masquer la longueur", "description": "'Character count' describes a feature that displays a number next to each character of the password."}, "passwordCharacterCount": {"message": "Longueur du mot de passe", "description": "'Character count' describes a feature that displays a number next to each character of the password."}, "hide": {"message": "Masquer"}, "projects": {"message": "Projets", "description": "Description for the Projects field."}, "lastEdited": {"message": "Dernière Modification", "description": "The label for the date and time when a item was last edited."}, "editSecret": {"message": "Modifier le Secret", "description": "Action to modify an existing secret."}, "addSecret": {"message": "Ajouter un Secret", "description": "Action to create a new secret."}, "copySecretName": {"message": "<PERSON><PERSON><PERSON> le Nom du Secret", "description": "Action to copy the name of a secret to the system's clipboard."}, "copySecretValue": {"message": "Copier la Valeur Secrète", "description": "Action to copy the value of a secret to the system's clipboard."}, "deleteSecret": {"message": "Supp<PERSON>er le Secret", "description": "Action to delete a single secret from the system."}, "deleteSecrets": {"message": "Supprimer les secrets", "description": "The action to delete multiple secrets from the system."}, "hardDeleteSecret": {"message": "Supprimer définitivement le secret"}, "hardDeleteSecrets": {"message": "Supprimer définitivement les secrets"}, "secretProjectAssociationDescription": {"message": "Sélectionnez les projets auxquels le secret sera associé. Seuls les utilisateurs de l'organisation ayant accès à ces projets pourront voir le secret.", "description": "A prompt explaining how secrets can be associated with projects."}, "selectProjects": {"message": "Sélectionnez les projets", "description": "A label for a type-to-filter input field to choose projects."}, "searchProjects": {"message": "Rechercher des Projets", "description": "Label for the search bar used to search projects."}, "project": {"message": "Projet", "description": "Similar to collections, projects can be used to group secrets."}, "editProject": {"message": "Modifier le Projet", "description": "The action to modify an existing project."}, "viewProject": {"message": "Afficher le Projet", "description": "The action to view details of a project."}, "deleteProject": {"message": "Supprimer le Projet", "description": "The action to delete a project from the system."}, "deleteProjects": {"message": "Supprimer les Projets", "description": "The action to delete multiple projects from the system."}, "secret": {"message": "Secret", "description": "Label for a secret (key/value pair)"}, "serviceAccount": {"message": "Compte de service", "description": "A machine user which can be used to automate processes and access secrets in the system."}, "serviceAccounts": {"message": "Comptes de Service", "description": "The title for the section that deals with service accounts."}, "secrets": {"message": "Secrets", "description": "The title for the section of the application that deals with secrets."}, "nameValuePair": {"message": "Nom/Paire de valeur", "description": "Title for a name/ value pair. Secrets typically consist of a name and value pair."}, "secretEdited": {"message": "Secret Modifié", "description": "Notification for the successful editing of a secret."}, "secretCreated": {"message": "<PERSON>", "description": "Notification for the successful creation of a secret."}, "newSecret": {"message": "Nouveau Secret", "description": "Title for creating a new secret."}, "newServiceAccount": {"message": "Nouveau Compte de Service", "description": "Title for creating a new service account."}, "secretsNoItemsTitle": {"message": "Aucun secret à afficher", "description": "Empty state to indicate that there are no secrets to display."}, "secretsNoItemsMessage": {"message": "Pour commencer, ajoutez un nouveau secret ou importez des secrets.", "description": "Message to encourage the user to start adding secrets."}, "secretsTrashNoItemsMessage": {"message": "Il n'y a pas de secrets dans la corbeille."}, "serviceAccountsNoItemsMessage": {"message": "Créez un nouveau Compte de Service pour commencer à automatiser l'accès secret.", "description": "Message to encourage the user to start creating service accounts."}, "serviceAccountsNoItemsTitle": {"message": "Rien à afficher pour l'instant", "description": "Title to indicate that there are no service accounts to display."}, "searchSecrets": {"message": "Rechercher des secrets", "description": "Placeholder text for searching secrets."}, "deleteServiceAccounts": {"message": "Supprimer des Comptes de Service", "description": "Title for the action to delete one or multiple service accounts."}, "deleteServiceAccount": {"message": "Supp<PERSON>er le Compte de Service", "description": "Title for the action to delete a single service account."}, "viewServiceAccount": {"message": "<PERSON>ff<PERSON>r le Compte de Service", "description": "Action to view the details of a service account."}, "deleteServiceAccountDialogMessage": {"message": "La suppression du compte de service $SERVICE_ACCOUNT$ est permanente et irréversible.", "placeholders": {"service_account": {"content": "$1", "example": "Service account name"}}}, "deleteServiceAccountsDialogMessage": {"message": "La suppression des comptes de service est permanente et irréversible."}, "deleteServiceAccountsConfirmMessage": {"message": "Supprimer les $COUNT$ comptes de service", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "deleteServiceAccountToast": {"message": "Compte de service supprimé"}, "deleteServiceAccountsToast": {"message": "Comptes de service supprimés"}, "searchServiceAccounts": {"message": "Rechercher des Comptes de Service", "description": "Placeholder text for searching service accounts."}, "editServiceAccount": {"message": "É<PERSON>er le compte de service", "description": "Title for editing a service account."}, "addProject": {"message": "Ajouter un Projet", "description": "Title for creating a new project."}, "projectEdited": {"message": "Projet modifié", "description": "Notification for the successful editing of a project."}, "projectSaved": {"message": "Projet sauvegardé", "description": "Notification for the successful saving of a project."}, "projectCreated": {"message": "Projet créé", "description": "Notification for the successful creation of a project."}, "projectName": {"message": "Nom du Projet", "description": "Label for entering the name of a project."}, "newProject": {"message": "Nouveau Projet", "description": "Title for creating a new project."}, "softDeleteSecretWarning": {"message": "La suppression de secrets peut affecter les intégrations existantes.", "description": "Warns that deleting secrets can have consequences on integrations"}, "softDeletesSuccessToast": {"message": "Secrets envoyés à la corbeille", "description": "Notifies that the selected secrets have been moved to the trash"}, "hardDeleteSecretConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer définitivement ce secret ?"}, "hardDeleteSecretsConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer définitivement ces secrets ?"}, "hardDeletesSuccessToast": {"message": "Secrets définitivement supprimés"}, "smAccess": {"message": "Accès", "description": "Title indicating what permissions a service account has"}, "projectCommaSecret": {"message": "Projet, Secret", "description": ""}, "serviceAccountName": {"message": "Nom du Compte de Service", "description": "Label for the name of a service account"}, "serviceAccountCreated": {"message": "Compte de Service créé", "description": "Notifies that a new service account has been created"}, "serviceAccountUpdated": {"message": "Compte de service mis à jour", "description": "Notifies that a service account has been updated"}, "typeOrSelectProjects": {"message": "Saisissez ou sélectionnez des projets", "description": "Instructions for selecting projects for a service account"}, "newSaTypeToFilter": {"message": "Saisir pour filtrer", "description": "Instructions for filtering a list of projects or secrets"}, "deleteProjectsToast": {"message": "Projets supprimés", "description": "Notifies that the selected projects have been deleted"}, "deleteProjectToast": {"message": "Projet supprimé", "description": "Notifies that a project has been deleted"}, "deleteProjectDialogMessage": {"message": "La suppression du projet $PROJECT$ est permanente et irréversible.", "description": "Informs users that projects are hard deleted and not sent to trash", "placeholders": {"project": {"content": "$1", "example": "project name"}}}, "deleteProjectInputLabel": {"message": "Tapez \"$CONFIRM$\" pour continuer", "description": "Users are prompted to type 'confirm' to delete a project", "placeholders": {"confirm": {"content": "$1", "example": "Delete 3 projects"}}}, "deleteProjectConfirmMessage": {"message": "Supprimer $PROJECT$", "description": "Confirmation prompt to delete a specific project, where '$PROJECT$' is a placeholder for the name of the project.", "placeholders": {"project": {"content": "$1", "example": "project name"}}}, "deleteProjectsConfirmMessage": {"message": "Supprimer $COUNT$ projets", "description": "Confirmation prompt to delete multiple projects, where '$COUNT$' is a placeholder for the number of projects to be deleted.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "deleteProjectsDialogMessage": {"message": "La suppression de projets est permanente et irréversible.", "description": "This message is displayed in a dialog box as a warning before proceeding with project deletion."}, "projectsNoItemsTitle": {"message": "Aucun projet à afficher", "description": "Empty state to be displayed when there are no projects to display in the list."}, "projectsNoItemsMessage": {"message": "Ajouter un nouveau projet pour commencer à organiser des secrets.", "description": "Message to be displayed when there are no projects to display in the list."}, "smConfirmationRequired": {"message": "Confirmation requise", "description": "Indicates that user confirmation is required for an action to proceed."}, "bulkDeleteProjectsErrorMessage": {"message": "Les projets suivants n'ont pu être supprimés :", "description": "Message to be displayed when there is an error during bulk project deletion."}, "softDeleteSuccessToast": {"message": "Secret envoy<PERSON> <PERSON> la corbeille", "description": "Notification to be displayed when a secret is successfully sent to the trash."}, "hardDeleteSuccessToast": {"message": "Secret définitivement supprimé"}, "accessTokens": {"message": "Jetons d'accès", "description": "Title for the section displaying access tokens."}, "createAccessToken": {"message": "<PERSON><PERSON>er un jeton d'accès", "description": "Button label for creating a new access token."}, "expires": {"message": "Expire", "description": "Label for the expiration date of an access token."}, "canRead": {"message": "Peut Lire", "description": "Label for the access level of an access token (Read only)."}, "accessTokensNoItemsTitle": {"message": "Aucun jeton d'accès à afficher", "description": "Title to be displayed when there are no access tokens to display in the list."}, "accessTokensNoItemsDesc": {"message": "Pour commencer, créez un jeton d'accès", "description": "Message to be displayed when there are no access tokens to display in the list."}, "downloadAccessToken": {"message": "Télécharger ou copier avant de fermer.", "description": "Message to be displayed before closing an access token, reminding the user to download or copy it."}, "expiresOnAccessToken": {"message": "Expire le:", "description": "Label for the expiration date of an access token."}, "accessTokenCallOutTitle": {"message": "Les jetons d'accès ne sont pas stockés et ne peuvent pas être récupérés", "description": "Notification to inform the user that access tokens are only displayed once and cannot be retrieved again."}, "copyToken": {"message": "<PERSON><PERSON><PERSON> le jeton", "description": "Copies the generated access token to the user's clipboard."}, "accessToken": {"message": "<PERSON>on d'a<PERSON>ès", "description": "A unique string that gives a client application (eg. CLI) access to a secret or set of secrets."}, "accessTokenExpirationRequired": {"message": "Date d'expiration requise", "description": "Error message indicating that an expiration date for the access token must be set."}, "accessTokenCreatedAndCopied": {"message": "Jeton d'accès créé et copié dans le presse-papiers", "description": "Notification to inform the user that the access token has been created and copied to the clipboard."}, "revokeAccessToken": {"message": "Révoquer le jeton d'accès", "description": "Invalidates / cancels an access token and as such removes access to secrets for the client application."}, "revokeAccessTokens": {"message": "Révoquer les jetons d'accès"}, "revokeAccessTokenDesc": {"message": "La révocation des jetons d'accès est permanente et irréversible."}, "accessTokenRevoked": {"message": "Jetons d'accès révoqués", "description": "Toast message after deleting one or multiple access tokens."}, "noAccessTokenSelected": {"message": "Aucun jeton d'accès sélectionné à révoquer", "description": "Toast error message after trying to delete access tokens but not selecting any access tokens."}, "submenu": {"message": "Sous-menu"}, "from": {"message": "De"}, "to": {"message": "À"}, "member": {"message": "Membre"}, "update": {"message": "Mis à jour"}, "plusNMore": {"message": "+ $QUANTITY$ de plus", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "groupInfo": {"message": "Informations du groupe"}, "editGroupMembersDesc": {"message": "Accorder aux membres l'accès aux collections assignées au groupe."}, "editGroupCollectionsDesc": {"message": "Accorder l'accès aux collections en les ajoutant à ce groupe."}, "restrictedCollectionAssignmentDesc": {"message": "Vous pouvez seulement assigner les collections que vous gérez."}, "selectMembers": {"message": "Sélectionner les membres"}, "selectCollections": {"message": "Sélectionner les collections"}, "role": {"message": "R<PERSON><PERSON>"}, "removeMember": {"message": "Supprimer le membre"}, "collection": {"message": "Collection"}, "noCollection": {"message": "Aucune collection"}, "noCollectionsAdded": {"message": "Pas de collections ajoutées"}, "noMembersAdded": {"message": "Pas de membres a<PERSON>s"}, "noGroupsAdded": {"message": "Pas de groupes ajoutés"}, "group": {"message": "Groupe"}, "domainVerification": {"message": "Vérification du domaine"}, "newDomain": {"message": "Nouveau domaine"}, "noDomains": {"message": "Aucun domaine"}, "noDomainsSubText": {"message": "La connexion d'un domaine permet aux membres de passer outre le champ de l'identifiant SSO lors de la connexion avec SSO."}, "copyDnsTxtRecord": {"message": "Copier l'enregistrement DNS TXT"}, "dnsTxtRecord": {"message": "Enregistrement DNS TXT"}, "dnsTxtRecordInputHint": {"message": "Copiez-collez l'enregistrement TXT dans votre fournisseur de services DNS."}, "removeDomain": {"message": "Retirer le domaine"}, "removeDomainWarning": {"message": "Le retrait d'un domaine ne peut pas être annulé. Êtes-vous sûr de vouloir continuer ?"}, "domainRemoved": {"message": "Domaine retiré"}, "domainSaved": {"message": "Domaine sa<PERSON>"}, "duplicateDomainError": {"message": "Vous ne pouvez pas revendiquer le même domaine deux fois."}, "domainNotAvailable": {"message": "Quelqu'un d'autre est en train d'utiliser $DOMAIN$. Utilisez un domaine différent pour continuer.", "placeholders": {"DOMAIN": {"content": "$1", "example": "bitwarden.com"}}}, "domainNameTh": {"message": "Nom"}, "domainStatusTh": {"message": "Statut"}, "lastChecked": {"message": "Dernière vérification"}, "editDomain": {"message": "É<PERSON><PERSON> le domaine"}, "domainFormInvalid": {"message": "Il y a des erreurs de formulaire qui nécessitent votre attention"}, "addedDomain": {"message": "Domaine $DOMAIN$ ajouté", "placeholders": {"DOMAIN": {"content": "$1", "example": "bitwarden.com"}}}, "removedDomain": {"message": "Domaine $DOMAIN$ retiré", "placeholders": {"DOMAIN": {"content": "$1", "example": "bitwarden.com"}}}, "verificationRequiredForActionSetPinToContinue": {"message": "Vérification requise pour cette action. Définissez un NIP pour continuer."}, "setPin": {"message": "Définir le NIP"}, "verifyWithBiometrics": {"message": "Vérifier par biométrie"}, "awaitingConfirmation": {"message": "En attente de confirmation"}, "couldNotCompleteBiometrics": {"message": "Impossible de compléter la biométrie."}, "needADifferentMethod": {"message": "Besoin d'une méthode différente ?"}, "useMasterPassword": {"message": "Utiliser le mot de passe principal"}, "usePin": {"message": "Utiliser un NIP"}, "useBiometrics": {"message": "Utiliser la biométrie"}, "enterVerificationCodeSentToEmail": {"message": "Entrez le code de vérification qui a été envoyé à votre adresse courriel."}, "resendCode": {"message": "Renvoyer le code"}, "memberColumnHeader": {"message": "Membre"}, "groupSlashMemberColumnHeader": {"message": "Groupe/Membre"}, "selectGroupsAndMembers": {"message": "Sélectionner les groupes et les membres"}, "selectGroups": {"message": "Sélectionner les groupes"}, "userPermissionOverrideHelperDesc": {"message": "Les autorisations définies pour un membre remplaceront les autorisations définies par le groupe de ce membre."}, "noMembersOrGroupsAdded": {"message": "Aucun membre ou groupe ajouté"}, "deleted": {"message": "Supprimé"}, "memberStatusFilter": {"message": "Filtre de statut de membre"}, "inviteMember": {"message": "Inviter un membre"}, "addSponsorship": {"message": "Ajouter un parrainage"}, "needsConfirmation": {"message": "Confirmation nécessaire"}, "memberRole": {"message": "Rôle du membre"}, "moreFromBitwarden": {"message": "Plus de Bitwarden"}, "switchProducts": {"message": "Changer de Produits"}, "freeOrgInvLimitReachedManageBilling": {"message": "Les organisations gratuites peuvent avoir jusqu'à $SEATCOUNT$ membres. Passez à une offre payante pour inviter plus de membres.", "placeholders": {"seatcount": {"content": "$1", "example": "2"}}}, "freeOrgInvLimitReachedNoManageBilling": {"message": "Les organisations gratuites peuvent avoir jusqu'à $SEATCOUNT$ membres. Contactez le propriétaire de votre organisation pour mettre à niveau.", "placeholders": {"seatcount": {"content": "$1", "example": "2"}}}, "teamsStarterPlanInvLimitReachedManageBilling": {"message": "Les abonnements Équipes Essentiel peuvent compter jusqu'à $SEATCOUNT$ membres. Mettez à niveau vers une offre payante pour inviter plus de membres.", "placeholders": {"seatcount": {"content": "$1", "example": "10"}}}, "teamsStarterPlanInvLimitReachedNoManageBilling": {"message": "Les abonnements Équipes Essentiel peuvent compter jusqu'à $SEATCOUNT$ membres. Contacter le propriétaire de votre organisation pour mettre à niveau votre abonnements et inviter plus de membres.", "placeholders": {"seatcount": {"content": "$1", "example": "10"}}}, "freeOrgMaxCollectionReachedManageBilling": {"message": "Les organisations gratuites peuvent avoir jusqu'à $COLLECTIONCOUNT$ collections. Passez à une offre payante pour inviter plus de collections.", "placeholders": {"COLLECTIONCOUNT": {"content": "$1", "example": "2"}}}, "freeOrgMaxCollectionReachedNoManageBilling": {"message": "Les organisations gratuites peuvent avoir jusqu'à $COLLECTIONCOUNT$ collections. Contactez le propriétaire de votre organisation pour mettre à niveau.", "placeholders": {"COLLECTIONCOUNT": {"content": "$1", "example": "2"}}}, "server": {"message": "Ser<PERSON><PERSON>"}, "exportData": {"message": "Exporter les données"}, "exportingOrganizationSecretDataTitle": {"message": "Exportation des Données Secrètes de l'Organisation"}, "exportingOrganizationSecretDataDescription": {"message": "Seules les données du Secrets Manager associées à $ORGANIZATION$ seront exportées. Les éléments d'autres produits ou d'autres organisations ne seront pas inclus.", "placeholders": {"ORGANIZATION": {"content": "$1", "example": "My Org Name"}}}, "fileUpload": {"message": "Chargement du fichier"}, "upload": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "acceptedFormats": {"message": "Formats acceptés :"}, "copyPasteImportContents": {"message": "<PERSON><PERSON><PERSON> et coller le contenu de l'importation :"}, "or": {"message": "ou"}, "unlockWithBiometrics": {"message": "Déverrouiller par biométrie"}, "unlockWithPin": {"message": "Déverrouiller avec le NIP"}, "unlockWithMasterPassword": {"message": "Déverrouiller avec le mot de passe principal"}, "licenseAndBillingManagement": {"message": "Gestion des licences et de la facturation"}, "automaticSync": {"message": "Synchronisation automatique"}, "manualUpload": {"message": "Téléversement manuel"}, "manualBillingTokenUploadDesc": {"message": "Si vous ne voulez pas opter pour la synchronisation de facturation, téléverser manuellement votre licence ici. Cela ne déverrouillera pas automatiquement les parrainages des Familles."}, "syncLicense": {"message": "Synchroniser la licence"}, "licenseSyncSuccess": {"message": "Licence synchronisée avec succès"}, "licenseUploadSuccess": {"message": "Licence téléversée avec succès"}, "lastLicenseSync": {"message": "Dernière synchronisation de licence"}, "billingSyncHelp": {"message": "Aide sur la Synchronisation de la Facturation"}, "licensePaidFeaturesHelp": {"message": "Aide sur les fonctionnalités payantes de la licence"}, "selfHostGracePeriodHelp": {"message": "Après l'expiration de votre abonnement, vous avez 60 jours pour appliquer un fichier de licence mis à jour à votre organisation. La période de grâce se termine le $GRACE_PERIOD_END_DATE$.", "placeholders": {"GRACE_PERIOD_END_DATE": {"content": "$1", "example": "May 12, 2024"}}}, "uploadLicense": {"message": "Téléverser la licence"}, "projectPeopleDescription": {"message": "Accorder l'accès à ce projet à des groupes ou à des personnes."}, "projectPeopleSelectHint": {"message": "Saisissez ou sélectionnez des personnes ou des groupes"}, "projectServiceAccountsDescription": {"message": "Accorder l'accès aux comptes de service à ce projet."}, "projectServiceAccountsSelectHint": {"message": "Saisissez ou sélectionnez les comptes de service"}, "projectEmptyPeopleAccessPolicies": {"message": "Ajouter des personnes ou des groupes pour commencer à collaborer"}, "projectEmptyServiceAccountAccessPolicies": {"message": "Ajouter des comptes de service pour accorder l'accès"}, "serviceAccountPeopleDescription": {"message": "Accorder l'accès à ce compte de service à des groupes ou à des personnes."}, "serviceAccountProjectsDescription": {"message": "Assigner des projets à ce compte de service. "}, "serviceAccountEmptyProjectAccesspolicies": {"message": "Ajouter des projets pour accorder l'accès"}, "canReadWrite": {"message": "Peut lire, écrire"}, "groupSlashUser": {"message": "Groupe/Utilisateur"}, "lowKdfIterations": {"message": "Itérations KDF basses"}, "updateLowKdfIterationsDesc": {"message": "Mettez à jour vos paramètres de chiffrement pour répondre à de nouvelles recommandations de sécurité et améliorer la protection de votre compte."}, "kdfSettingsChangeLogoutWarning": {"message": "Effectuer cette action vous déconnectera de toutes les sessions actives. Vous devrez vous connecter à nouveau et compléter votre authentification à 2 facteurs, s'il y a lieu. Nous vous recommandons d'exporter votre coffre avant de modifier vos paramètres de chiffrement pour prévenir la perte de vos données."}, "secretsManager": {"message": "Secrets Manager"}, "secretsManagerAccessDescription": {"message": "Activer l'accès utilisateur au Secrets Manager."}, "userAccessSecretsManagerGA": {"message": "Cet utilisateur peut accéder au Secrets Manager"}, "important": {"message": "Important :"}, "viewAll": {"message": "<PERSON><PERSON> afficher"}, "showingPortionOfTotal": {"message": "Affichage de $PORTION$ sur $TOTAL$", "placeholders": {"portion": {"content": "$1", "example": "2"}, "total": {"content": "$2", "example": "5"}}}, "resolveTheErrorsBelowAndTryAgain": {"message": "Résolvez les erreurs ci-dessous et réessayez."}, "description": {"message": "Description"}, "errorReadingImportFile": {"message": "Une erreur est survenue lors de la lecture du fichier importé"}, "accessedSecret": {"message": "Accès au secret $SECRET_ID$.", "placeholders": {"secret_id": {"content": "$1", "example": "4d34e8a8"}}}, "sdk": {"message": "SDK", "description": "Software Development Kit"}, "createAnAccount": {"message": "<PERSON><PERSON><PERSON> un compte"}, "createSecret": {"message": "<PERSON><PERSON><PERSON> un secret"}, "createProject": {"message": "Créer un projet"}, "createServiceAccount": {"message": "Créez un compte de service"}, "downloadThe": {"message": "Télécharger le", "description": "Link to a downloadable resource. This will be used as part of a larger phrase. Example: Download the Secrets Manager CLI"}, "smCLI": {"message": "Secrets Manager CLI"}, "importSecrets": {"message": "Importer des secrets"}, "getStarted": {"message": "Commencer"}, "complete": {"message": "$COMPLETED$/$TOTAL$ Complété(s)", "placeholders": {"COMPLETED": {"content": "$1", "example": "1"}, "TOTAL": {"content": "$2", "example": "4"}}}, "restoreSecret": {"message": "Restaurer le secret"}, "restoreSecrets": {"message": "Restaurer les secrets"}, "restoreSecretPrompt": {"message": "Êtes-vous sûr de vouloir restaurer ce secret ?"}, "restoreSecretsPrompt": {"message": "Êtes-vous sûr de vouloir restaurer ces secrets ?"}, "secretRestoredSuccessToast": {"message": "Secret restauré"}, "secretsRestoredSuccessToast": {"message": "Secrets restaurés"}, "selectionIsRequired": {"message": "Une sélection est requise."}, "saPeopleWarningTitle": {"message": "Jetons d'accès encore disponibles"}, "saPeopleWarningMessage": {"message": "Le retrait de personnes d'un compte de service ne supprime pas les jetons d'accès qu'ils ont créés. Pour les meilleures pratiques de sécurité, il est recommandé de révoquer les jetons d'accès créés par des personnes retirées d'un compte de service."}, "smAccessRemovalWarningProjectTitle": {"message": "Retirer l'accès à ce projet"}, "smAccessRemovalWarningProjectMessage": {"message": "Cette action retirera votre accès au projet."}, "smAccessRemovalWarningSaTitle": {"message": "Retirer l'accès à ce compte de service"}, "smAccessRemovalWarningSaMessage": {"message": "Cette action retirera votre accès au compte de service."}, "removeAccess": {"message": "Retirer l'accès"}, "checkForBreaches": {"message": "Vérifier les brèches de données connues pour ce mot de passe"}, "exposedMasterPassword": {"message": "Mot de passe principal exposé"}, "exposedMasterPasswordDesc": {"message": "Mot de passe trouvé dans une brèche de données. Utilisez un mot de passe unique pour protéger votre compte. Êtes-vous sûr de vouloir utiliser un mot de passe exposé ?"}, "weakAndExposedMasterPassword": {"message": "Mot de passe principal faible et exposé"}, "weakAndBreachedMasterPasswordDesc": {"message": "Mot de passe faible identifié et trouvé dans une brèche de données. Utilisez un mot de passe robuste et unique pour protéger votre compte. Êtes-vous sûr de vouloir utiliser ce mot de passe ?"}, "characterMinimum": {"message": "$LENGTH$ caractères minimum", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "masterPasswordMinimumlength": {"message": "Le mot de passe principal doit comporter au moins $LENGTH$ caractères.", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "inputTrimValidator": {"message": "L'entrée ne doit pas contenir seulement des espaces.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "notAvailableForFreeOrganization": {"message": "Cette fonctionnalité n'est pas disponible pour les organisations gratuites. Contactez le propriétaire de votre organisation pour une mise à niveau."}, "smProjectSecretsNoItemsNoAccess": {"message": "Contactez l'administrateur de votre organisation pour gérer les secrets de ce projet.", "description": "The message shown to the user under a project's secrets tab when the user only has read access to the project."}, "enforceOnLoginDesc": {"message": "Exiger que les membres existants changent leurs mots de passe"}, "smProjectDeleteAccessRestricted": {"message": "Vous n'avez pas les autorisations pour supprimer ce projet", "description": "The individual description shown to the user when the user doesn't have access to delete a project."}, "smProjectsDeleteBulkConfirmation": {"message": "Les projets suivants ne peuvent pas être supprimés. Voulez-vous continuer ?", "description": "The message shown to the user when bulk deleting projects and the user doesn't have access to some projects."}, "updateKdfSettings": {"message": "Mettre à jour les paramètres KDF"}, "loginInitiated": {"message": "Connexion initiée"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Se souvenir de cet appareil pour rendre les connexions futures transparentes"}, "deviceApprovalRequired": {"message": "L'approbation de l'appareil est requise. Sélectionnez une option d'approbation ci-dessous:"}, "deviceApprovalRequiredV2": {"message": "Autorisation de l'appareil requise"}, "selectAnApprovalOptionBelow": {"message": "Sélectionnez une option d'approbation ci-dessous"}, "rememberThisDevice": {"message": "Se souvenir de cet appareil"}, "uncheckIfPublicDevice": {"message": "Décocher si vous utilisez un appareil public"}, "approveFromYourOtherDevice": {"message": "Approuver de votre autre appareil"}, "requestAdminApproval": {"message": "Demander l'approbation de l'administrateur"}, "trustedDeviceEncryption": {"message": "Chiffrement de l'appareil de confiance"}, "trustedDevices": {"message": "Appareils de confiance"}, "memberDecryptionOptionTdeDescPart1": {"message": "Les membres n'auront pas besoin d'un mot de passe principale lors de la connexion avec SSO. Le mot de passe principal est remplacé par une clé de chiffrement stockée sur le périphérique, ce qui rend ce périphérique fiable. Le premier appareil avec lequel un membre créera son compte et se connectera sera fiable. Les nouveaux appareils devront être approuvés par un périphérique de confiance existant ou par un administrateur. La", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "memberDecryptionOptionTdeDescLink1": {"message": "politique", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "memberDecryptionOptionTdeDescPart2": {"message": "d'organisation unique,", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "memberDecryptionOptionTdeDescLink2": {"message": "la politique", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "memberDecryptionOptionTdeDescPart3": {"message": "SSO requise et", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "memberDecryptionOptionTdeDescLink3": {"message": "la politique", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "memberDecryptionOptionTdeDescPart4": {"message": "d'administration de récupération de compte seront activées si cette option est utilisée.", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'Members will not need a master password when logging in with SSO. Master password is replaced with an encryption key stored on the device, making that device trusted. The first device a member creates their account and logs into will be trusted. New devices will need to be approved by an existing trusted device or by an administrator. The single organization policy, SSO required policy, and account recovery administration policy will turn on when this option is used.'"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Les autorisations de votre organisation ont été mises à jour, vous obligeant à définir un mot de passe principal.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Votre organisation exige que vous définissiez un mot de passe principal.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "sur $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "notFound": {"message": "$RESOURCE$ introuvable", "placeholders": {"resource": {"content": "$1", "example": "Service Account"}}}, "verificationRequired": {"message": "Vérification requise", "description": "Default title for the user verification dialog."}, "recoverAccount": {"message": "Récupérer le compte"}, "updatedTempPassword": {"message": "L'utilisateur a mis à jour un mot de passe émis lors de la récupération du compte."}, "activatedAccessToSecretsManager": {"message": "Accès au Secrets Manager activé", "description": "Confirmation message that one or more users gained access to Secrets Manager"}, "activateAccess": {"message": "Activer l’accès"}, "bulkEnableSecretsManagerDescription": {"message": "Accorder aux membres suivants l'accès au Secrets Manager. Le rôle accordé dans le gestionnaire de mots de passe s'appliquera au Secrets Manager.", "description": "This description is shown to an admin when they are attempting to add more users to Secrets Manager."}, "activateSecretsManager": {"message": "Activer Secrets Manager"}, "yourOrganizationsFingerprint": {"message": "Phrase d'empreinte de votre organisation", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their organization's public key with another user, for the purposes of sharing."}, "deviceApprovals": {"message": "Approbations de l'appareil"}, "deviceApprovalsDesc": {"message": "Approuver les demandes de connexion ci-dessous pour permettre au membre requérant de terminer la connexion. Les demandes non approuvées expirent après 1 semaine. Vérifiez les informations du membre avant d'approuver."}, "deviceInfo": {"message": "Informations sur l'appareil"}, "time": {"message": "Temps"}, "denyAllRequests": {"message": "Refuser toutes les demandes"}, "denyRequest": {"message": "Refuser la demande"}, "approveRequest": {"message": "Approuver la demande"}, "deviceApproved": {"message": "<PERSON><PERSON><PERSON><PERSON> approuvé"}, "deviceRemoved": {"message": "Appareil supprimé"}, "removeDevice": {"message": "Supprimer l'appareil"}, "removeDeviceConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer cet appareil ?"}, "noDeviceRequests": {"message": "Aucune demande de l'appareil"}, "noDeviceRequestsDesc": {"message": "Les demandes d'approbation de l'appareil du membre apparaîtront ici"}, "loginRequestDenied": {"message": "Demande de connexion refusée"}, "allLoginRequestsDenied": {"message": "Toutes demandes de connexion refusées"}, "loginRequestApproved": {"message": "Demande de connexion approuvée"}, "removeOrgUserNoMasterPasswordTitle": {"message": "Le compte n'a pas de mot de passe principal"}, "removeOrgUserNoMasterPasswordDesc": {"message": "La suppression de $USER$ sans lui définir un mot de passe principal, peut restreindre l'accès à son compte dans son intégralité. Êtes-vous sûr de vouloir continuer ?", "placeholders": {"user": {"content": "$1", "example": "<PERSON>"}}}, "noMasterPassword": {"message": "Aucun mot de passe principal"}, "removeMembersWithoutMasterPasswordWarning": {"message": "La suppression des membres qui n'ont pas de mot de passe principal sans leur en définir un, peut restreindre l'accès à leur compte dans son intégralité."}, "approvedAuthRequest": {"message": "Appareil approuvé pour $ID$.", "placeholders": {"id": {"content": "$1", "example": "First 8 Character of a GUID"}}}, "rejectedAuthRequest": {"message": "Appareil refusé pour $ID$.", "placeholders": {"id": {"content": "$1", "example": "First 8 Character of a GUID"}}}, "requestedDeviceApproval": {"message": "Approbation de l'appareil demandée."}, "tdeOffboardingPasswordSet": {"message": "L'utilisateur a défini un mot de passe principal lors de la prise en charge TDE."}, "startYour7DayFreeTrialOfBitwardenFor": {"message": "Commencez votre essai gratuit de 7 jours de Bitwarden pour $ORG$", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "startYour7DayFreeTrialOfBitwardenSecretsManagerFor": {"message": "Commencez votre essai gratuit de 7 jours pour Secrets Manager de Bitwarden pour $ORG$", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "next": {"message": "Suivant"}, "ssoLoginIsRequired": {"message": "La connexion SSO est requise"}, "selectedRegionFlag": {"message": "Drapeau de la région sélectionnée"}, "accountSuccessfullyCreated": {"message": "Compte créé avec succès !"}, "adminApprovalRequested": {"message": "Approbation de l'administrateur demandée"}, "adminApprovalRequestSentToAdmins": {"message": "Votre demande a été envoyée à votre administrateur."}, "troubleLoggingIn": {"message": "Problème pour vous connecter ?"}, "loginApproved": {"message": "Connexion approuvée"}, "userEmailMissing": {"message": "<PERSON><PERSON><PERSON> de l'utilisateur manquant"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "<PERSON><PERSON><PERSON> de l'utilisateur actif introuvable. Vous allez être déconnecté."}, "deviceTrusted": {"message": "Appareil de confiance"}, "inviteUsers": {"message": "Inviter des utilisateurs"}, "secretsManagerForPlan": {"message": "Secrets Manager pour $PLAN$", "placeholders": {"plan": {"content": "$1", "example": "Teams"}}}, "secretsManagerForPlanDesc": {"message": "Pour gérer les secrets tout au long du cycle de vie du développement par les équipes d'ingénérie et DevOps."}, "free2PersonOrganization": {"message": "Organisations à 2 personnes gratuites"}, "unlimitedSecrets": {"message": "Secrets illimités"}, "unlimitedProjects": {"message": "Projets illimités"}, "projectsIncluded": {"message": "$COUNT$ projets inclus", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "serviceAccountsIncluded": {"message": "$COUNT$ comptes de service inclus", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "additionalServiceAccountCost": {"message": "$COST$ par mois pour des comptes de service supplémentaires", "placeholders": {"cost": {"content": "$1", "example": "$0.50"}}}, "subscribeToSecretsManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> au Secrets Manager"}, "addSecretsManagerUpgradeDesc": {"message": "Ajoutez Secrets Manager à votre plan mis à niveau pour conserver l'accès à tous les secrets créés avec votre plan précédent."}, "additionalServiceAccounts": {"message": "Comptes de service supplémentaires"}, "includedServiceAccounts": {"message": "Votre forfait inclut $COUNT$ comptes de service.", "placeholders": {"count": {"content": "$1", "example": "50"}}}, "addAdditionalServiceAccounts": {"message": "Vous pouvez ajouter des comptes de service supplémentaires pour $COST$ par mois.", "placeholders": {"cost": {"content": "$1", "example": "$0.50"}}}, "collectionManagement": {"message": "Gestion de la collection"}, "collectionManagementDesc": {"message": "G<PERSON>rer le comportement de la collection pour l'organisation"}, "limitCollectionCreationDesc": {"message": "Limiter la création de collections aux propriétaires et administrateurs"}, "limitCollectionDeletionDesc": {"message": "Limiter la suppression de collections aux propriétaires et administrateurs"}, "limitItemDeletionDescription": {"message": "Limite la suppression de l'élément aux membres avec l'autorisation Peut gérer la collection"}, "allowAdminAccessToAllCollectionItemsDesc": {"message": "Les propriétaires et les administrateurs peuvent gérer toutes les collections et tous les éléments"}, "updatedCollectionManagement": {"message": "Paramètre de gestion de la collection mis à jour"}, "passwordManagerPlanPrice": {"message": "Prix du plan Password Manager"}, "secretsManagerPlanPrice": {"message": "Prix du plan du Secrets Manager"}, "passwordManager": {"message": "Password Manager"}, "freeOrganization": {"message": "Organisation gratuite"}, "limitServiceAccounts": {"message": "Limiter les comptes de service (facultatif)"}, "limitServiceAccountsDesc": {"message": "Définissez une limite pour vos comptes de service. Une fois cette limite atteinte, vous ne pourrez plus créer de nouveauz comptes de service."}, "serviceAccountLimit": {"message": "Limite de compte de service (facultatif)"}, "maxServiceAccountCost": {"message": "Coût potentiel maximum du compte de service"}, "loggedInExclamation": {"message": "Connecté !"}, "beta": {"message": "Beta"}, "assignCollectionAccess": {"message": "Assigner l'accès à la collection"}, "editedCollections": {"message": "Collections modifiées"}, "baseUrl": {"message": "URL du serveur"}, "selfHostBaseUrl": {"message": "URL du serveur auto-hébergé", "description": "Label for field requesting a self-hosted integration service URL"}, "alreadyHaveAccount": {"message": "Vous avez déjà un compte ?"}, "toggleSideNavigation": {"message": "Basculer la navigation latérale"}, "skipToContent": {"message": "Sauter directement au contenu"}, "managePermissionRequired": {"message": "Au moins un membre ou un groupe doit avoir l'autorisation peut gérer."}, "typePasskey": {"message": "Passkey"}, "passkeyNotCopied": {"message": "La Passkey ne sera pas copiée"}, "passkeyNotCopiedAlert": {"message": "La passkey ne sera pas copiée à l'item cloné. Voulez-vous continuez à cloner cet élément?"}, "modifiedCollectionManagement": {"message": "Paramètre de gestion de la collection modifiée $ID$.", "placeholders": {"id": {"content": "$1", "example": "Unique ID"}}}, "seeDetailedInstructions": {"message": "Consultez les instructions détaillées sur notre site d'aide à", "description": "This is followed a by a hyperlink to the help website."}, "installBrowserExtension": {"message": "Installer l'extension du navigateur"}, "installBrowserExtensionDetails": {"message": "Utilisez l'extension pour enregistrer rapidement les identifiants et les formulaires de saisie automatique sans ouvrir l'application Web."}, "projectAccessUpdated": {"message": "Accès au projet mis à jour"}, "unexpectedErrorSend": {"message": "Une erreur inattendue est survenue lors du chargement de ce Send. Réessayez plus tard."}, "seatLimitReached": {"message": "La limite de places a <PERSON><PERSON> atteinte"}, "contactYourProvider": {"message": "Contactez votre fournisseur pour acheter des places supplémentaires."}, "seatLimitReachedContactYourProvider": {"message": "La limite de places a été atteinte. Contactez votre fournisseur pour acheter des places supplémentaires."}, "collectionAccessRestricted": {"message": "L'accès à la collection est restreint"}, "readOnlyCollectionAccess": {"message": "Vous n'avez pas accès à la gestion de cette collection."}, "grantManageCollectionWarningTitle": {"message": "Autorisations pour Gérer la Collection manquantes"}, "grantManageCollectionWarning": {"message": "Accorde les autorisations pour Gérer la collection pour permettre la gestion complète de la collection incluant sa suppression."}, "grantCollectionAccess": {"message": "Accorder l'accès à cette collection aux groupes ou aux membres."}, "grantCollectionAccessMembersOnly": {"message": "Accorder l'accès à cette collection aux membres."}, "adminCollectionAccess": {"message": "Les administrateurs peuvent accéder et gérer les collections."}, "serviceAccountAccessUpdated": {"message": "Accès au compte de service mis à jour"}, "commonImportFormats": {"message": "Formats communs", "description": "Label indicating the most common import formats"}, "maintainYourSubscription": {"message": "Afin de maintenir votre abonnement pour $ORG$, ", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'To maintain your subscription for $ORG$, add a payment method.'", "placeholders": {"org": {"content": "$1", "example": "Example Inc."}}}, "addAPaymentMethod": {"message": "ajouter un moyen de paiement", "description": "This will be used as part of a larger sentence, broken up to include links. The full sentence will read 'To maintain your subscription for $ORG$, add a payment method'"}, "organizationInformation": {"message": "Informations sur l'organisation"}, "confirmationDetails": {"message": "<PERSON><PERSON><PERSON>"}, "smFreeTrialThankYou": {"message": "<PERSON><PERSON><PERSON> de vous être inscrit au Secrets Manager de Bitwarden !"}, "smFreeTrialConfirmationEmail": {"message": "Nous avons envoyé un courriel de confirmation à votre adresse de messagerie à "}, "sorryToSeeYouGo": {"message": "Désolé de vous voir partir! Aidez à améliorer Bitwarden en partageant pourquoi vous annulez.", "description": "A message shown to users as part of an offboarding survey asking them to provide more information on their subscription cancelation."}, "selectCancellationReason": {"message": "Sélectionnez une raison pour laquelle vous annulez", "description": "Used as a form field label for a select input on the offboarding survey."}, "anyOtherFeedback": {"message": "Y a-t-il d'autres commentaires que vous aimeriez partager ?", "description": "Used as a form field label for a textarea input on the offboarding survey."}, "missingFeatures": {"message": "Fonctionnalités manquantes", "description": "An option for the offboarding survey shown when a user cancels their subscription."}, "movingToAnotherTool": {"message": "Utiliser un autre outil", "description": "An option for the offboarding survey shown when a user cancels their subscription."}, "tooDifficultToUse": {"message": "Trop difficile à utiliser", "description": "An option for the offboarding survey shown when a user cancels their subscription."}, "notUsingEnough": {"message": "Pas assez utilisé", "description": "An option for the offboarding survey shown when a user cancels their subscription."}, "tooExpensive": {"message": "Trop cher", "description": "An option for the offboarding survey shown when a user cancels their subscription."}, "freeForOneYear": {"message": "Gratuit pour 1 an"}, "newWebApp": {"message": "Bienvenue dans la nouvelle application web améliorée. En savoir plus sur ce qui a changé."}, "releaseBlog": {"message": "Lire le blog de la version"}, "adminConsole": {"message": "<PERSON><PERSON><PERSON>"}, "providerPortal": {"message": "Portail fournisseur"}, "success": {"message": "Su<PERSON>ès"}, "restrictedGroupAccess": {"message": "Vous ne pouvez vous ajoutez vous-même aux groupes."}, "cannotAddYourselfToCollections": {"message": "Vous ne pouvez pas vous ajouter vous-même aux collections."}, "assign": {"message": "Assigner"}, "assignToCollections": {"message": "Assigner aux collections"}, "assignToTheseCollections": {"message": "Assigner à ces collections"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Seuls les membres de l'organisation ayant accès à ces collections pourront voir l'élément."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Seuls les membres de l'organisation ayant accès à ces collections pourront voir les éléments."}, "selectCollectionsToAssign": {"message": "Sélectionnez les collections à assigner"}, "noCollectionsAssigned": {"message": "Aucune collection n'a été assignée"}, "successfullyAssignedCollections": {"message": "Collections assignées avec succès"}, "bulkCollectionAssignmentWarning": {"message": "Vous avez sélectionné $TOTAL_COUNT$ éléments. Vous ne pouvez pas mettre à jour $READONLY_COUNT$ de ces éléments parce que vous n'avez pas les autorisations pour les éditer.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2", "example": "3"}}}, "addField": {"message": "Ajouter un champ"}, "editField": {"message": "Modifier le champ"}, "items": {"message": "Éléments"}, "assignedSeats": {"message": "Places assignées"}, "assigned": {"message": "<PERSON><PERSON><PERSON>"}, "used": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "remaining": {"message": "Restant"}, "unlinkOrganization": {"message": "Délier l'organisation"}, "manageSeats": {"message": "GÉRER LES PLACES"}, "manageSeatsDescription": {"message": "Les ajustements aux licences seront reflétés lors du prochain cycle de facturation."}, "unassignedSeatsDescription": {"message": "Licenses d'abonnement non assignés"}, "purchaseSeatDescription": {"message": "Licenses additionnelles achetées"}, "assignedSeatCannotUpdate": {"message": "Les Licences assignées ne peuvent être mises à jour. Veuillez contacter le propriétaire de votre organisation pour assistance."}, "subscriptionUpdateFailed": {"message": "Mise à jour de l'abonnement échouée"}, "trial": {"message": "<PERSON><PERSON><PERSON>", "description": "A subscription status label."}, "pastDue": {"message": "Échéance dépassée", "description": "A subscription status label"}, "subscriptionExpired": {"message": "Abonnement expiré", "description": "The date header used when a subscription is past due."}, "pastDueWarningForChargeAutomatically": {"message": "Vous bénéficiez d'une période de grace de $DAYS$ jours suivants la date d'expiration de votre abonnement pour le maintenir. Veuillez adresser les paiements en souffrance pour les factures dont les échéances sont passées d'ici le $SUSPENSION_DATE$.", "placeholders": {"days": {"content": "$1", "example": "11"}, "suspension_date": {"content": "$2", "example": "01/10/2024"}}, "description": "A warning shown to the user when their subscription is past due and they are charged automatically."}, "pastDueWarningForSendInvoice": {"message": "Vous bénéficiez d'une période de grace de $DAYS$ jours à partir de la première journée de votre première facture impayée pour maintenir votre abonnement. Veuillez adresser les paiements en souffrance pour les factures dont les échéances sont passées d'ici le $SUSPENSION_DATE$.", "placeholders": {"days": {"content": "$1", "example": "11"}, "suspension_date": {"content": "$2", "example": "01/10/2024"}}, "description": "A warning shown to the user when their subscription is past due and they pay via invoice."}, "unpaidInvoice": {"message": "Facture impayée", "description": "The header of a warning box shown to a user whose subscription is unpaid."}, "toReactivateYourSubscription": {"message": "Pour ré<PERSON>r votre abonnement, veuil<PERSON><PERSON> adresser les paiements en souffrance pour les factures dont les échéances sont passées.", "description": "The body of a warning box shown to a user whose subscription is unpaid."}, "cancellationDate": {"message": "Date d'annulation", "description": "The date header used when a subscription is cancelled."}, "machineAccountsCannotCreate": {"message": "Les comptes de machine ne peuvent pas être créés dans les organisations suspendues. Veuillez contacter le propriétaire de votre organisation pour obtenir de l'aide."}, "machineAccount": {"message": "Compte machine", "description": "A machine user which can be used to automate processes and access secrets in the system."}, "machineAccounts": {"message": "Comptes de machine", "description": "The title for the section that deals with machine accounts."}, "newMachineAccount": {"message": "Nouveau compte machine", "description": "Title for creating a new machine account."}, "machineAccountsNoItemsMessage": {"message": "Créez un nouveau compte machine pour commencer à automatiser l'accès secret.", "description": "Message to encourage the user to start creating machine accounts."}, "machineAccountsNoItemsTitle": {"message": "Rien à afficher pour l'instant", "description": "Title to indicate that there are no machine accounts to display."}, "deleteMachineAccounts": {"message": "Supprimer les comptes de machine", "description": "Title for the action to delete one or multiple machine accounts."}, "deleteMachineAccount": {"message": "Supprimer le compte machine", "description": "Title for the action to delete a single machine account."}, "viewMachineAccount": {"message": "A<PERSON><PERSON><PERSON> le compte machine", "description": "Action to view the details of a machine account."}, "deleteMachineAccountDialogMessage": {"message": "La suppression du compte machine $MACHINE_ACCOUNT$ est permanente et irréversible.", "placeholders": {"machine_account": {"content": "$1", "example": "Machine account name"}}}, "deleteMachineAccountsDialogMessage": {"message": "La suppression des comptes de la machine est permanente et irréversible."}, "deleteMachineAccountsConfirmMessage": {"message": "Supprimer $COUNT$ comptes machines", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "deleteMachineAccountToast": {"message": "Compte de machine supprimé"}, "deleteMachineAccountsToast": {"message": "Comptes de machine supprimés"}, "searchMachineAccounts": {"message": "Rechercher des comptes de machine", "description": "Placeholder text for searching machine accounts."}, "editMachineAccount": {"message": "É<PERSON>er le compte machine", "description": "Title for editing a machine account."}, "machineAccountName": {"message": "Nom du compte machine", "description": "Label for the name of a machine account"}, "machineAccountCreated": {"message": "Compte de machine créé", "description": "Notifies that a new machine account has been created"}, "machineAccountUpdated": {"message": "Compte de la machine mis à jour", "description": "Notifies that a machine account has been updated"}, "projectMachineAccountsDescription": {"message": "Accorder l'accès à ce projet aux comptes de machine."}, "projectMachineAccountsSelectHint": {"message": "Sai<PERSON><PERSON>z ou sélectionnez les comptes de machine"}, "projectEmptyMachineAccountAccessPolicies": {"message": "Ajouter des comptes de machine pour accorder l'accès"}, "machineAccountPeopleDescription": {"message": "Accorder l'accès à ce compte à des groupes ou à des personnes."}, "machineAccountProjectsDescription": {"message": "Assigner des projets à ce compte machine. "}, "createMachineAccount": {"message": "<PERSON><PERSON>er un compte machine"}, "maPeopleWarningMessage": {"message": "La suppression de personnes d'un compte machine ne supprime pas les jetons d'accès qu'ils ont créés. Pour de meilleures pratiques en matière de sécurité, il est recommandé de révoquer les jetons d'accès créés par des personnes supprimées d'un compte machine."}, "smAccessRemovalWarningMaTitle": {"message": "Re<PERSON>rer l'accès à ce compte machine"}, "smAccessRemovalWarningMaMessage": {"message": "Cette action supprimera votre accès au compte de la machine."}, "machineAccountsIncluded": {"message": "$COUNT$ comptes machines inclus", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "additionalMachineAccountCost": {"message": "$COST$ par mois pour des comptes machines supplémentaires", "placeholders": {"cost": {"content": "$1", "example": "$0.50"}}}, "additionalMachineAccounts": {"message": "Comptes de machine supplémentaires"}, "includedMachineAccounts": {"message": "Votre forfait est livré avec $COUNT$ comptes machines.", "placeholders": {"count": {"content": "$1", "example": "50"}}}, "addAdditionalMachineAccounts": {"message": "Vous pouvez ajouter des comptes machines supplémentaires pour $COST$ par mois.", "placeholders": {"cost": {"content": "$1", "example": "$0.50"}}}, "limitMachineAccounts": {"message": "Limiter les comptes de la machine (facultatif)"}, "limitMachineAccountsDesc": {"message": "Définissez une limite pour vos comptes machines. Une fois cette limite atteinte, vous ne pourrez plus créer de nouveaux comptes machines."}, "machineAccountLimit": {"message": "Limite du compte de la machine (facultatif)"}, "maxMachineAccountCost": {"message": "Coût potentiel maximal de compte machine"}, "machineAccountAccessUpdated": {"message": "Accès au compte machine mis à jour"}, "restrictedGroupAccessDesc": {"message": "Vous ne pouvez pas vous ajouter vous-même à un groupe."}, "deleteProvider": {"message": "Supp<PERSON><PERSON> le fournisseur"}, "deleteProviderConfirmation": {"message": "La suppression d'un fournisseur est permanente et irréversible. Entrez votre mot de passe principal pour confirmer la suppression du fournisseur et de toutes les données associées."}, "deleteProviderName": {"message": "Impossible de supprimer $ID$", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "deleteProviderWarningDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON> dissocier tous les clients avant de pouvoir supprimer $ID$.", "placeholders": {"id": {"content": "$1", "example": "<PERSON>"}}}, "providerDeleted": {"message": "Fournisseur supprimé"}, "providerDeletedDesc": {"message": "Le fournisseur et toutes les données associées ont été supprimés."}, "deleteProviderRecoverConfirmDesc": {"message": "Vous avez demandé à supprimer ce fournisseur. Utilisez le bouton ci-dessous pour confirmer."}, "deleteProviderWarning": {"message": "La suppression de votre fournisseur est permanente. Elle ne peut pas être annulée."}, "errorAssigningTargetCollection": {"message": "Erreur lors de l'assignation de la collection cible."}, "errorAssigningTargetFolder": {"message": "Erreur lors de l'assignation du dossier cible."}, "integrationsAndSdks": {"message": "Intégrations & SDKs", "description": "The title for the section that deals with integrations and SDKs."}, "integrations": {"message": "Intégrations"}, "integrationsDesc": {"message": "Synchroniser automatiquement les secrets à partir du Secrets Manager de Bitwarden vers un service tiers."}, "sdks": {"message": "SDK"}, "sdksDesc": {"message": "Util<PERSON>z <PERSON>warden Secrets Manager SDK dans les langages de programmation suivants pour construire vos propres applications."}, "ssoDescStart": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "This represents the beginning of a sentence, broken up to include links. The full sentence will be 'Configure single sign-on for Bitwarden using the implementation guide for your Identity Provider."}, "ssoDescEnd": {"message": "pour Bitwarden en utilisant le guide d'implémentation pour votre Fournisseut d'Identités.", "description": "This represents the end of a sentence, broken up to include links. The full sentence will be 'Configure single sign-on for Bitwarden using the implementation guide for your Identity Provider."}, "userProvisioning": {"message": "Provisionnement de l'utilisateur"}, "scimIntegration": {"message": "SCIM"}, "scimIntegrationDescStart": {"message": "<PERSON><PERSON><PERSON><PERSON> ", "description": "This represents the beginning of a sentence, broken up to include links. The full sentence will be 'Configure SCIM (System for Cross-domain Identity Management) to automatically provision users and groups to Bitwarden using the implementation guide for your Identity Provider"}, "scimIntegrationDescEnd": {"message": "(Système de gestion des identités inter-domaines) pour fournir automatiquement des utilisateurs et des groupes à Bitwarden en utilisant le guide d'implémentation de votre fournisseur d'identité.", "description": "This represents the end of a sentence, broken up to include links. The full sentence will be 'Configure SCIM (System for Cross-domain Identity Management) to automatically provision users and groups to Bitwarden using the implementation guide for your Identity Provider"}, "bwdc": {"message": "Connecteur de répertoire Bitwarden"}, "bwdcDesc": {"message": "Configurez le Connecteur de Répertoire Bitwarden pour fournir automatiquement les utilisateurs et les groupes en utilisant le guide d'implémentation de votre fournisseur d'identité."}, "eventManagement": {"message": "Gestion des événements"}, "eventManagementDesc": {"message": "Intégrez les journaux d'événements de Bitwarden à votre système SIEM (information système et gestion d'événements) en utilisant le guide d'implémentation de votre plate-forme."}, "deviceManagement": {"message": "Gestion des appareils"}, "deviceManagementDesc": {"message": "Configurez la gestion des appareils pour Bitwarden en utilisant le guide d'implémentation pour votre plateforme."}, "deviceIdMissing": {"message": "L'identification de l'appareil est manquante"}, "deviceTypeMissing": {"message": "Le type d'appareil est manquant"}, "deviceCreationDateMissing": {"message": "La date de création de l'appareil est manquante"}, "desktopRequired": {"message": "Ordinateur de bureau requis"}, "reopenLinkOnDesktop": {"message": "Réouvrez ce lien à partir de votre courriel sur un bureau."}, "integrationCardTooltip": {"message": "Lancez le guide d'implémentation $INTEGRATION$.", "placeholders": {"integration": {"content": "$1", "example": "Google"}}}, "smIntegrationTooltip": {"message": "Mettez en place $INTEGRATION$.", "placeholders": {"integration": {"content": "$1", "example": "Google"}}}, "smSdkTooltip": {"message": "Affichez le $SDK$", "placeholders": {"sdk": {"content": "$1", "example": "Rust"}}}, "integrationCardAriaLabel": {"message": "ouvrir le guide d'implémentation $INTEGRATION$ dans un nouvel onglet.", "placeholders": {"integration": {"content": "$1", "example": "google"}}}, "smSdkAriaLabel": {"message": "affichezle dépôt $SDK$ dans un nouvel onglet.", "placeholders": {"sdk": {"content": "$1", "example": "rust"}}}, "smIntegrationCardAriaLabel": {"message": "mettez en place le guide d'implémentation $INTEGRATION$ dans un nouvel onglet.", "placeholders": {"integration": {"content": "$1", "example": "google"}}}, "createNewClientToManageAsProvider": {"message": "Créez une nouvelle organisation de clients à gérer en tant que Fournisseur. Des sièges supplémentaires seront reflétés lors du prochain cycle de facturation."}, "selectAPlan": {"message": "Sélectionnez un plan"}, "thirtyFivePercentDiscount": {"message": "35% de réduction"}, "monthPerMember": {"message": "mois par membre"}, "monthPerMemberBilledAnnually": {"message": "mois par membre facturés annuellement"}, "seats": {"message": "Licences"}, "addOrganization": {"message": "Ajouter une organisation"}, "createdNewClient": {"message": "Nouveau client c<PERSON><PERSON> avec succès"}, "noAccess": {"message": "Au<PERSON>n <PERSON>"}, "collectionAdminConsoleManaged": {"message": "Cette collection n'est accessible qu'à partir de la Console Admin"}, "organizationOptionsMenu": {"message": "Afficher/masquer le Menu de l'Organisation"}, "vaultItemSelect": {"message": "Sélectionner un élément du coffre"}, "collectionItemSelect": {"message": "Sélectionner un élément de la collection"}, "manageBillingFromProviderPortalMessage": {"message": "G<PERSON>rer la facturation depuis le Portail Fournisseur"}, "continueSettingUp": {"message": "Continuer la configuration de Bitwarden"}, "continueSettingUpFreeTrial": {"message": "Continuer à configurer votre essai gratuit de Bitwarden"}, "continueSettingUpPasswordManager": {"message": "Continuer la configuration du Gestionnaire de Mots de Passe Bitwarden"}, "continueSettingUpFreeTrialPasswordManager": {"message": "Continuer à configurer votre essai gratuit du gestionnaire de mots de passe Bitwarden"}, "continueSettingUpSecretsManager": {"message": "Continuez à configurer votre essai gratuit de Secrets Manager de Bitwarden"}, "continueSettingUpFreeTrialSecretsManager": {"message": "Continuez à configurer votre essai gratuit de Bitwarden Secrets Manager"}, "enterTeamsOrgInfo": {"message": "Entrez vos informations d'organisation pour Équipes"}, "enterFamiliesOrgInfo": {"message": "Entrez vos informations d'organisation pour Familles"}, "enterEnterpriseOrgInfo": {"message": "Entrez vos informations d'organisation pour Entreprise"}, "viewItemsIn": {"message": "Voir les éléments dans $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Retour à $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "back": {"message": "Précédent", "description": "Button text to navigate back"}, "removeItem": {"message": "Retirer $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "viewInfo": {"message": "Afficher les informations"}, "viewAccess": {"message": "Afficher les accès"}, "noCollectionsSelected": {"message": "Vous n'avez sélectionné aucune collection."}, "updateName": {"message": "Mettre le nom à jour"}, "updatedOrganizationName": {"message": "Nom de l'organisation mis à jour"}, "providerPlan": {"message": "Service Provider géré"}, "managedServiceProvider": {"message": "Fournisseur de services géré"}, "multiOrganizationEnterprise": {"message": "Entreprise multi-organisation"}, "orgSeats": {"message": "Licences de l'Organisation"}, "providerDiscount": {"message": "Remise de $AMOUNT$%", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "lowKDFIterationsBanner": {"message": "Nombres d'itérations KDF bas. Augmentez vos itérations pour améliorer la sécurité de votre compte."}, "changeKDFSettings": {"message": "Modifier les paramètres KDF"}, "secureYourInfrastructure": {"message": "Sécurisez votre infrastructure"}, "protectYourFamilyOrBusiness": {"message": "Protégez votre famille ou votre entreprise"}, "upgradeOrganizationCloseSecurityGaps": {"message": "Fermer les failles de sécurité avec des rapports de surveillance"}, "upgradeOrganizationCloseSecurityGapsDesc": {"message": "Restez en avance sur les vulnérabilités de sécurité en passant à un plan payant pour une surveillance améliorée."}, "approveAllRequests": {"message": "Approuver toutes les demandes"}, "allLoginRequestsApproved": {"message": "Toutes les demandes de connexion approuvées"}, "payPal": {"message": "PayPal"}, "bitcoin": {"message": "Bitcoin"}, "updatedTaxInformation": {"message": "Informations sur les taxes mises à jour"}, "billingInvalidTaxIdError": {"message": "ID de taxe non valide, si vous pensez qu'il s'agit d'une erreur, veuillez contacter le support."}, "billingTaxIdTypeInferenceError": {"message": "Nous n'avons pu valider votre ID de taxes. Si vous pensez que c'est une erreur, veuillez contacter le support s'il-vous-plaît."}, "billingPreviewInvalidTaxIdError": {"message": "ID de taxe non valide, si vous pensez qu'il s'agit d'une erreur, veuillez contacter le support."}, "billingPreviewInvoiceError": {"message": "Une erreur s'est produite lors de la prévisualisation de la facture. Veuillez réessayer plus tard."}, "unverified": {"message": "Non vérifié"}, "verified": {"message": "Vérifié"}, "viewSecret": {"message": "<PERSON><PERSON><PERSON><PERSON> le secret"}, "noClients": {"message": "Il n'y a aucun client à afficher"}, "providerBillingEmailHint": {"message": "Cette adresse courriel recevra toutes les factures relatives à ce fournisseur", "description": "A hint that shows up on the Provider setup page to inform the admin the billing email will receive the provider's invoices."}, "upgradeOrganizationEnterprise": {"message": "Identifier les risques de sécurité en auditant l'accès des membres"}, "onlyAvailableForEnterpriseOrganization": {"message": "Visualisez rapidement l'accès des membres à travers l'organisation en passant à un plan Enterprise."}, "date": {"message": "Date"}, "exportClientReport": {"message": "Exporter le rapport client"}, "memberAccessReport": {"message": "Accès des membres"}, "memberAccessReportDesc": {"message": "S'assurer que les membres ont accès aux bons identifiants et que leurs comptes sont sécurisés. Utilisez ce rapport pour obtenir un CSV d'accès aux membres et des configurations de compte."}, "memberAccessReportPageDesc": {"message": "Audite les accès des membres de l'organisation au sein des groupes, des collections et des éléments des collections. L'exportation CSV fournit un rapport détaillé par membre comprenant des informations sur les collections autorisées et les configurations de compte."}, "memberAccessReportNoCollection": {"message": "(Aucune collection)"}, "memberAccessReportNoCollectionPermission": {"message": "(Aucune Autorisation de Collection)"}, "memberAccessReportNoGroup": {"message": "(Aucun groupe)"}, "memberAccessReportTwoFactorEnabledTrue": {"message": "Activé"}, "memberAccessReportTwoFactorEnabledFalse": {"message": "Désactivé"}, "memberAccessReportAuthenticationEnabledTrue": {"message": "Activé"}, "memberAccessReportAuthenticationEnabledFalse": {"message": "Désactivé"}, "higherKDFIterations": {"message": "Des itérations KDF plus élevées peuvent aider à protéger votre mot de passe principal contre la force brute d'un assaillant."}, "incrementsOf100,000": {"message": "incréments de 100 000"}, "smallIncrements": {"message": "petits incréments"}, "kdfIterationRecommends": {"message": "Nous vous recommandons 600 000 ou plus"}, "kdfToHighWarningIncreaseInIncrements": {"message": "Pour les appareils plus anciens, régler le nombre d'itérations KDF trop haut peut entraîner des problèmes de performance. Augmentez la valeur dans $VALUE$ et testez vos appareils.", "placeholders": {"value": {"content": "$1", "example": "increments of 100,000"}}}, "providerReinstate": {"message": " Contactez le Support Client pour rétablir votre abonnement."}, "secretPeopleDescription": {"message": "Autoriser les groupes ou les personnes à accéder à ce secret. Les autorisations définies pour les personnes remplaceront les autorisations définies par les groupes."}, "secretPeopleEmptyMessage": {"message": "Ajouter des personnes ou des groupes pour partager l'accès à ce secret"}, "secretMachineAccountsDescription": {"message": "Accorder l'accès aux comptes de machine à ce projet."}, "secretMachineAccountsEmptyMessage": {"message": "Ajouter des comptes de machine pour accorder l'accès à ce secret"}, "smAccessRemovalWarningSecretTitle": {"message": "<PERSON><PERSON>rer l'accès à ce secret"}, "smAccessRemovalSecretMessage": {"message": "Cette action retirera votre accès à ce secret."}, "invoice": {"message": "Facture"}, "unassignedSeatsAvailable": {"message": "Vous avez $SEATS$ licences non-assignées disponibles.", "placeholders": {"seats": {"content": "$1", "example": "10"}}, "description": "A message showing how many unassigned seats are available for a provider."}, "contactYourProviderForAdditionalSeats": {"message": "Contactez l'administrateur de votre fournisseur pour acheter des licences supplémentaires."}, "open": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "The status of an invoice."}, "uncollectible": {"message": "Ne peut pas être collecté", "description": "The status of an invoice."}, "clientDetails": {"message": "Détails Du Client"}, "downloadCSV": {"message": "Télécharger le fichier CSV"}, "monthlySubscriptionUserSeatsMessage": {"message": "Les ajustements à votre abonnement entraîneront des frais au prorata de vos totaux facturés lors de votre prochaine période de facturation. "}, "annualSubscriptionUserSeatsMessage": {"message": "Les ajustements à votre abonnement entraîneront des frais au prorata d'une période de facturation mensuelle. "}, "billingHistoryDescription": {"message": "Téléchargez un fichier CSV pour obtenir les détails du client pour chaque date de facturation. Les frais au prorata ne sont pas inclus dans le fichier CSV et peuvent varier de la facture liée. Pour obtenir les détails de facturation les plus exacts, reportez-vous à vos factures mensuelles.", "description": "A paragraph on the Billing History page of the Provider Portal letting users know they can download a CSV report for their invoices that does not include prorations."}, "noInvoicesToList": {"message": "Il n'y a aucune facture à afficher", "description": "A paragraph on the Billing History page of the Provider Portal letting users know they can download a CSV report for their invoices that does not include prorations."}, "providerClientVaultPrivacyNotification": {"message": "Remarque : Plus tard ce mois-ci, la confidentialité du coffre du client sera améliorée et les membres du fournisseur n'auront plus un accès direct aux éléments du coffre du client. Pour les questions,", "description": "This will be displayed as part of a larger sentence. The whole sentence reads: 'Notice: Later this month, client vault privacy will be improved and provider members will no longer have direct access to client vault items. For questions, please contact Bitwarden support'."}, "contactBitwardenSupport": {"message": "contactez le support Bitwarden.", "description": "This will be displayed as part of a larger sentence. The whole sentence reads: 'Notice: Later this month, client vault privacy will be improved and provider members will no longer have direct access to client vault items. For questions, please contact Bitwarden support'. 'Bitwarden' should not be translated"}, "sponsored": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "licenseAndBillingManagementDesc": {"message": "Après avoir fait des mises à jour sur le serveur cloud de Bitwarden, téléversez votre fichier de licence pour appliquer les modifications les plus récentes."}, "addToFolder": {"message": "Ajouter au dossier"}, "selectFolder": {"message": "Sélectionner un dossier"}, "personalItemTransferWarningSingular": {"message": "1 élément sera transféré définitivement à l'organisation sélectionnée. Vous ne serez plus le propriétaire de cet élément."}, "personalItemsTransferWarningPlural": {"message": "Les éléments $PERSONAL_ITEMS_COUNT$ seront transférés de façon permanente à l'organisation sélectionnée. Vous ne serez plus le propriétaire de ces éléments.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 élément sera transféré définitivement à $ORG$. Vous ne serez plus le propriétaire de cet élément.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ seront transférés à $ORG$ de façon permanente. Vous ne serez plus propriétaire de ces éléments.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "data": {"message": "<PERSON><PERSON><PERSON>"}, "purchasedSeatsRemoved": {"message": "licenses achetées retirées"}, "environmentVariables": {"message": "Variables d'environnement"}, "organizationId": {"message": "ID d’organisation"}, "projectIds": {"message": "ID du projet"}, "projectId": {"message": "ID du projet"}, "projectsAccessedByMachineAccount": {"message": "Les projets suivants peuvent être consultés par ce compte machine."}, "config": {"message": "Configuration"}, "learnMoreAboutEmergencyAccess": {"message": "En savoir plus sur l'accès d'urgence"}, "learnMoreAboutMatchDetection": {"message": "En savoir plus sur la détection de correspondance"}, "learnMoreAboutMasterPasswordReprompt": {"message": "En savoir plus sur la demande de ressaisie du mot de passe principal"}, "learnMoreAboutSearchingYourVault": {"message": "En savoir plus sur la recherche dans votre coffre"}, "learnMoreAboutYourAccountFingerprintPhrase": {"message": "En savoir plus sur la phrase d'empreinte de votre compte"}, "impactOfRotatingYourEncryptionKey": {"message": "Impact de la rotation de votre clé de chiffrement"}, "learnMoreAboutEncryptionAlgorithms": {"message": "En savoir plus sur les algorithmes de chiffrement"}, "learnMoreAboutKDFIterations": {"message": "En savoir plus sur les itérations KDF"}, "learnMoreAboutLocalization": {"message": "En savoir plus sur la localisation"}, "learnMoreAboutWebsiteIcons": {"message": "En savoir plus sur l'utilisation des icônes de site web"}, "learnMoreAboutUserAccess": {"message": "En savoir plus sur l'accès des utilisateurs"}, "learnMoreAboutMemberRoles": {"message": "En savoir plus sur les rôles et les autorisations des membres"}, "whatIsACvvNumber": {"message": "Qu'est-ce qu'un numéro CVV ?"}, "learnMoreAboutApi": {"message": "En savoir plus sur l'API de Bitwarden"}, "fileSend": {"message": "Send d'un fichier"}, "fileSends": {"message": "<PERSON><PERSON><PERSON><PERSON> des Sends"}, "textSend": {"message": "Send d'un texte"}, "textSends": {"message": "<PERSON><PERSON> Sends"}, "includesXMembers": {"message": "pour $COUNT$ membre(s)", "placeholders": {"count": {"content": "$1", "example": "5"}}}, "costPerMember": {"message": "$COST$", "placeholders": {"cost": {"content": "$1", "example": "$3"}}}, "optionalOnPremHosting": {"message": "Auto-hébergement optionnel"}, "upgradeFreeOrganization": {"message": "Mettez à niveau votre organisation $NAME$ ", "placeholders": {"name": {"content": "$1", "example": "Teams"}}}, "includeSsoAuthenticationMessage": {"message": "Authentification SSO"}, "familiesPlanInvLimitReachedManageBilling": {"message": "Les organisations Familles peuvent avoir jusqu'à $SEATCOUNT$ membres. Passez à un plan payant pour inviter plus de membres.", "placeholders": {"seatcount": {"content": "$1", "example": "6"}}}, "familiesPlanInvLimitReachedNoManageBilling": {"message": "Les organisations Familles peuvent avoir jusqu'à $SEATCOUNT$ membres. Contactez le propriétaire de votre organisation pour passer à un plan supérieur.", "placeholders": {"seatcount": {"content": "$1", "example": "6"}}}, "upgradePlans": {"message": "Mettez à niveau votre plan pour inviter des membres et profiter de fonctionnalités de sécurité puissantes."}, "upgradeDiscount": {"message": "Économisez $AMOUNT$%", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "enterprisePlanUpgradeMessage": {"message": "Fonctionnalités avancées pour les grandes organisations"}, "teamsPlanUpgradeMessage": {"message": "Protection résistante pour les équipes en croissance"}, "teamsInviteMessage": {"message": "Invitez un nombre illimité de membres"}, "accessToCreateGroups": {"message": "Accès à la création de groupes"}, "syncGroupsAndUsersFromDirectory": {"message": "Synchroniser les groupes et les utilisateurs à partir d'un répertoire"}, "familyPlanUpgradeMessage": {"message": "Sécurisez les identifiants de votre famille"}, "accessToPremiumFeatures": {"message": "Accède aux fonctionnalités Premium"}, "additionalStorageGbMessage": {"message": "GB de stockage additionnel"}, "sshKeyAlgorithm": {"message": "Algorithme de clé"}, "sshPrivateKey": {"message": "Clé privée"}, "sshPublicKey": {"message": "Clé publique"}, "sshFingerprint": {"message": "Empreinte digitale"}, "sshKeyFingerprint": {"message": "Empreinte digitale"}, "sshKeyPrivateKey": {"message": "Clé privée"}, "sshKeyPublicKey": {"message": "Clé publique"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048 bits"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072 bits"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096 bits"}, "premiumAccounts": {"message": "6 comptes premium"}, "unlimitedSharing": {"message": "Partage illimité"}, "unlimitedCollections": {"message": "Collections illimitées"}, "secureDataSharing": {"message": "Partage sécurisé de donn<PERSON>"}, "eventLogMonitoring": {"message": "Surveillance du journal des événements"}, "directoryIntegration": {"message": "Intégration des répertoires"}, "passwordLessSso": {"message": "SSO sans mot de passe"}, "accountRecovery": {"message": "Récupération de compte"}, "customRoles": {"message": "<PERSON><PERSON><PERSON>"}, "unlimitedSecretsStorage": {"message": "Stockage illimité des secrets"}, "unlimitedUsers": {"message": "Utillisateurs illimités"}, "UpTo50MachineAccounts": {"message": "Jusqu'à 50 comptes machines"}, "UpTo20MachineAccounts": {"message": "Jusqu'à 20 comptes de machine"}, "current": {"message": "Actuel"}, "secretsManagerSubscriptionInfo": {"message": "Votre abonnement au Secrets Manager sera mis à niveau selon le plan sélectionné"}, "bitwardenPasswordManager": {"message": "Gestionnaire de Mots de Passe Bitwarden"}, "secretsManagerComplimentaryPasswordManager": {"message": "Votre abonnement complémentairegratuit d'un an au Gestionnaire de Mots de Passe sera mis à niveau au plan sélectionné. Vous ne serez pas chargé avant la fin de votre période gratuite."}, "fileSavedToDevice": {"message": "<PERSON>chier enregistré sur l'appareil. Gérez à partir des téléchargements de votre appareil."}, "publicApi": {"message": "API publique", "description": "The text, 'API', is an acronym and should not be translated."}, "showCharacterCount": {"message": "Afficher le nombre de caractères"}, "hideCharacterCount": {"message": "Cacher le nombre de caractères"}, "editAccess": {"message": "Modifier l'accès"}, "textHelpText": {"message": "Utiliser des champs de texte pour les données comme les questions de sécurité"}, "hiddenHelpText": {"message": "Utiliser des champs cachés pour des données sensibles comme un mot de passe"}, "checkBoxHelpText": {"message": "Utilisez les cases à cocher si vous souhaitez remplir automatiquement la case à cocher d'un formulaire, comme la case se souvenir du courriel"}, "linkedHelpText": {"message": "Utilisez un champ lié lorsque vous rencontrez des problèmes de saisie automatique pour un site Web spécifique."}, "linkedLabelHelpText": {"message": "Entrez l'identifiant html, le nom, l'étiquette aria ou l'espace réservé du champ."}, "uppercaseDescription": {"message": "Inclure les caractères majuscules", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Inclure des caractères minuscules", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "Inclure des chiffres", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "Inclure des caractères spéciaux", "description": "Full description for the password generator special characters checkbox"}, "addAttachment": {"message": "Ajouter une pièce jointe"}, "maxFileSizeSansPunctuation": {"message": "La taille maximale du fichier est de 500 Mo"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Êtes-vous sûr de vouloir supprimer définitivement cette pièce jointe ?"}, "manageSubscriptionFromThe": {"message": "<PERSON><PERSON>rer l'abonnement à partir du", "description": "This represents the beginning of a sentence. The full sentence will be 'Manage subscription from the Provider Portal', but 'Provider Portal' will be a link and thus cannot be included in the translation file."}, "toHostBitwardenOnYourOwnServer": {"message": "Pour héberger Bitwarden sur votre propre serveur, vous devrez téléverser votre fichier de licence. Pour prendre en charge les abonnements gratuits à Bitwarden Familles et les fonctionnalités de facturation avancées pour votre organisation auto-hébergée, vous devrez configurer la synchronisation automatique dans votre organisation auto-hébergée."}, "selfHostingTitleProper": {"message": "Auto-Hébergement"}, "claim-domain-single-org-warning": {"message": "Réclamer un domaine activera la politique d'organisation unique."}, "single-org-revoked-user-warning": {"message": "Les membres non conformes seront révoqués. Les administrateurs peuvent restaurer les membres une fois qu'ils quittent toutes les autres organisations."}, "deleteOrganizationUser": {"message": "Supprimer $NAME$", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}, "description": "Title for the delete organization user dialog"}}, "deleteOrganizationUserWarningDesc": {"message": "Ceci supprimera définitivement tous les éléments appartenant à $NAME$. Les éléments de la collection ne sont pas impactés.", "description": "Warning description for the delete organization user dialog", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}}}, "deleteManyOrganizationUsersWarningDesc": {"message": "Ceci supprimera définitivement tous les éléments appartenant aux membres suivants. Les éléments de la collection ne sont pas impactés.", "description": "Warning description for the bulk delete organization users dialog"}, "organizationUserDeleted": {"message": "$NAME$ supprimé", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}}}, "organizationUserDeletedDesc": {"message": "L'utilisateur a été retiré de l'organisation et toutes ses données utilisateur associées ont été supprimées."}, "deletedUserId": {"message": "Utilisateur supprimé $ID$ - un propriétaire / administrateur a supprimé le compte utilisateur", "placeholders": {"id": {"content": "$1", "example": "First 8 Character of a GUID"}}}, "userLeftOrganization": {"message": "L'utilisateur $ID$ a quitté l'organisation", "placeholders": {"id": {"content": "$1", "example": "First 8 Character of a GUID"}}}, "suspendedOrganizationTitle": {"message": "$ORGANIZATION$ est suspendue", "placeholders": {"organization": {"content": "$1", "example": "Acme c"}}}, "suspendedUserOrgMessage": {"message": "Contactez le propriétaire de votre organisation pour obtenir de l'aide."}, "suspendedOwnerOrgMessage": {"message": "Pour regagner l'accès à votre organisation, ajoutez un mode de paiement."}, "deleteMembers": {"message": "Supprimer les membres"}, "noSelectedMembersApplicable": {"message": "Cette action n'est applicable à aucun des membres sélectionnés."}, "deletedSuccessfully": {"message": "Supprimé avec succès"}, "freeFamiliesSponsorship": {"message": "Supprimer le parrainage gratuit de Bitwarden Familles"}, "freeFamiliesSponsorshipPolicyDesc": {"message": "N'autorisez pas les membres à échanger un plan Familles par l'intermédiaire de cette organisation."}, "verifyBankAccountWithStatementDescriptorWarning": {"message": "Le paiement avec un compte bancaire est seulement disponible pour les clients aux États-Unis. Vous devrez procéder à la vérification de votre compte bancaire. Nous effectuerons un micro-dépôt dans les 2 prochains jours ouvrables. Entrez le code du descripteur de relevé de ce dépôt sur la page de facturation de l'organisation pour compléter la vérification du compte bancaire. Si vous ne complétez pas la vérification de votre compte bancaire, cela résultera en un paiement manqué et votre abonnement sera suspendu."}, "verifyBankAccountWithStatementDescriptorInstructions": {"message": "Nous avons effectué un micro-dépôt dans votre compte bancaire (cela peut prendre jusqu'à 2 jours ouvrables). Entrez le code à six chiffres commençant par 'SM' trouvé dans la description du dépôt. Si vous ne complétez pas la vérification de votre compte bancaire, cela résultera en un paiement manqué et votre abonnement sera suspendu."}, "descriptorCode": {"message": "Code descripteur"}, "cannotRemoveViewOnlyCollections": {"message": "Vous ne pouvez pas supprimer des collections avec les autorisations d'affichage uniquement : $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "removeMembers": {"message": "Retirer des membres"}, "devices": {"message": "Appareils"}, "deviceListDescription": {"message": "Votre compte a été connecté à chacun des appareils ci-dessous. Si vous ne reconnaissez pas un appareil, supprimez-le maintenant."}, "deviceListDescriptionTemp": {"message": "Votre compte a été connecté à chacun des appareils ci-dessous."}, "claimedDomains": {"message": "Domaines réclamés"}, "claimDomain": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> le domaine"}, "reclaimDomain": {"message": "Récupérer le domaine"}, "claimDomainNameInputHint": {"message": "Exemple : mondomaine.com. Les sous-domaines nécessitent des entrées séparées pour être réclamés."}, "automaticClaimedDomains": {"message": "Domaines Réclamés Automatiquement"}, "automaticDomainClaimProcess": {"message": "Bitwarden tentera de récupérer le domaine 3 fois pendant les 72 premières heures. Si le domaine ne peut pas être réclamé, vérifiez l'enregistrement DNS dans votre hôte et réclamez manuellement. Le domaine sera supprimé de votre organisation dans 7 jours s'il n'est pas réclamé."}, "domainNotClaimed": {"message": "$DOMAIN$ non réclamé. Vérifiez vos enregistrements DNS.", "placeholders": {"DOMAIN": {"content": "$1", "example": "bitwarden.com"}}}, "domainStatusClaimed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "domainStatusUnderVerification": {"message": "En cours de vérification"}, "claimedDomainsDesc": {"message": "Réclamez un domaine pour posséder tous les comptes membres dont l'adresse courriel correspond au domaine. Les membres pourront éviter l'identifiant SSO lors de la connexion. Les administrateurs seront également en mesure de supprimer les comptes de membre."}, "invalidDomainNameClaimMessage": {"message": "L'entrée n'est pas un format valide. Format: mondomaine.com. Les sous-domaines nécessitent des entrées séparées pour être réclamés."}, "domainClaimedEvent": {"message": "$DOMAIN$ réclamé", "placeholders": {"DOMAIN": {"content": "$1", "example": "bitwarden.com"}}}, "domainNotClaimedEvent": {"message": "$DOMAIN$ non réclamé", "placeholders": {"DOMAIN": {"content": "$1", "example": "bitwarden.com"}}}, "updatedRevokeSponsorshipConfirmationForSentSponsorship": {"message": "Si vous supprimez $EMAIL$, la commandite pour ce plan Familles ne peut pas être échangée. Êtes-vous sûr de vouloir continuer ?", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "updatedRevokeSponsorshipConfirmationForAcceptedSponsorship": {"message": "Si vous supprimez $EMAIL$, la commandite pour ce plan Familles prendra fin et la méthode de paiement enregistrée sera facturée $40 + taxe applicable le $DATE$. Vous ne pourrez pas réclamer un nouveau parrainage avant $DATE$. Êtes-vous sûr de vouloir continuer ?", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "date": {"content": "$2", "example": "12/10/2024"}}}, "domainClaimed": {"message": "Domaine r<PERSON>"}, "organizationNameMaxLength": {"message": "Le nom de l'organisation ne doit pas dépasser 50 caractères."}, "rotationCompletedTitle": {"message": "Rotation de la clé réussie"}, "rotationCompletedDesc": {"message": "Votre mot de passe principal et vos clés de chiffrement ont été mis à jour. Vos autres appareils ont été déconnectés."}, "trustUserEmergencyAccess": {"message": "Faire confiance et confirmer l'utilisateur"}, "trustOrganization": {"message": "Faire confiance à l'organisation"}, "trust": {"message": "Faire confiance"}, "doNotTrust": {"message": "Ne pas faire confiance"}, "organizationNotTrusted": {"message": "L'organisation n'est pas fiable"}, "emergencyAccessTrustWarning": {"message": "Pour la sécurité de votre compte, confirmez seulement si vous avez accordé l'accès d'urgence à cet utilisateur et que sa phrase d'empreinte correspond à ce qui est affiché dans son compte"}, "orgTrustWarning": {"message": "Pour la sécurité de votre compte, continuez seulement si vous êtes un membre de cette organisation, avez la récupération de compte activée et que la phrase d'empreinte correspond à celle de l'organisation."}, "orgTrustWarning1": {"message": "Cette organisation a une politique de sécurité Entreprise qui vous inscrira dans la récupération de votre compte. L'inscription permettra aux administrateurs de l'organisation de changer votre mot de passe. Continuez seulement si vous reconnaissez cette organisation et que la phrase d'empreinte affichée ci-dessous correspond à l'empreinte de l'organisation."}, "trustUser": {"message": "Faire confiance à l'utilisateur"}, "sshKeyWrongPassword": {"message": "Le mot de passe que vous avez entré est incorrect."}, "importSshKey": {"message": "Importer"}, "confirmSshKeyPassword": {"message": "Confirmez le mot de passe"}, "enterSshKeyPasswordDesc": {"message": "Entrez le mot de passe pour la clé SSH."}, "enterSshKeyPassword": {"message": "Entrer le mot de passe"}, "invalidSshKey": {"message": "La clé SSH est invalide"}, "sshKeyTypeUnsupported": {"message": "Le type de clé SSH n'est pas pris en charge"}, "importSshKeyFromClipboard": {"message": "Importer une clé depuis le presse-papiers"}, "sshKeyImported": {"message": "Clé SSH importée avec succès"}, "copySSHPrivateKey": {"message": "Co<PERSON>r la clé privée"}, "openingExtension": {"message": "Ouverture de l'extension de navigateur Bitwarden"}, "somethingWentWrong": {"message": "Quelquechose n'a pas fonctionné..."}, "openingExtensionError": {"message": "Nous avons eu des difficultés à ouvrir l'extension de navigateur Bitwarden. Cliquez sur le bouton pour l'ouvrir maintenant."}, "openExtension": {"message": "Ouvrir l'extension"}, "doNotHaveExtension": {"message": "Vous n'avez pas l'extension de navigateur Bitwarden?"}, "installExtension": {"message": "Installer l'extension"}, "openedExtension": {"message": "Extension de navigateur ouverte"}, "openedExtensionViewAtRiskPasswords": {"message": "Ouverture réussie de l'extension du navigateur Bitwarden. V<PERSON> pouvez maintenant revoir vos mots de passe à risque."}, "openExtensionManuallyPart1": {"message": "Nous avons eu des difficultés à ouvrir l'extension de navigateur Bitwarden. Ouvrez l'icône Bitwarden", "description": "This will be used as part of a larger sentence, broken up to include the Bitwarden icon. The full sentence will read 'We had trouble opening the Bitwarden browser extension. Open the Bitwarden icon [Bitwarden Icon] from the toolbar.'"}, "openExtensionManuallyPart2": {"message": "de la barre d'outils.", "description": "This will be used as part of a larger sentence, broken up to include the Bitwarden icon. The full sentence will read 'We had trouble opening the Bitwarden browser extension. Open the Bitwarden icon [Bitwarden Icon] from the toolbar.'"}, "resellerRenewalWarningMsg": {"message": "Votre abonnement sera renouvelé bientôt. Pour assurer un service ininterrompu, contactez $RESELLER$ pour confirmer votre renouvellement avant le $RENEWAL_DATE$.", "placeholders": {"reseller": {"content": "$1", "example": "Reseller Name"}, "renewal_date": {"content": "$2", "example": "01/01/2024"}}}, "resellerOpenInvoiceWarningMgs": {"message": "Une facture pour votre abonnement a été émise le $ISSUED_DATE$. Pour assurer un service ininterrompu, contactez $RESELLER$ pour confirmer votre renouvellement avant le $DUE_DATE$.", "placeholders": {"reseller": {"content": "$1", "example": "Reseller Name"}, "issued_date": {"content": "$2", "example": "01/01/2024"}, "due_date": {"content": "$3", "example": "01/15/2024"}}}, "resellerPastDueWarningMsg": {"message": "La facture de votre abonnement n'a pas été payée. Pour assurer un service ininterrompu, contactez $RESELLER$ pour confirmer votre renouvellement avant $GRACE_PERIOD_END$.", "placeholders": {"reseller": {"content": "$1", "example": "Reseller Name"}, "grace_period_end": {"content": "$2", "example": "02/14/2024"}}}, "restartOrganizationSubscription": {"message": "L'abonnement à l'organisation a été redémarré"}, "restartSubscription": {"message": "Redémarrez votre abonnement"}, "suspendedManagedOrgMessage": {"message": "Contactez $PROVIDER$ pour obtenir de l'aide.", "placeholders": {"provider": {"content": "$1", "example": "Acme c"}}}, "accountDeprovisioningNotification": {"message": "Les administrateurs ont maintenant la possibilité de supprimer les comptes de membre qui appartiennent à un domaine revendiqué."}, "deleteManagedUserWarningDesc": {"message": "Cette action supprimera le compte du membre, incluant tous les éléments de son coffre. <PERSON><PERSON> remplace l'action précédente de Supprimer."}, "deleteManagedUserWarning": {"message": "Supprimer est une nouvelle action !"}, "seatsRemaining": {"message": "Vous avez $REMAINING$ places restantes sur $TOTAL$ attribuées à cette organisation. Contactez votre fournisseur pour gérer votre abonnement.", "placeholders": {"remaining": {"content": "$1", "example": "5"}, "total": {"content": "$2", "example": "10"}}}, "existingOrganization": {"message": "Organisation existante"}, "selectOrganizationProviderPortal": {"message": "Sélectionnez une organisation à ajouter à votre Portail fournisseur."}, "noOrganizations": {"message": "Il n'y a aucune organisation à lister"}, "yourProviderSubscriptionCredit": {"message": "Votre abonnement à un fournisseur recevra un crédit pour tout temps restant dans l'abonnement de l'organisation."}, "doYouWantToAddThisOrg": {"message": "Voulez-vous ajouter cette organisation à $PROVIDER$?", "placeholders": {"provider": {"content": "$1", "example": "Cool MSP"}}}, "addedExistingOrganization": {"message": "Organisation existante ajoutée"}, "assignedExceedsAvailable": {"message": "Les places assignées dépassent les places disponibles."}, "userkeyRotationDisclaimerEmergencyAccessText": {"message": "Phrase d'empreinte des $NUM_USERS$ contacts pour lesquels vous avez activé l'accès d'urgence.", "placeholders": {"num_users": {"content": "$1", "example": "5"}}}, "userkeyRotationDisclaimerAccountRecoveryOrgsText": {"message": "Phrase d'empreinte de l'organisation $ORG_NAME$ pour laquelle vous avez activé la récupération de compte.", "placeholders": {"org_name": {"content": "$1", "example": "My org"}}}, "userkeyRotationDisclaimerDescription": {"message": "Faire la rotation de vos clés exige que vous fassiez confiance aux clés de toutes les organisations qui peuvent récupérer votre compte et à tous les contacts à qui vous avez autorsé l'accès d'urgence. Pour continuer, assurez-vous que vous pouvez vérifier ce qui suit :"}, "userkeyRotationDisclaimerTitle": {"message": "Clés de chiffrement non fiables"}, "changeAtRiskPassword": {"message": "Changer le mot de passe à risque"}, "removeUnlockWithPinPolicyTitle": {"message": "Supprimer Déverrouiller avec un NIP"}, "removeUnlockWithPinPolicyDesc": {"message": "Ne pas autoriser les membres à déverrouiller leur compte avec un NIP."}, "upgradeForFullEventsMessage": {"message": "Les journaux d'événements ne sont pas conservés pour votre organisation. Mettez à niveau vers un forfait Équipes ou Entreprise pour obtenir un accès complet aux journaux d'événements de l'organisation."}, "upgradeEventLogTitleMessage": {"message": "Mettez à niveau pour voir les journaux d'événements de votre organisation."}, "upgradeEventLogMessage": {"message": "Ces événements sont des exemples et ne reflètent pas les événements réels au sein de votre organisation Bitwarden."}, "cannotCreateCollection": {"message": "Les organisations gratuites peuvent avoir jusqu'à 2 collections. Passez à une offre payante pour ajouter plus de collections."}, "businessUnit": {"message": "Unité d'affaires"}, "businessUnits": {"message": "Unités d’affaires"}, "newBusinessUnit": {"message": "Nouvelle unité d'affaires"}, "sendsTitleNoItems": {"message": "Envoyez des informations sensibles, en toute sécurité", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsBodyNoItems": {"message": "Partagez des fichiers et des données en toute sécurité avec n'importe qui, sur n'importe quelle plateforme. Vos informations resteront chiffrées de bout en bout tout en limitant l'exposition.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "generatorNudgeTitle": {"message": "Créer rapidement des mots de passe"}, "generatorNudgeBodyOne": {"message": "C<PERSON>ez facilement des mots de passe forts et uniques en cliquant sur", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyTwo": {"message": "pour vous aider à garder vos identifiants sécuritaires.", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyAria": {"message": "Créez facilement des mots de passe forts et uniques en cliquant sur le bouton Générer un mot de passe pour vous aider à garder vos identifiants sécuritaires.", "description": "Aria label for the body content of the generator nudge"}, "newLoginNudgeTitle": {"message": "Gagnez du temps avec le remplissage automatique"}, "newLoginNudgeBodyOne": {"message": "Inclure un", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Site internet", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "pour que cet identifiant apparaisse comme une suggestion de remplissage automatique.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Paiement en ligne transparent"}, "newCardNudgeBody": {"message": "Avec les cartes, remplissez facilement les formulaires de paiement en toute sécurité et avec précision."}, "newIdentityNudgeTitle": {"message": "Simplifier la création de comptes"}, "newIdentityNudgeBody": {"message": "Avec les identités, remplissez rapidement de longs formulaires d'inscription ou de contact."}, "newNoteNudgeTitle": {"message": "Gardez vos données sensibles en sécurité"}, "newNoteNudgeBody": {"message": "Avec les notes, conservez en toute sécurité des données sensibles comme les informations bancaires ou d'assurances."}, "newSshNudgeTitle": {"message": "Accès SSH convivial pour les développeurs"}, "newSshNudgeBodyOne": {"message": "Enregistrez vos clés et connectez-vous avec l'agent SSH pour une authentification rapide et chiffrée.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "En savoir plus sur l'agent SSH", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "restart": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "verifyProviderBankAccountWithStatementDescriptorWarning": {"message": "Le paiement avec un compte bancaire est seulement disponible pour les clients aux États-Unis. Vous devrez procéder à la vérification de votre compte bancaire. Nous effectuerons un micro-dépôt dans les 1-2 prochains jours ouvrables. Entrez le code de la transaction de ce dépôt sur la page d'abonnement de votre fournisseur pour compléter la vérification du compte bancaire. Si vous ne complétez pas la vérification de votre compte bancaire résultera en un paiement manqué et votre abonnement sera suspendu."}, "clickPayWithPayPal": {"message": "Veuillez cliquer sur le bouton Payer avec PayPal pour ajouter votre mode de paiement."}, "revokeActiveSponsorshipConfirmation": {"message": "Si vous supprimez $EMAIL$, le parrainage pour ce plan Familles prendra fin. Un siège au sein de votre organisation sera disponible pour les membres ou les parrainages après la date de renouvellement de l'organisation parrainée le $DATE$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "date": {"content": "$2", "example": "12/31/2024"}}}, "billingAddressRequiredToAddCredit": {"message": "L'adresse de facturation est requise pour ajouter du crédit.", "description": "Error message shown when trying to add credit to a trialing organization without a billing address."}}