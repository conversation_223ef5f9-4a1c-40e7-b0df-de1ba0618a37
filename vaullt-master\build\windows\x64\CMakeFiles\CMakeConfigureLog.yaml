
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Version MSBuild 17.14.18+a338add32 pour .NET Framework
      La g├®n├®ration a d├®marr├® 28/08/2025 21:15:59.
      
      Projet "C:\\Users\\<USER>\\Documents\\VaAulLT\\vaullt-master\\build\\windows\\x64\\CMakeFiles\\3.31.6-msvc6\\CompilerIdCXX\\CompilerIdCXX.vcxproj" sur le n┼ôud 1 (cibles par d├®faut).
      PrepareForBuild:
        Cr├®ation du r├®pertoire "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Cr├®ation du r├®pertoire "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Cr├®ation de "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild", car "AlwaysCreate" a ├®t├® sp├®cifi├®.
        Mise ├á jour de l'horodatage "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\Documents\\VaAulLT\\vaullt-master\\build\\windows\\x64\\CMakeFiles\\3.31.6-msvc6\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Suppression du fichier "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Mise ├á jour de l'horodatage "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      G├®n├®ration du projet "C:\\Users\\<USER>\\Documents\\VaAulLT\\vaullt-master\\build\\windows\\x64\\CMakeFiles\\3.31.6-msvc6\\CompilerIdCXX\\CompilerIdCXX.vcxproj" termin├®e (cibles par d├®faut).
      
      La g├®n├®ration a r├®ussi.
          0 Avertissement(s)
          0 Erreur(s)
      
      Temps ├®coul├® 00:00:01.50
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/CMakeFiles/3.31.6-msvc6/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/CMakeFiles/CMakeScratch/TryCompile-gmpl7a"
      binary: "C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/CMakeFiles/CMakeScratch/TryCompile-gmpl7a"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/CMakeFiles/CMakeScratch/TryCompile-gmpl7a'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_9796b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Version MSBuild 17.14.18+a338add32 pour .NET Framework
        La g├®n├®ration a d├®marr├® 28/08/2025 21:16:01.
        
        Projet "C:\\Users\\<USER>\\Documents\\VaAulLT\\vaullt-master\\build\\windows\\x64\\CMakeFiles\\CMakeScratch\\TryCompile-gmpl7a\\cmTC_9796b.vcxproj" sur le n┼ôud 1 (cibles par d├®faut).
        PrepareForBuild:
          Cr├®ation du r├®pertoire "cmTC_9796b.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Cr├®ation du r├®pertoire "C:\\Users\\<USER>\\Documents\\VaAulLT\\vaullt-master\\build\\windows\\x64\\CMakeFiles\\CMakeScratch\\TryCompile-gmpl7a\\Debug\\".
          Cr├®ation du r├®pertoire "cmTC_9796b.dir\\Debug\\cmTC_9796b.tlog\\".
        InitializeBuildStatus:
          Cr├®ation de "cmTC_9796b.dir\\Debug\\cmTC_9796b.tlog\\unsuccessfulbuild", car "AlwaysCreate" a ├®t├® sp├®cifi├®.
          Mise ├á jour de l'horodatage "cmTC_9796b.dir\\Debug\\cmTC_9796b.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_9796b.dir\\Debug\\\\" /Fd"cmTC_9796b.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35214 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_9796b.dir\\Debug\\\\" /Fd"cmTC_9796b.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Documents\\VaAulLT\\vaullt-master\\build\\windows\\x64\\CMakeFiles\\CMakeScratch\\TryCompile-gmpl7a\\Debug\\cmTC_9796b.exe" /INCREMENTAL /ILK:"cmTC_9796b.dir\\Debug\\cmTC_9796b.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/CMakeFiles/CMakeScratch/TryCompile-gmpl7a/Debug/cmTC_9796b.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/CMakeFiles/CMakeScratch/TryCompile-gmpl7a/Debug/cmTC_9796b.lib" /MACHINE:X64  /machine:x64 cmTC_9796b.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_9796b.vcxproj -> C:\\Users\\<USER>\\Documents\\VaAulLT\\vaullt-master\\build\\windows\\x64\\CMakeFiles\\CMakeScratch\\TryCompile-gmpl7a\\Debug\\cmTC_9796b.exe
        FinalizeBuildStatus:
          Suppression du fichier "cmTC_9796b.dir\\Debug\\cmTC_9796b.tlog\\unsuccessfulbuild".
          Mise ├á jour de l'horodatage "cmTC_9796b.dir\\Debug\\cmTC_9796b.tlog\\cmTC_9796b.lastbuildstate".
        G├®n├®ration du projet "C:\\Users\\<USER>\\Documents\\VaAulLT\\vaullt-master\\build\\windows\\x64\\CMakeFiles\\CMakeScratch\\TryCompile-gmpl7a\\cmTC_9796b.vcxproj" termin├®e (cibles par d├®faut).
        
        La g├®n├®ration a r├®ussi.
            0 Avertissement(s)
            0 Erreur(s)
        
        Temps ├®coul├® 00:00:01.58
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35214.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
