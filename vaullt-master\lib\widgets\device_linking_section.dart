import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import '../providers/vaultwarden_provider.dart';

/// Widget for device linking functionality with mDNS and QR codes
class DeviceLinkingSection extends StatefulWidget {
  const DeviceLinkingSection({super.key});

  @override
  State<DeviceLinkingSection> createState() => _DeviceLinkingSectionState();
}

class _DeviceLinkingSectionState extends State<DeviceLinkingSection> {
  bool _showQrCode = false;
  String? _qrData;

  @override
  Widget build(BuildContext context) {
    return Consumer<VaultwardenProvider>(
      builder: (context, provider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.devices,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Device Linking',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // mDNS Status
                _buildMdnsStatus(provider),
                const SizedBox(height: 16),
                
                // Local Domain Info
                if (provider.localDomainName != null) ...[
                  _buildDomainInfo(provider),
                  const SizedBox(height: 16),
                ],
                
                // Action Buttons
                _buildActionButtons(provider),
                
                // QR Code Display
                if (_showQrCode && _qrData != null) ...[
                  const SizedBox(height: 16),
                  _buildQrCodeDisplay(),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMdnsStatus(VaultwardenProvider provider) {
    final isAdvertising = provider.isMdnsAdvertising;
    final isRunning = provider.isRunning;
    
    return Row(
      children: [
        Icon(
          isAdvertising ? Icons.wifi : Icons.wifi_off,
          color: isAdvertising ? Colors.green : Colors.grey,
          size: 20,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            isRunning
                ? (isAdvertising 
                    ? 'Broadcasting on network - devices can discover this service'
                    : 'Network discovery unavailable')
                : 'Start Vaultwarden to enable network discovery',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: isAdvertising ? Colors.green : Colors.grey[600],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDomainInfo(VaultwardenProvider provider) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Local Domain',
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: Text(
                  provider.localDomainName!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontFamily: 'monospace',
                  ),
                ),
              ),
              IconButton(
                onPressed: () => _copyToClipboard(provider.localDomainName!),
                icon: const Icon(Icons.copy, size: 18),
                tooltip: 'Copy domain name',
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Mobile devices on the same network can connect using this domain name.',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(VaultwardenProvider provider) {
    final canGenerateQr = provider.isRunning && 
                         provider.localDomainName != null && 
                         provider.sshPublicKey != null;

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        ElevatedButton.icon(
          onPressed: canGenerateQr ? _generateQrCode : null,
          icon: const Icon(Icons.qr_code),
          label: const Text('Generate QR Code'),
        ),
        OutlinedButton.icon(
          onPressed: provider.isInitialized ? _regenerateSshKeys : null,
          icon: const Icon(Icons.key),
          label: const Text('Regenerate SSH Keys'),
        ),
        if (provider.localDomainName != null)
          OutlinedButton.icon(
            onPressed: _shareConnectionInfo,
            icon: const Icon(Icons.share),
            label: const Text('Share Info'),
          ),
      ],
    );
  }

  Widget _buildQrCodeDisplay() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          Text(
            'Scan with VaAulLT Mobile App',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Center(
            child: QrImageView(
              data: _qrData!,
              version: QrVersions.auto,
              size: 200.0,
              backgroundColor: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton.icon(
                onPressed: () => _copyToClipboard(_qrData!),
                icon: const Icon(Icons.copy),
                label: const Text('Copy Data'),
              ),
              TextButton.icon(
                onPressed: () => setState(() => _showQrCode = false),
                icon: const Icon(Icons.close),
                label: const Text('Close'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _generateQrCode() async {
    final provider = Provider.of<VaultwardenProvider>(context, listen: false);
    
    try {
      final qrData = await provider.generateQrCodeData();
      if (qrData != null) {
        setState(() {
          _qrData = qrData;
          _showQrCode = true;
        });
      } else {
        _showErrorSnackBar('Failed to generate QR code');
      }
    } catch (e) {
      _showErrorSnackBar('Error generating QR code: $e');
    }
  }

  Future<void> _regenerateSshKeys() async {
    final provider = Provider.of<VaultwardenProvider>(context, listen: false);
    
    final confirmed = await _showConfirmationDialog(
      'Regenerate SSH Keys',
      'This will invalidate all existing mobile connections. Continue?',
    );
    
    if (confirmed) {
      try {
        await provider.regenerateSshKeys();
        setState(() {
          _showQrCode = false;
          _qrData = null;
        });
        _showSuccessSnackBar('SSH keys regenerated successfully');
      } catch (e) {
        _showErrorSnackBar('Failed to regenerate SSH keys: $e');
      }
    }
  }

  void _shareConnectionInfo() {
    final provider = Provider.of<VaultwardenProvider>(context, listen: false);
    final summary = provider.getConnectionSummary();
    _copyToClipboard(summary);
    _showSuccessSnackBar('Connection info copied to clipboard');
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    _showSuccessSnackBar('Copied to clipboard');
  }

  Future<bool> _showConfirmationDialog(String title, String message) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Continue'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
