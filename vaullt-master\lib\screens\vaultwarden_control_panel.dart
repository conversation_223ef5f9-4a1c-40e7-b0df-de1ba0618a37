import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/vaultwarden_provider.dart';
import '../widgets/status_card.dart';
import '../widgets/control_buttons.dart';
import '../widgets/configuration_section.dart';
import '../widgets/quick_actions_section.dart';

class VaultwardenControlPanel extends StatelessWidget {
  const VaultwardenControlPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Vaullt - Vaultwarden Manager'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<VaultwardenProvider>().refreshStatus();
            },
            tooltip: 'Refresh Status',
          ),
        ],
      ),
      body: Consumer<VaultwardenProvider>(
        builder: (context, provider, child) {
          if (!provider.isInitialized) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Initializing Vaultwarden Manager...'),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Status Card
                const StatusCard(),
                
                const SizedBox(height: 20),
                
                // Control Buttons
                const ControlButtons(),
                
                const SizedBox(height: 24),
                
                // Configuration Section
                const ConfigurationSection(),
                
                const SizedBox(height: 24),
                
                // Quick Actions
                const QuickActionsSection(),
                
                // Error message if any
                if (provider.errorMessage != null) ...[
                  const SizedBox(height: 16),
                  Card(
                    color: Theme.of(context).colorScheme.errorContainer,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error,
                            color: Theme.of(context).colorScheme.error,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              provider.errorMessage!,
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.onErrorContainer,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }
}
