import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:nsd/nsd.dart';
import 'package:network_info_plus/network_info_plus.dart';

/// Service for mDNS advertising and discovery
class MdnsService {
  static const String _serviceType = '_vaullt._tcp';
  static const String _serviceName = 'vaullt';
  static const String _domain = 'local';
  
  Registration? _registration;
  bool _isAdvertising = false;
  String? _localDomainName;
  
  // Getters
  bool get isAdvertising => _isAdvertising;
  String? get localDomainName => _localDomainName;
  
  /// Start advertising the service via mDNS
  Future<void> startAdvertising({
    required int port,
    required String sshPublicKey,
    Map<String, String>? additionalAttributes,
  }) async {
    try {
      if (_isAdvertising) {
        await stopAdvertising();
      }
      
      // Get local IP address
      final networkInfo = NetworkInfo();
      final wifiIP = await networkInfo.getWifiIP();
      final localIP = wifiIP ?? '127.0.0.1';
      
      // Create service attributes
      final attributes = <String, String>{
        'version': '1.0',
        'service': 'vaultwarden',
        'ssh_key': sshPublicKey,
        'ip': localIP,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
        ...?additionalAttributes,
      };

      // Convert string attributes to Uint8List for nsd package
      final txtAttributes = <String, Uint8List>{};
      for (final entry in attributes.entries) {
        txtAttributes[entry.key] = Uint8List.fromList(utf8.encode(entry.value));
      }

      // Create service info
      final service = Service(
        name: _serviceName,
        type: _serviceType,
        port: port,
        host: await _getHostname(),
        txt: txtAttributes,
      );
      
      debugPrint('Starting mDNS advertising for $_serviceName.$_serviceType.$_domain on port $port');
      
      // Register the service
      _registration = await register(service);
      _isAdvertising = true;
      _localDomainName = '$_serviceName.$_domain';
      
      debugPrint('mDNS service registered successfully as $_localDomainName');
      
    } catch (e) {
      debugPrint('Failed to start mDNS advertising: $e');
      _isAdvertising = false;
      _localDomainName = null;
      rethrow;
    }
  }
  
  /// Stop advertising the service
  Future<void> stopAdvertising() async {
    try {
      if (_registration != null) {
        debugPrint('Stopping mDNS advertising');
        await unregister(_registration!);
        _registration = null;
      }
      _isAdvertising = false;
      _localDomainName = null;
      debugPrint('mDNS advertising stopped');
    } catch (e) {
      debugPrint('Error stopping mDNS advertising: $e');
      // Don't rethrow as this is cleanup
    }
  }
  
  /// Update service attributes (e.g., when port changes)
  Future<void> updateService({
    required int port,
    required String sshPublicKey,
    Map<String, String>? additionalAttributes,
  }) async {
    if (_isAdvertising) {
      await stopAdvertising();
      await startAdvertising(
        port: port,
        sshPublicKey: sshPublicKey,
        additionalAttributes: additionalAttributes,
      );
    }
  }
  
  /// Get the hostname for this device
  Future<String> _getHostname() async {
    try {
      // Try to get a meaningful hostname
      final hostname = Platform.localHostname;
      if (hostname.isNotEmpty && hostname != 'localhost') {
        return hostname;
      }
      
      // Fallback to a generated name
      return 'vaullt-desktop';
    } catch (e) {
      debugPrint('Error getting hostname: $e');
      return 'vaullt-desktop';
    }
  }
  
  /// Discover other VaAulLT services on the network
  Future<List<Service>> discoverServices({Duration timeout = const Duration(seconds: 10)}) async {
    try {
      debugPrint('Starting mDNS discovery for $_serviceType services');
      
      final discovery = await startDiscovery(_serviceType, ipLookupType: IpLookupType.any);
      final services = <Service>[];
      
      final completer = Completer<List<Service>>();
      Timer? timeoutTimer;
      
      // Set up timeout
      timeoutTimer = Timer(timeout, () {
        if (!completer.isCompleted) {
          completer.complete(services);
        }
      });
      
      // Listen for discovered services
      discovery.addServiceListener((service, status) {
        if (status == ServiceStatus.found) {
          debugPrint('Discovered service: ${service.name}');
          services.add(service);
        }
      });
      
      final result = await completer.future;
      
      // Cleanup
      timeoutTimer.cancel();
      await stopDiscovery(discovery);
      
      debugPrint('Discovery completed, found ${result.length} services');
      return result;
      
    } catch (e) {
      debugPrint('Error during service discovery: $e');
      return [];
    }
  }
  
  /// Dispose of the service
  Future<void> dispose() async {
    await stopAdvertising();
  }
}
