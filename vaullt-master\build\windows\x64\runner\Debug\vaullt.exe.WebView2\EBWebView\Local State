{"accessibility": {"captions": {"common_models_path": "", "soda_binary_path": ""}}, "autofill": {"ablation_seed": "CVor7zO/FQg="}, "breadcrumbs": {"enabled": true, "enabled_time": "13400882396295247"}, "default_browser": {"browser_name_enum": 0}, "domain_actions_config": "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", "edge": {"mitigation_manager": {"renderer_app_container_compatible_count": 4, "renderer_code_integrity_compatible_count": 4}, "tab_stabs": {"closed_without_unfreeze_never_unfrozen": 0, "closed_without_unfreeze_previously_unfrozen": 0, "discard_without_unfreeze_never_unfrozen": 0, "discard_without_unfreeze_previously_unfrozen": 0}, "tab_stats": {"frozen_daily": 0, "unfrozen_daily": 0}}, "edge_ci": {"num_healthy_browsers_since_failure": 1}, "hardware_acceleration_mode_previous": true, "legacy": {"profile": {"name": {"migrated": true}}}, "local": {"password_hash_data_list": []}, "network_time": {"network_time_mapping": {"local": 1756408798152.958, "network": 1756408799000.0, "ticks": **********.0, "uncertainty": 1465684.0}}, "optimization_guide": {"model_execution": {"last_usage_by_feature": {}}, "model_store_metadata": {}, "on_device": {"last_version": "139.0.3405.119", "model_crash_count": 0}}, "os_crypt": {"audit_enabled": true, "encrypted_key": "RFBBUEkBAAAA0Iyd3wEV0RGMegDAT8KX6wEAAABYGLvdIU0rRKTtMtqznuwsEAAAAB4AAABNAGkAYwByAG8AcwBvAGYAdAAgAEUAZABnAGUAAAAQZgAAAAEAACAAAADG2XnzFFgE4WWo/3NaswAjWRORhwiuhyPc2HG1L/0NXgAAAAAOgAAAAAIAACAAAABWNTNr8qfK5xqja8p7VTlvcE/VNbuVT16V6+16JKgsrzAAAADRg6NMydooWHcdeSur3WLIhtEbWB8B7rqLMe3FI7GGTbjgnYWEBXcZN4u7Uw9B2SlAAAAAg2xN7oSR9n1q2uUIO8pbiXl2cgIBlj7B0VyLgZrdMe/Qpv6+IUwgY0Sy1V0I+WGrm5J/Y4f28GB/TAgMGnA9yA=="}, "performance_intervention": {"last_daily_sample": "*****************"}, "phoenix": {"user_laf_toggle_state_static": 2}, "policy": {"last_statistics_update": "*****************"}, "profile": {"info_cache": {"Default": {"avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_20", "background_apps": false, "edge_account_cid": "", "edge_account_environment": 0, "edge_account_environment_string": "", "edge_account_first_name": "", "edge_account_last_name": "", "edge_account_oid": "", "edge_account_sovereignty": 0, "edge_account_tenant_id": "", "edge_account_type": 0, "edge_non_signin_profile_type": 1, "edge_profile_can_be_deleted": true, "edge_test_on_premises": false, "edge_wam_aad_for_app_account_type": 0, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_given_name": "", "gaia_id": "", "gaia_name": "", "hosted_domain": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_glic_eligible": false, "is_managed": 0, "is_using_default_avatar": true, "is_using_default_name": true, "managed_user_id": "", "metrics_bucket_index": 1, "name": "Profil 1", "signin.with_credential_provider": false, "user_name": ""}}, "last_active_profiles": ["<PERSON><PERSON><PERSON>"], "metrics": {"next_bucket_index": 2}, "profile_counts_reported": "*****************", "profiles_order": ["<PERSON><PERSON><PERSON>"]}, "profile_network_context_service": {"http_cache_finch_experiment_groups": "None None None None"}, "profiles": {"edge": {"guided_switch_pref": [], "multiple_profiles_with_same_account": false}, "edge_sso_info": {"msa_first_profile_key": "<PERSON><PERSON><PERSON>", "msa_sso_algo_state": 1}, "signin_last_seen_version": "139.0.3405.119", "signin_last_updated_time": **********.331742}, "sentinel_creation_time": "0", "session_id_generator_last_value": "**********", "signin": {"active_accounts_last_emitted": "*****************"}, "startup_boost": {"last_browser_open_time": "*****************"}, "subresource_filter": {"ruleset_version": {"checksum": 0, "content": "", "format": 0}}, "tab_stats": {"discards_external": 0, "discards_proactive": 0, "discards_urgent": 0, "last_daily_sample": "*****************", "max_tabs_per_window": 1, "reloads_external": 0, "reloads_urgent": 0, "total_tab_count_max": 2, "window_count_max": 2}, "telemetry_client": {"cloned_install": {"user_data_dir_id": 1766567}, "governance": {"last_dma_change_date": "*****************", "last_known_cps": 0}, "host_telclient_path": "QzpcUHJvZ3JhbSBGaWxlcyAoeDg2KVxNaWNyb3NvZnRcRWRnZVdlYlZpZXdcQXBwbGljYXRpb25cMTM5LjAuMzQwNS4xMTlcdGVsY2xpZW50LmRsbA==", "sample_id": 3616351}, "uninstall_metrics": {"installation_date2": "**********"}, "updateclientdata": {"apps": {"ahmaebgpfccdhgidjaidaoojjcijckba": {"cohort": "", "cohortname": "", "installdate": -1}, "alpjnmnfbgfkmmpcfpejmmoebdndedno": {"cohort": "", "cohortname": "", "installdate": -1}, "eeobbhfgfagbclfofmgbdfoicabjdbkn": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "fgbafbciocncjfbbonhocjaohoknlaco": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "2025.8.25.1"}, "fppmbhmldokgmleojlplaaodlkibgikh": {"cohort": "", "cohortname": "", "installdate": -1}, "hajigopbbjhghbfimgkfmpenfkclmohk": {"cohort": "", "cohortname": "", "installdate": -1}, "jbfaflocpnkhbgcijpkiafdpbjkedane": {"cohort": "", "cohortname": "", "installdate": -1}, "kpfehajjjbbcifeehjgfgnabifknmdad": {"cohort": "", "cohortname": "", "installdate": -1}, "ldfkbgjbencjpgjfleiooeldhjdapggh": {"cohort": "", "cohortname": "", "installdate": -1}, "mcfjlbnicoclaecapilmleaelokfnijm": {"cohort": "", "cohortname": "", "installdate": -1}, "ndikpojcjlepofdkaaldkinkjbeeebkl": {"cohort": "", "cohortname": "", "installdate": -1}, "oankkpibpaokgecfckkdkgaoafllipag": {"cohort": "", "cohortname": "", "installdate": -1}, "ohckeflnhegojcjlcpbfpciadgikcohk": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "ojblfafjmiikbkepnnolpgbbhejhlcim": {"cohort": "", "cohortname": "", "installdate": -1}, "pghocgajpebopihickglahgebcmkcekh": {"cohort": "", "cohortname": "", "installdate": -1}, "pmagihnlncbcefglppponlgakiphldeh": {"cohort": "", "cohortname": "", "installdate": -1}}}, "updateclientlastupdatecheckerror": 0, "updateclientlastupdatecheckerrorcategory": 0, "updateclientlastupdatecheckerrorextracode1": 0, "user_experience_metrics": {"client_id2": "{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}C:\\Users\\<USER>", "diagnostics": {"last_data_collection_level_on_launch": 1}, "limited_entropy_randomization_source": "9BEA1B99DA61F7D2CA39EB453BAACFC2", "low_entropy_source3": 2176, "machine_id": 7081972, "payload_counter": 1, "pseudo_low_entropy_source": 2877, "reporting_enabled": false, "reset_client_id_deterministic": true, "session_id": 4, "stability": {"browser_last_live_timestamp": "13400884390271552", "exited_cleanly": true, "stats_buildtime": "1756060583", "stats_version": "139.0.3405.119-64", "system_crash_count": 0}}, "variations_compressed_seed": "H4sIAAAAAAAAAJVW227jNhD9lYDPYiDqbhd9cHzZGIm7ri9x0e4ioKWxzEYiBZKyYwT+94KS7NjZaNvmwcgMzzlz4XCgNzTsz1H3DQ1f46xMYPiqQXKa9QXfsHScqDF/FCnqalmChWrvo0gXVKagURdBksKz0nSdATpaaJikUIOM5gPLsvme6XiLl4qmMNwB1wOqqTlMmDKsEVBdSlCo+xfK1ZhXWVyjx3wEkKxp/IK+Hy00aIgsg/lBach7cQxK9bcQv2RM6Rbxj/gBkxBrIQ9jDZJqJvhdJmqFSqqKNeRGZvXk9JKEGQzNplIYuqkNkgVkkIOWBxMU+I8FrWD9xGD/7/yLylZPzoLxw4rxROxnQDOmD3VKbW1rgVutRz2eLIuEaqirzF97BVtspdA6A5OZqm6tNd414UPufZqDpNUVTgRnWkjG0xapCSSMjkTJk+oGPqca+Yf5HY1fUmmgM1CilDGMoD3HNrTR2u+cOeXJWry2kMdKZFRDA4JkvJE0B3U9ETPgCUiQvaLoC64p4yBbG3Yag5r9KdWIr4TMkn5ZjDJB9eE06JUxEQm0yl9AjMyjoMlcU82UZrF6FGnKeDqVrJX/kbCfStbQKsF5Em/r1Nvm3Dx8g6rLgHWv1NspVeoFDmo5roZdzn82VA0HuGZxNQvv7LrvJsBWFEVdSgx9kRdUwgxUIbi62DqfpXdiLmnBhq8F+alkL3mkPP26AylZ8v+E3Ur49+W4bwgFNXOjQRrwG+I0B9RF8ZZyDhmy0I5mpfGM0NE6H0Mh4u3FoWvXf5cYCVpSrnKmq837LPjznkl41iwHUernnGUZUxALnqgLKceomARPtVUrVrXeaXXayzKxfzR79bvVUlEDPalW4PeoExZLocRG365gfSfFXoG8nUqhxbrc3C4fJrf1Wp5KsWEZWLZF/F/+A0lTrW4fRTrXVGrLtrz/QzJXnYGGilc35byLp6KYU3Nc/TbL67MGjYSM4Uw7o63qRaULUHrJ2UbIfMCUlmxdmrm+Z0qLVNK8vZ9mcRXOOY2rG7wcgwQ2tMz0FwP/DH0FLl/yVsnvx+Y5jJhUelby96Ef5oU+fH70Y0sANjJGldoc0hy4rp6yAU96416WoS7a0EyZ+KvV8Nqh4mJTGo9d/Z+WVCYnY2d2q22hiRv4fSHhmmmWKHA9B7ljMagPcWD9x+Ladcd4eu0xBV57zrO0VCZ24z9a6B5oUt3XGxouaIq66BuCYvAwuv9Kdn+O/vb1nYo7m/nDcubfj3eDnTei6ZfRb0mflU+zl1+/IRPvtWBVz9BiW1o3TnTTK9Mbx3b8G8fukk7Xj26+TBZVbSXX8tCv1j/q3yMLmSEuVeOp3vTpy2w8qPMyn3RoimeY2JHjhzYmmBBrigfYc9wowg4O0PXHWo0OiEM8G7vYsyrb9wgJCQ5wp7GdyLEvzr3Idjod7GK/sf1O5IfYxc7ZtkMPuye+54Ud4mAXkwbgEt+LCCY4qG3Hdzuui10c1TYJbN93MMFuY7uuF4bvCRDi+i7B3imAHTnENgk1erbvmYLICW97nQ6JznpB5IUe7pzSCeyAhDjCDqlMz3dDF0c48tHnr6NpcehHgY89HKKPY18DvNC1SYDJuSi/04l8TE5dI8TtEB87fn1JRrITua53bksQ2OFF0oEdhmd2YEeRaVBdgefakXO+D88JPaPioOPxH+ChR3XpCwAA", "variations_config_ids": "{\"ECS\":\"P-R-1082570-1-11,P-D-42388-2-6\",\"EdgeConfig\":\"P-R-1612140-3-4,P-R-1541171-6-9,P-R-1528200-3-4,P-R-1480299-3-5,P-R-1459857-3-2,P-R-1459074-3-9,P-R-1447912-3-12,P-R-1315481-1-6,P-R-1253933-3-8,P-R-1160552-1-3,P-R-1133477-3-4,P-R-1113531-4-9,P-R-1082109-3-6,P-R-1054140-1-4,P-R-1049918-1-3,P-R-68474-9-12,P-R-60617-8-21,P-R-45373-8-85\",\"EdgeFirstRunConfig\":\"P-R-1075865-4-7\",\"Segmentation\":\"P-R-1473016-1-8,P-R-1159985-1-5,P-R-1113915-25-11,P-R-1098334-1-6,P-R-66078-1-3,P-R-66077-1-5,P-R-60882-1-2,P-R-43082-3-5,P-R-42744-1-2\"}", "variations_country": "CH", "variations_crash_streak": 0, "variations_failed_to_fetch_seed_streak": 0, "variations_google_groups": {"Default": []}, "variations_last_fetch_time": "13400882399510938", "variations_last_runtime_fetch_time": "13400882399512418", "variations_permanent_consistency_country": ["139.0.3405.119", "CH"], "variations_runtime_compressed_seed": "H4sIAAAAAAAAAG2Q3W7TQBCF32Vu8Ug7O/triYvKCQoVQiGllApz4TYbY5TYJXZAJfK7V+tN00pweWbO2Z3vHKHo2k1Tv5/1kMOxhHlxVUJewhJXSMJJbQUSEmVLnKGS7BxKNCVkJczXdZh1u6ppL+6Hpmv7V0HPUukY9FkasPVs4kCdBlIZz89PT38ZoZCQzaStYzH5XZJs5CRtkiSkRo3EkzRMRqNCmbKapWKUmLxKCM/Iz0t2Vim06NOWyXmP0qPyfmIk0s6QQonqpNkxuXjZmXp1aIdmF1J1r6jZWSNQop+cV6HehXaoYjcvJmVZUERJYETaexer0idN7Emj1C/NeMccq0kAxgg7nXNW9pw2wjmJhDKRs3AS+bRTMoITyhJGyP7lgPwI893D8PjfTWiru214F6rhsA895N8ghP09fB/HDBahWod9H21Fd2iH/WPRrQPkUCziR5+rGnIoYfi6aeqb21/u4Yt4c/Ph9+ru58e/i9s/TVhuN+5y23+6/NE1h+r6+uJtCTCOT9pv3XidAgAA", "variations_seed_client_version_at_store": "139.0.3405.119", "variations_seed_date": "13400882399000000", "variations_seed_etag": "\"epDKFHO1vZFj5tBsc9fSKUR5HIvDv4FagGFNdCiuVRk=\"", "variations_seed_milestone": 139, "variations_seed_runtime_etag": "\"tXfigWYq8pV0+WLvRbjNzHYwiePlf8JlsQJhoiuaUUA=\"", "variations_seed_signature": "", "was": {"restarted": false}}