^C:\USERS\<USER>\DOCUMENTS\VAAULLT\VAULLT-MASTER\BUILD\WINDOWS\X64\CMAKEFILES\0EC871BACF288BFF6ACB59F4ED164416\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/VaAulLT/vaullt-master/windows -BC:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Documents/VaAulLT/vaullt-master/build/windows/x64/vaullt.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
