import 'package:flutter/material.dart';
import 'vaultwarden_tab.dart';
import 'settings_tab.dart';
import 'devices_tab.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    final List<Widget> screens = [
      const VaultwardenTab(),
      const DevicesTab(),
      const SettingsTab(),
    ];

    return Scaffold(
      body: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
            child: NavigationRail(
              selectedIndex: _selectedIndex,
              onDestinationSelected: (int index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
              labelType: NavigationRailLabelType.none,
              minWidth: 56,
              minExtendedWidth: 80,
              destinations: const [
                NavigationRailDestination(
                  icon: Icon(Icons.shield),
                  selectedIcon: Icon(Icons.shield),
                  label: Text('Vaullt'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.devices),
                  selectedIcon: Icon(Icons.devices),
                  label: Text('Devices'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.settings),
                  selectedIcon: Icon(Icons.settings),
                  label: Text('Settings'),
                ),
              ],
            ),
          ),
          const VerticalDivider(thickness: 1, width: 1),
          Expanded(
            child: IndexedStack(
              index: _selectedIndex,
              children: screens,
            ),
          ),
        ],
      ),
    );
  }
}
